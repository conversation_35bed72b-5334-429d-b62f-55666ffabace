<?php

namespace App\Http\Middleware;

use App\Models\User;
use App\Models\UserAgreement;
use App\Services\LogService;
use App\Enums\LogConstants;
use Illuminate\Support\Facades\Log;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckUserAgreement
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        /** @var User|null $user */
        $user = Auth::user();

        if (!$user) {
            return $next($request);
        }

        // 管理員不需要檢查使用者協議
        if ($user->isAdmin()) {
            return $next($request);
        }

        // 跳過特定路由
        // 這些路由不需要檢查使用者是否同意協議
        $skipRoutes = [
            'user-agreement.show',
            'user-agreement.store',
            'logout',
            'auth.*',
        ];

        foreach ($skipRoutes as $pattern) {
            if ($request->routeIs($pattern)) {
                return $next($request);
            }
        }

        Log::debug('【Middleware】檢查使用者協議', [
            'user_id' => $user->id,
            'route' => $request->route()?->getName(),
            'ip_address' => $request->ip()
        ]);

        if (!UserAgreement::hasAgreed($user)) {
            LogService::operationSuccess(
                '用戶被重定向到使用者協議頁面',
                [
                    'middleware' => 'CheckUserAgreement',
                    'user_id' => $user->id,
                    'user_role' => $user->role,
                    'route' => $request->route()?->getName(),
                    'ip_address' => $request->ip()
                ],
                LogConstants::ACTION_VIEW
            );
            return redirect()->route('user-agreement.show');
        }

        return $next($request);
    }
}
