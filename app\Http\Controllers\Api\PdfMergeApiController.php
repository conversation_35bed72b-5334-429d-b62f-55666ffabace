<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Jobs\ProcessPdfMergeJob;
use App\Models\PdfMergeTask;
use App\Models\RecommendationLetter;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

/**
 * PDF壓縮API控制器
 *
 * 提供外部系統調用的PDF壓縮功能API
 */
class PdfMergeApiController extends Controller
{
    /**
     * 啟動PDF壓縮任務
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function startMerge(Request $request): JsonResponse
    {
        try {
            // 驗證請求參數
            $validator = Validator::make($request->all(), [
              'exam_id' => 'required|string|max:50',
              'exam_year' => 'required|integer|min:100|max:200',
              'test_mode' => 'nullable|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                  'status' => 'error',
                  'message' => '參數驗證失敗',
                  'errors' => $validator->errors()
                ], 422);
            }

            // 準備壓縮參數
            $parameters = array_filter([
              'exam_id' => $request->input('exam_id'),
              'exam_year' => $request->input('exam_year'),
              'test_mode' => $request->input('test_mode'),
              'requested_at' => now()->toISOString(),
              'client_ip' => $request->ip(),
            ]);

            // 測試模式處理
            if (!empty($parameters['test_mode'])) {
                Log::info('[TEST_START]', [
                  'exam_id' => $parameters['exam_id'],
                  'exam_year' => $parameters['exam_year'],
                  'test_mode' => $parameters['test_mode'],
                ]);

                $availableRecommendations = $this->generateTestRecommendations($parameters);
            } else {
                // 檢查是否有符合條件的推薦函
                $availableRecommendations = $this->checkAvailableRecommendations($parameters);
            }

            if ($availableRecommendations['total_count'] === 0) {
                return response()->json([
                  'status' => 'error',
                  'message' => '沒有找到符合條件的已提交推薦函',
                  'details' => [
                    'exam_id' => $parameters['exam_id'],
                    'exam_year' => $parameters['exam_year'],
                    'total_recommendations' => 0,
                    'applicants_with_recommendations' => 0,
                    'test_mode' => $parameters['test_mode'] ?? null
                  ]
                ], 404);
            }

            // 創建壓縮任務
            $task = PdfMergeTask::createTask($parameters);

            // 設定預期的檔案數量
            $task->update([
              'total_files' => $availableRecommendations['applicant_count']
            ]);

            // 派發背景任務
            Log::info('[TASK] PDF壓縮任務執行', [
              'task_id' => $task->task_id,
              'queue' => config('queue.default'),
              'test_mode' => $parameters['test_mode'] ?? null
            ]);

            try {
                /**
                 * 派發 PDF 壓縮任務
                 *
                 * sync 模式下，任務會立即執行並直接修改任務狀態
                 *
                 * 非 sync 模式則會將任務排入 queue 等待 background worker 處理
                 *
                 * *** queue需要在背景運行，否則會直接在當前請求中執行，造成請求延遲 ***
                 */
                ProcessPdfMergeJob::dispatch($task->task_id, $parameters)->onQueue('pdf');

                Log::info('[DISPATCH] PDF壓縮任務已派發', [
                  'task_id' => $task->task_id,
                  'queue_connection' => config('queue.default'),
                  'execution_mode' => config('queue.default') === 'sync' ? 'immediate (sync)' : 'queued',
                ]);
            } catch (\Exception $e) {
                Log::error('[ERROR] PDF壓縮任務執行失敗', [
                  'task_id' => $task->task_id,
                  'error' => $e->getMessage(),
                  'trace' => $e->getTraceAsString(),
                ]);

                // 標記任務失敗
                $task->markAsFailed('Job派發失敗: ' . $e->getMessage());

                return response()->json([
                  'status' => 'error',
                  'message' => 'PDF壓縮任務派發失敗',
                  'details' => [
                    'error' => $e->getMessage()
                  ]
                ], 500);
            }

            Log::info('[START] PDF壓縮任務已啟動', ['task_id' => $task->task_id]);

            return response()->json([
              'status' => 'processing',
              'task_id' => $task->task_id,
              'message' => '壓縮任務已啟動，請使用task_id查詢進度'
            ]);
        } catch (\Exception $e) {
            Log::error('啟動PDF壓縮任務失敗', [
              'error' => $e->getMessage(),
              'request_data' => $request->all(),
              'client_ip' => $request->ip()
            ]);

            return response()->json([
              'status' => 'error',
              'message' => '啟動壓縮任務失敗，請稍後再試'
            ], 500);
        }
    }

    /**
     * 查詢壓縮任務狀態
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getMergeStatus(Request $request): JsonResponse
    {
        Log::info('[REQUEST] 收到任務狀態查詢請求', [
          'query' => $request->query(),
          'input' => $request->all(),
          'ip' => $request->ip(),
        ]);

        if (!$request->has('task_id')) {
            Log::warning('[WARNING] 任務狀態查詢請求未提供 task_id', [
              'query' => $request->query(),
              'ip' => $request->ip(),
            ]);
        }

        try {

            $validator = Validator::make($request->all(), [
              'task_id' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                  'status' => 'error',
                  'message' => '參數驗證失敗',
                  'errors' => $validator->errors()
                ], 422);
            }

            $taskId = $request->input('task_id');
            $task = PdfMergeTask::findByTaskId($taskId);

            if (!$task) {
                Log::warning('[NOT_FOUND] 查無任務', [
                  'task_id' => $taskId,
                  'ip' => $request->ip(),
                ]);

                return response()->json([
                  'status' => 'error',
                  'message' => '找不到指定的任務'
                ], 404);
            }


            // 檢查任務是否過期
            if ($task->isExpired()) {
                $task->update(['status' => PdfMergeTask::STATUS_EXPIRED]);

                return response()->json([
                  'status' => 'expired',
                  'message' => '任務已過期',
                  'task_id' => $taskId
                ]);
            }

            // 根據任務狀態返回不同的響應
            $response = [
              'status' => $task->status,
              'task_id' => $taskId,
              'progress' => $task->progress,
              'created_at' => $task->created_at->toISOString(),
            ];

            switch ($task->status) {
                case PdfMergeTask::STATUS_PROCESSING:
                    $response['message'] = '任務處理中';
                    $response['processed_files'] = $task->processed_files;
                    $response['total_files'] = $task->total_files;
                    break;

                case PdfMergeTask::STATUS_READY:
                    if ($task->isReady()) {
                        $response['message'] = '任務完成，可以下載';
                        $response['download_url'] = $task->download_url;
                        $response['expires_at'] = $task->expires_at?->toISOString();
                    } else {
                        $response['status'] = 'error';
                        $response['message'] = '檔案不存在或已損壞';
                    }
                    break;

                case PdfMergeTask::STATUS_FAILED:
                    $response['message'] = '任務處理失敗';
                    $response['error'] = $task->error_message;
                    break;

                case PdfMergeTask::STATUS_EXPIRED:
                    $response['message'] = '任務已過期';
                    break;

                default:
                    $response['message'] = '未知狀態';
                    break;
            }

            Log::info('[RESPONSE] 回傳任務狀態', [
              'task_id' => $taskId,
              'status' => $response['status'],
              'message' => $response['message'],
              'ip' => $request->ip(),
            ]);

            return response()->json($response);
        } catch (\Exception $e) {
            Log::error('查詢PDF壓縮任務狀態失敗', [
              'error' => $e->getMessage(),
              'task_id' => $request->input('task_id'),
              'client_ip' => $request->ip()
            ]);

            return response()->json([
              'status' => 'error',
              'message' => '查詢任務狀態失敗，請稍後再試'
            ], 500);
        }
    }

    /**
     * 公共下載方法（無需認證）
     *
     * @param string $taskId
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function download(string $taskId, Request $request)
    {
        // 設定 PHP 執行最大時間為 300 秒 (5 分鐘)，避免處理大檔案時超時導致傳輸中斷
        set_time_limit(300);

        try {
            // 根據任務 ID 查找對應的壓縮檔任務資料
            $task = PdfMergeTask::findByTaskId($taskId);

            // 若找不到任務，回傳 404 錯誤
            if (!$task) {
                return response()->json([
                  'status' => 'error',
                  'message' => '找不到指定的任務'
                ], 404);
            }

            // 若任務尚未準備好或已過期，回傳 400 錯誤
            if (!$task->isReady()) {
                return response()->json([
                  'status' => 'error',
                  'message' => '檔案尚未準備好或已過期'
                ], 400);
            }

            // 取得檔案存放的 public 磁碟 (storage/app/public)
            $disk = Storage::disk('public');

            // 確認檔案是否存在，若不存在回傳 404 錯誤
            if (!$disk->exists($task->zip_file_path)) {
                return response()->json([
                  'status' => 'error',
                  'message' => '檔案不存在'
                ], 404);
            }

            // 取得檔案絕對路徑
            $filePath = $disk->path($task->zip_file_path);

            // 紀錄下載行為（方便追蹤與除錯）
            Log::info('公共PDF壓縮檔案下載', [
              'task_id' => $taskId,
              'file_path' => $task->zip_file_path,
              'client_ip' => $request->ip(),
              'user_agent' => $request->userAgent()
            ]);

            // 以串流方式下載檔案，降低記憶體使用並避免一次性載入大檔
            return response()->streamDownload(function () use ($filePath) {
                $stream = fopen($filePath, 'rb');
                while (!feof($stream)) {
                    echo fread($stream, 1024 * 1024);
                    flush(); // 立即送出資料，避免緩衝造成延遲
                }
                fclose($stream);
            }, basename($task->zip_file_path), [
              'Content-Type' => 'application/zip',
              'Content-Disposition' => 'attachment; filename="' . basename($task->zip_file_path) . '"',
              'Cache-Control' => 'no-cache, no-store, must-revalidate',
              'Pragma' => 'no-cache',
              'Expires' => '0',
            ]);
        } catch (\Exception $e) {
            // 若執行過程中發生例外，記錄錯誤並回傳 500 下載失敗錯誤
            Log::error('公共下載PDF壓縮檔案失敗', [
              'error' => $e->getMessage(),
              'task_id' => $taskId,
              'client_ip' => $request->ip()
            ]);

            return response()->json([
              'status' => 'error',
              'message' => '下載失敗'
            ], 500);
        }
    }

    /**
     * 檢查可用的推薦函數量
     *
     * @param array $parameters
     * @return array
     */
    protected function checkAvailableRecommendations(array $parameters): array
    {
        $query = RecommendationLetter::where('status', RecommendationLetter::STATUS_SUBMITTED)
          ->whereNotNull('pdf_path');

        // 根據參數過濾
        if (isset($parameters['exam_id'])) {
            $query->where('exam_id', $parameters['exam_id']);
        }

        if (isset($parameters['exam_year'])) {
            $query->where('exam_year', $parameters['exam_year']);
        }

        // 獲取推薦函並按考生分組
        $recommendations = $query->with(['applicant'])->get();

        $groupedByApplicant = $recommendations->groupBy('applicant_id');

        // 檢查每個考生的PDF檔案是否存在
        $validApplicants = 0;
        $validRecommendations = 0;

        foreach ($groupedByApplicant as $applicantId => $applicantRecommendations) {
            $hasValidPdf = false;

            foreach ($applicantRecommendations as $recommendation) {
                if (Storage::disk('local')->exists($recommendation->pdf_path)) {
                    $validRecommendations++;
                    $hasValidPdf = true;
                }
            }

            if ($hasValidPdf) {
                $validApplicants++;
            }
        }

        return [
          'total_count' => $validRecommendations,
          'applicant_count' => $validApplicants,
          'total_applicants' => count($groupedByApplicant),
          'recommendations_by_applicant' => $groupedByApplicant->map(function ($recs) {
              return count($recs);
          })->toArray()
        ];
    }

    /**
     * 生成測試模式的PDF壓縮數據
     *
     * @return array
     */
    private function generateTestRecommendations(): array
    {
        return [
          'total_count' => 180,
          'applicant_count' => 140,
          'total_applicants' => 140,
          'recommendations_by_applicant' => [
            'applicant_1' => 3,
            'applicant_2' => 2,
            'applicant_3' => 1,
            // ... 其他考生
          ],
          'test_mode' => true
        ];
    }
}
