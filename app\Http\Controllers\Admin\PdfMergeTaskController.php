<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Jobs\ProcessPdfMergeJob;
use App\Models\PdfMergeTask;
use App\Models\RecommendationLetter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

/**
 * PDF合併任務管理控制器
 *
 * 提供管理員後台的任務管理功能
 */
class PdfMergeTaskController extends Controller
{
    /**
     * 顯示任務列表
     */
    public function index(Request $request)
    {

        $query = PdfMergeTask::query();

        // 搜尋過濾
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('task_id', 'like', "%{$search}%")
                    ->orWhereJsonContains('parameters->exam_id', $search)
                    ->orWhereJsonContains('parameters->exam_year', $search);
            });
        }

        $tasks = $query->orderBy('created_at', 'desc')
            ->paginate(20)
            ->withQueryString();

        return Inertia::render('admin/Tasks', [
            'tasks' => $tasks,
            'filters' => $request->only(['status', 'search']),
            'statusOptions' => [
                'processing' => '處理中',
                'ready' => '完成',
                'failed' => '失敗',
                'expired' => '已過期'
            ]
        ]);
    }

    /**
     * 顯示任務詳情
     */
    public function show(PdfMergeTask $task)
    {
        $task->load([]);

        // 獲取相關的推薦函資訊
        $parameters = $task->parameters ?? [];
        $recommendations = [];

        if (isset($parameters['exam_id']) && isset($parameters['exam_year'])) {
            $recommendations = RecommendationLetter::where('exam_id', $parameters['exam_id'])
                ->where('exam_year', $parameters['exam_year'])
                ->where('status', 'submitted')
                ->whereNotNull('pdf_path')
                ->with(['applicant'])
                ->get();
        }

        return Inertia::render('admin/PdfMergeTaskDetail', [
            'task' => $task,
            'recommendations' => $recommendations,
            'canRetry' => in_array($task->status, ['failed', 'expired']),
            'canCancel' => $task->status === 'processing'
        ]);
    }

    /**
     * 重試失敗的任務
     */
    public function retry(PdfMergeTask $task)
    {
        if (!in_array($task->status, ['failed', 'expired'])) {
            return back()->withErrors([
                'message' => '只能重試失敗或過期的任務'
            ]);
        }

        try {
            // 重置任務狀態
            $task->update([
                'status' => PdfMergeTask::STATUS_PROCESSING,
                'progress' => 0,
                'processed_files' => 0,
                'error_message' => null,
                'download_url' => null,
                'zip_file_path' => null,
                'expires_at' => now()->addHours(24)
            ]);

            // 重新派發任務
            ProcessPdfMergeJob::dispatch($task->task_id, $task->parameters);

            Log::info('管理員重試PDF合併任務', [
                'task_id' => $task->task_id,
                'user_id' => Auth::id()
            ]);

            return back()->with('success', '任務已重新開始處理');
        } catch (\Exception $e) {
            Log::error('重試PDF合併任務失敗', [
                'task_id' => $task->task_id,
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return back()->withErrors([
                'message' => '重試任務失敗：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 取消處理中的任務
     */
    public function cancel(PdfMergeTask $task)
    {
        if ($task->status !== 'processing') {
            return back()->withErrors([
                'message' => '只能取消處理中的任務'
            ]);
        }

        try {
            $task->update([
                'status' => PdfMergeTask::STATUS_FAILED,
                'error_message' => '任務被管理員手動取消',
                'progress' => 0
            ]);

            Log::info('管理員取消PDF合併任務', [
                'task_id' => $task->task_id,
                'user_id' => Auth::id()
            ]);

            return back()->with('success', '任務已取消');
        } catch (\Exception $e) {
            Log::error('取消PDF合併任務失敗', [
                'task_id' => $task->task_id,
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return back()->withErrors([
                'message' => '取消任務失敗：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 刪除任務
     */
    public function destroy(PdfMergeTask $task)
    {
        try {
            // 刪除相關檔案
            if ($task->zip_file_path && file_exists(storage_path('app/' . $task->zip_file_path))) {
                unlink(storage_path('app/' . $task->zip_file_path));
            }

            $taskId = $task->task_id;
            $task->delete();

            Log::info('管理員刪除PDF合併任務', [
                'task_id' => $taskId,
                'user_id' => Auth::id()
            ]);

            return redirect()->route('admin.tasks.index')
                ->with('success', '任務已刪除');
        } catch (\Exception $e) {
            Log::error('刪除PDF合併任務失敗', [
                'task_id' => $task->task_id,
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return back()->withErrors([
                'message' => '刪除任務失敗：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 獲取可用的exam_id和exam_year組合
     */
    public function getAvailableExams()
    {
        $exams = RecommendationLetter::where('status', 'submitted')
            ->whereNotNull('pdf_path')
            ->select('exam_id', 'exam_year')
            ->selectRaw('COUNT(*) as recommendation_count')
            ->groupBy('exam_id', 'exam_year')
            ->orderBy('exam_year', 'desc')
            ->orderBy('exam_id')
            ->get();

        return response()->json($exams);
    }

    /**
     * 下載合併後的PDF檔案（管理員專用，無需token）
     */
    public function download(PdfMergeTask $task)
    {
        try {
            if (!$task->isReady()) {
                return back()->withErrors([
                    'message' => '檔案尚未準備好或已過期'
                ]);
            }

            $filePath = storage_path('app/' . $task->zip_file_path);

            if (!file_exists($filePath)) {
                return back()->withErrors([
                    'message' => '檔案不存在'
                ]);
            }

            Log::info('管理員下載PDF合併檔案', [
                'task_id' => $task->task_id,
                'file_path' => $task->zip_file_path,
                'user_id' => Auth::id()
            ]);

            return response()->download($filePath, basename($task->zip_file_path), [
                'Content-Type' => 'application/zip',
                'Content-Disposition' => 'attachment; filename="' . basename($task->zip_file_path) . '"'
            ]);
        } catch (\Exception $e) {
            Log::error('管理員下載PDF合併檔案失敗', [
                'task_id' => $task->task_id,
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return back()->withErrors([
                'message' => '下載檔案失敗：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 查看任務相關日誌
     */
    public function logs(PdfMergeTask $task)
    {
        try {
            // 讀取Laravel日誌檔案
            $logFile = storage_path('logs/laravel.log');
            $logs = [];

            if (file_exists($logFile)) {
                $logContent = file_get_contents($logFile);
                $logLines = explode("\n", $logContent);

                // 過濾包含任務ID的日誌行
                foreach ($logLines as $line) {
                    if (strpos($line, $task->task_id) !== false) {
                        $logs[] = $line;
                    }
                }
            }

            return Inertia::render('admin/PdfMergeTaskLogs', [
                'task' => $task,
                'logs' => array_reverse($logs), // 最新的在前面
                'breadcrumbs' => [
                    ['title' => '儀表板', 'href' => '/dashboard'],
                    ['title' => 'PDF合併任務管理', 'href' => '/admin/tasks'],
                    ['title' => '任務詳情', 'href' => "/admin/tasks/{$task->id}"],
                    ['title' => '處理日誌', 'href' => "/admin/tasks/{$task->id}/logs"],
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('查看PDF合併任務日誌失敗', [
                'task_id' => $task->task_id,
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return back()->withErrors([
                'message' => '查看日誌失敗：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 清理過期任務
     */
    public function cleanup()
    {
        try {
            $cleanedCount = PdfMergeTask::cleanupExpiredTasks();

            Log::info('管理員清理過期PDF合併任務', [
                'cleaned_count' => $cleanedCount,
                'user_id' => Auth::id()
            ]);

            return back()->with('success', "已清理 {$cleanedCount} 個過期任務");
        } catch (\Exception $e) {
            Log::error('清理過期任務失敗', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return back()->withErrors([
                'message' => '清理過期任務失敗：' . $e->getMessage()
            ]);
        }
    }
}
