import { Sidebar, <PERSON>bar<PERSON>ontent, Sidebar<PERSON>ooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/Sidebar';
import { LayoutGrid, BarChart3, Settings, Mail, File, Database, Upload } from 'lucide-react';
import { NavMain } from '@/components/NavMain';
import { NavUser } from '@/components/NavUser';
import { Link } from '@inertiajs/react';
import AppLogo from './AppLogo';

/**
 * 側邊欄組件
 *
 * 此元件僅提供後台使用，包含推薦函管理、日誌管理、其他功能等分組。
 *
 * @returns
 */
export function AppSidebar() {
    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href={route('dashboard')} prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain
                    groups={[
                        {
                            title: '推薦函管理',
                            collapsible: true,
                            items: [
                                {
                                    title: '推薦函列表',
                                    href: route('admin.recommendations.index'),
                                    icon: BarChart3,
                                },
                                {
                                    title: '提交方式管理',
                                    href: route('admin.submission.index'),
                                    icon: Upload,
                                },
                            ],
                        },
                        {
                            title: '系統設定',
                            collapsible: true,
                            items: [
                                {
                                    title: '招生期間管理',
                                    href: route('admin.recruitment-periods.index'),
                                    icon: LayoutGrid,
                                },
                                {
                                    title: '基礎設定',
                                    href: route('admin.system-settings.index'),
                                    icon: Settings,
                                },
                                {
                                    title: '進階設定',
                                    href: route('admin.advanced-settings.index'),
                                    icon: Settings,
                                },
                            ],
                        },
                        {
                            title: '日誌監控',
                            collapsible: true,
                            items: [
                                {
                                    title: '日誌管理',
                                    href: route('admin.logs.index'),
                                    icon: Database,
                                },
                            ],
                        },
                        {
                            title: '任務管理',
                            collapsible: true,
                            items: [
                                {
                                    title: 'PDF合併任務',
                                    href: route('admin.tasks.index'),
                                    icon: File,
                                },
                            ],
                        },
                    ]}
                />
            </SidebarContent>

            <SidebarFooter>
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
