import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { useState, useEffect } from 'react';
import { FileText, Eye, X, AlertCircle } from 'lucide-react';
import { useLanguage } from '@/hooks/useLanguage';

interface PDFPreviewProps {
    file: File | null;
    onRemove?: () => void;
    showPreview?: boolean;
    className?: string;
}

/**
 * PDF 預覽組件
 */
export default function PDFPreview({ file, showPreview = true, className = '' }: PDFPreviewProps) {
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);
    const [isPreviewOpen, setIsPreviewOpen] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const { t } = useLanguage();

    useEffect(() => {
        if (file) {
            // 檢查檔案類型是否為 PDF
            if (file.type !== 'application/pdf') {
                setError('請選擇 PDF 檔案');
                return;
            }

            const maxSize = 10 * 1024 * 1024; // 10MB
            if (file.size > maxSize) {
                setError('檔案大小不能超過 10MB');
                return;
            }

            setError(null);

            // 創建 PDF 預覽 URL，添加編碼參數以改善中文顯示
            const url = URL.createObjectURL(file);
            setPreviewUrl(url);

            return () => {
                URL.revokeObjectURL(url);
            };
        } else {
            setPreviewUrl(null);
            setError(null);
        }
    }, [file]);

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    if (!file) {
        return null;
    }

    return (
        <div className={`space-y-4 ${className}`}>
            {/* 檔案資訊卡片 */}
            <Card className="border-blue-200 bg-blue-50/50">
                <CardContent className="">
                    <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3">
                            <div className="flex-shrink-0">
                                <FileText className="h-8 w-8 text-blue-600" />
                            </div>
                            <div className="min-w-0 flex-1">
                                <p className="truncate text-sm font-medium text-gray-900">{file.name}</p>
                                <div className="mt-1 flex items-center space-x-2">
                                    <Badge variant="secondary" className="text-xs">
                                        PDF
                                    </Badge>
                                    <span className="text-xs text-gray-500">{formatFileSize(file.size)}</span>
                                </div>
                                {error && (
                                    <div className="mt-2 flex items-center space-x-1">
                                        <AlertCircle className="h-4 w-4 text-red-500" />
                                        <span className="text-xs text-red-600">{error}</span>
                                    </div>
                                )}
                            </div>
                        </div>
                        <div className="flex items-center space-x-2">
                            {showPreview && previewUrl && !error && (
                                <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => setIsPreviewOpen(true)}
                                    className="border-blue-200 text-blue-600 hover:bg-blue-50"
                                >
                                    <Eye className="mr-1 h-4 w-4" />
                                    {t('通用.按鈕.預覽')}
                                </Button>
                            )}
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* PDF 預覽模型 */}
            {isPreviewOpen && previewUrl && !error && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
                    <div className="flex h-[90vh] w-full max-w-4xl flex-col rounded-lg bg-white shadow-xl">
                        {/* 模型 Header */}
                        <div className="flex items-center justify-between border-b p-4">
                            <div className="flex items-center space-x-2">
                                <FileText className="h-5 w-5 text-blue-600" />
                                <h3 className="text-lg font-semibold text-gray-900">{t('儀表板.推薦人.推薦函.預覽PDF')}</h3>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Button size="sm" variant="outline" onClick={() => setIsPreviewOpen(false)}>
                                    <X className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>

                        {/* PDF 查看器 */}
                        <div className="flex-1 p-4">
                            <div className="h-full w-full overflow-hidden rounded-lg border">
                                <object data={previewUrl} type="application/pdf" className="h-full w-full" style={{ minHeight: '600px' }}>
                                    <div className="flex h-full items-center justify-center bg-gray-50">
                                        <div className="text-center">
                                            <FileText className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                                        </div>
                                    </div>
                                </object>
                            </div>
                        </div>

                        {/* 模型 Footer */}
                        <div className="border-t bg-gray-50 p-4">
                            <div className="flex items-center justify-between">
                                <div className="text-sm text-gray-600">
                                    {t('儀表板.推薦人.推薦函.檔案名稱')}
                                    {file.name} ({formatFileSize(file.size)})
                                </div>
                                <Button variant="outline" onClick={() => setIsPreviewOpen(false)}>
                                    {t('通用.按鈕.關閉')}
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
