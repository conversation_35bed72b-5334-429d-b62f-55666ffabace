import { AppContent } from '@/components/ui/AppContent';
import { AppHeader } from '@/components/AppHeader';
import { AppFooter } from '@/components/AppFooter';
import { AppShell } from '@/components/AppShell';
import type { PropsWithChildren } from 'react';

/**
 * 前台使用 Layout (包含頁首和頁尾)
 */
export default function AppHeaderFooterLayout({ children }: PropsWithChildren) {
    return (
        <AppShell>
            <AppHeader />
            <AppContent>{children}</AppContent>
            <AppFooter />
        </AppShell>
    );
}
