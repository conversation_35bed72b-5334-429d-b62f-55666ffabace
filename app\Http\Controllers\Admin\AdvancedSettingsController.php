<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SystemSetting;
use App\Services\LogService;
use App\Enums\LogConstants;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

/**
 * 進階系統設定控制器
 *
 * 管理 Log、安全性、上傳、推薦函等進階設定
 */
class AdvancedSettingsController extends Controller
{
    /**
     * 顯示進階設定頁面
     */
    public function index()
    {
        try {
            // 取得所有進階設定
            $settings = [
                // Log 系統設定
                'log' => [
                    'retention_days' => SystemSetting::getLogRetentionDays(),
                    'batch_size' => SystemSetting::getLogBatchSize(),
                    'async_enabled' => SystemSetting::isLogAsyncEnabled(),
                    'email_retry_limit' => SystemSetting::getLogEmailRetryLimit(),
                    'cleanup_enabled' => SystemSetting::isLogCleanupEnabled(),
                ],

                // 安全性設定
                'security' => [
                    'rate_limit_enabled' => SystemSetting::isSecurityRateLimitEnabled(),
                    'rate_limit_attempts' => SystemSetting::getSecurityRateLimitAttempts(),
                    'login_max_attempts' => SystemSetting::getSecurityLoginMaxAttempts(),
                    'login_lockout_time' => SystemSetting::getSecurityLoginLockoutTime(),
                ],

                // 上傳設定
                'upload' => [
                    'max_size' => SystemSetting::getUploadMaxSize(),
                    'allowed_types' => implode(',', SystemSetting::getUploadAllowedTypes()),
                    'backup_enabled' => SystemSetting::isUploadBackupEnabled(),
                ],

                // 推薦函業務設定
                'recommendation' => [
                    'max_letters_per_applicant' => SystemSetting::getRecommendationMaxLettersPerApplicant(),
                    'default_deadline_days' => SystemSetting::getRecommendationDefaultDeadlineDays(),
                ],
            ];

            LogService::operationSuccess(
                '查看進階系統設定頁面',
                ['page' => 'advanced_settings'],
                LogConstants::ACTION_VIEW
            );

            return Inertia::render('admin/AdvancedSettings', [
                'settings' => $settings,
            ]);
        } catch (\Exception $e) {
            LogService::operationFailure(
                '查看進階系統設定頁面失敗',
                ['error' => $e->getMessage()],
                LogConstants::LEVEL_ERROR,
                LogConstants::ACTION_VIEW
            );

            return back()->withErrors([
                'system' => '載入進階設定失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 更新 Log 系統設定
     */
    public function updateLogSettings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'retention_days' => 'required|integer|min:1|max:3650',
            'batch_size' => 'required|integer|min:1|max:1000',
            'async_enabled' => 'required|boolean',
            'email_retry_limit' => 'required|integer|min:1|max:10',
            'cleanup_enabled' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $user = Auth::user();

            // 更新設定
            SystemSetting::set(SystemSetting::LOG_RETENTION_DAYS, $request->retention_days);
            SystemSetting::set(SystemSetting::LOG_CLEANUP_ENABLED, $request->cleanup_enabled ? '1' : '0');

            LogService::securityOperation(
                "管理員 {$user->name} 更新了 Log 系統設定",
                [
                    'retention_days' => $request->retention_days,
                    'batch_size' => $request->batch_size,
                    'async_enabled' => $request->async_enabled,
                    'email_retry_limit' => $request->email_retry_limit,
                    'cleanup_enabled' => $request->cleanup_enabled,
                    'admin_id' => $user->id,
                ]
            );

            return back()->with('success', 'Log 系統設定已更新');
        } catch (\Exception $e) {
            LogService::operationFailure(
                'Log 系統設定更新失敗',
                [
                    'error' => $e->getMessage(),
                    'request_data' => $request->all()
                ],
                LogConstants::LEVEL_ERROR,
                LogConstants::ACTION_UPDATE
            );

            return back()->withErrors([
                'log_settings' => '設定更新失敗，請稍後再試'
            ]);
        }
    }



    /**
     * 更新上傳設定
     */
    public function updateUploadSettings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'max_size' => 'required|integer|min:1|max:102400',
            'allowed_types' => 'required|string|max:255',
            'backup_enabled' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $user = Auth::user();

            // 驗證檔案類型格式
            $allowedTypes = array_map('trim', explode(',', $request->allowed_types));
            $validTypes = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'];

            foreach ($allowedTypes as $type) {
                if (!in_array(strtolower($type), $validTypes)) {
                    return back()->withErrors([
                        'allowed_types' => "不支援的檔案類型: {$type}"
                    ])->withInput();
                }
            }

            // 更新設定
            SystemSetting::set(SystemSetting::UPLOAD_MAX_SIZE, $request->max_size);
            SystemSetting::set(SystemSetting::UPLOAD_ALLOWED_TYPES, $request->allowed_types);

            LogService::securityOperation(
                "管理員 {$user->name} 更新了上傳設定",
                [
                    'max_size' => $request->max_size,
                    'allowed_types' => $request->allowed_types,
                    'backup_enabled' => $request->backup_enabled,
                    'admin_id' => $user->id,
                ]
            );

            return back()->with('success', '上傳設定已更新');
        } catch (\Exception $e) {
            LogService::operationFailure(
                '上傳設定更新失敗',
                [
                    'error' => $e->getMessage(),
                    'request_data' => $request->all()
                ],
                LogConstants::LEVEL_ERROR,
                LogConstants::ACTION_UPDATE
            );

            return back()->withErrors([
                'upload_settings' => '設定更新失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 更新推薦函業務設定
     */
    public function updateRecommendationSettings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'max_letters_per_applicant' => 'required|integer|min:1|max:20',
            'default_deadline_days' => 'required|integer|min:1|max:365',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $user = Auth::user();

            // 更新設定
            SystemSetting::set(SystemSetting::RECOMMENDATION_MAX_LETTERS_PER_APPLICANT, $request->max_letters_per_applicant);

            LogService::securityOperation(
                "管理員 {$user->name} 更新了推薦函業務設定",
                [
                    'max_letters_per_applicant' => $request->max_letters_per_applicant,
                    'default_deadline_days' => $request->default_deadline_days,
                    'admin_id' => $user->id,
                ]
            );

            return back()->with('success', '推薦函業務設定已更新');
        } catch (\Exception $e) {
            LogService::operationFailure(
                '推薦函業務設定更新失敗',
                [
                    'error' => $e->getMessage(),
                    'request_data' => $request->all()
                ],
                LogConstants::LEVEL_ERROR,
                LogConstants::ACTION_UPDATE
            );

            return back()->withErrors([
                'recommendation_settings' => '設定更新失敗，請稍後再試'
            ]);
        }
    }
}
