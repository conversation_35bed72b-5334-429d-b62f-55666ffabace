<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * 考生模型
 *
 * 管理考生的詳細資訊和第三方系統整合
 */
class Applicant extends Model
{
    use HasFactory;

    /**
     * 可批量賦值的屬性
     *
     * @var array<string>
     */
    protected $fillable = [
      'user_id',            // 關聯的使用者 ID
      'exam_id',            // 考試ID
      'exam_year',          // 學年度
      'external_uid',       // 加密保存的考生唯一識別碼
      'ext_autono',         // 報名系統的考生流水號，用於合併PDF時使用
      'phone',              // 聯絡電話
    ];

    /**
     * 取得考生關聯的使用者
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 取得考生的所有推薦函
     *
     * @return HasMany
     */
    public function recommendationLetters(): HasMany
    {
        return $this->hasMany(RecommendationLetter::class);
    }

    /**
     * 取得考生的合併文件
     *
     * @return HasMany
     */
    public function mergedDocuments(): HasMany
    {
        return $this->hasMany(MergedDocument::class);
    }

    /**
     * 取得考生的顯示名稱
     *
     * @return string
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->user->name ?: $this->external_uid ?: '未知考生';
    }

    /**
     * 取得考生唯一識別碼externalUid
     *
     * @return string|null
     */
    public function getStuIdAttribute(): ?string
    {
        return $this->external_uid ?? null;
    }

    /**
     * 根據考試資訊和email查找考生
     *
     * @param string $exam_id 招生類別
     * @param string $exam_year 學年度
     * @param string $externalUid 考生ID(加密)
     * @return static|null
     */
    public static function findByExamInfo(string $exam_id, string $exam_year, string $externalUid): ?static
    {
        return static::where('exam_id', $exam_id)
          ->where('exam_year', $exam_year)
          ->where('external_uid', $externalUid)
          ->first();
    }
}
