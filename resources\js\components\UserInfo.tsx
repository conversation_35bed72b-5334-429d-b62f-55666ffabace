import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { useInitials } from '@/hooks/useInitials';
import { type User } from '@/types';

/**
 * 用戶資訊組件
 *
 * todo 需加入 i18n 支持
 *
 * @param param0
 * @returns
 */
export function UserInfo({ user, showEmail = false, showRole = false }: { user: User; showEmail?: boolean; showRole?: boolean }) {
    const getInitials = useInitials();

    return (
        <>
            <Avatar className="h-8 w-8 overflow-hidden rounded-full">
                <AvatarImage src={typeof user.avatar === 'string' ? user.avatar : undefined} alt={user.name} />
                <AvatarFallback className="rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white">
                    {getInitials(user.name)}
                </AvatarFallback>
            </Avatar>
            <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">{user.name}</span>
                {showEmail && <span className="truncate text-xs text-muted-foreground">{user.email}</span>}
                {showRole && (
                    <span className="truncate text-xs text-muted-foreground">
                        {user.role === 'applicant' ? '考生' : user.role === 'recommender' ? '推薦人' : '管理員'}
                    </span>
                )}
            </div>
        </>
    );
}
