<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

/**
 * 使用者模型
 *
 * 管理系統中所有使用者的基本資訊和認證
 * 支援多種角色：applicant (考生), recommender (推薦人), admin (管理員)
 */
class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory;
    use Notifiable;

    /**
     * 可批量賦值的屬性
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',        // 使用者姓名
        'email',       // 電子郵件
        'role',        // 角色: applicant, recommender, admin
    ];

    /**
     * 序列化時應隱藏的屬性
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * 屬性類型轉換
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * 取得使用者的考生資料
     *
     * @return HasOne
     */
    public function applicant(): HasOne
    {
        return $this->hasOne(Applicant::class);
    }

    /**
     * 取得使用者的推薦人資料
     *
     * @return HasOne
     */
    public function recommender(): HasOne
    {
        return $this->hasOne(Recommender::class);
    }

    /**
     * 檢查使用者是否為考生
     *
     * @return bool
     */
    public function isApplicant(): bool
    {
        return $this->role === 'applicant';
    }

    /**
     * 檢查使用者是否為推薦人
     *
     * @return bool
     */
    public function isRecommender(): bool
    {
        return $this->role === 'recommender';
    }

    /**
     * 檢查使用者是否為管理員
     *
     * @return bool
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * 取得顯示名稱 (根據角色)
     *
     * @return string
     */
    public function getDisplayNameAttribute(): string
    {
        // 如果是考生，則返回"User模型"的name屬性
        if ($this->isApplicant() && $this->applicant) {
            return $this->name;
        }

        // 如果是推薦人，則返回"Recommender模型"的name屬性
        if ($this->isRecommender() && $this->recommender) {
            return $this->recommender->name;
        }

        return $this->name ?? '';
    }

    /**
     * 取得使用者的電話號碼
     *
     * 如果是考生，則返回考生的電話號碼
     * 如果是推薦人，則返回推薦人的電話號碼
     * 否則返回 null
     *
     * @return string|null
     */
    public function getPhoneNumberAttribute(): ?string
    {
        if ($this->isApplicant() && $this->applicant) {
            return $this->applicant->phone;
        }

        if ($this->isRecommender() && $this->recommender) {
            return $this->recommender->phone;
        }

        return null;
    }
}
