<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SystemLog;
use App\Models\EmailLog;
use App\Models\LoginLog;
use App\Services\LogService;
use App\Enums\LogConstants;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

/**
 * 統一日誌管理控制器
 *
 * 整合系統日誌、操作日誌、登入日誌和郵件日誌
 */
class LogsController extends Controller
{
    /**
     * 顯示日誌管理頁面
     */
    public function index(Request $request)
    {
        try {
            $logType = $request->get('type', 'system');
            $page = $request->get('page', 1);
            $perPage = $request->get('per_page', 20);
            $search = $request->get('search');
            $dateFrom = $request->get('date_from');
            $dateTo = $request->get('date_to');

            // 根據日誌類型取得資料
            $logs = $this->getLogsByType($logType, $search, $dateFrom, $dateTo, $page, $perPage);

            // 取得統計資料
            $statistics = $this->getLogStatistics();

            LogService::operationSuccess(
                '查看日誌管理頁面',
                [
                    'log_type' => $logType,
                    'page' => $page,
                    'search' => $search
                ],
                LogConstants::ACTION_VIEW
            );

            return Inertia::render('admin/Logs', [
                'logs' => $logs,
                'statistics' => $statistics,
                'current_type' => $logType,
                'filters' => [
                    'search' => $search,
                    'date_from' => $dateFrom,
                    'date_to' => $dateTo,
                    'page' => $page,
                    'per_page' => $perPage,
                ],
            ]);
        } catch (\Exception $e) {
            LogService::operationFailure(
                '查看日誌管理頁面失敗',
                ['error' => $e->getMessage()],
                LogConstants::LEVEL_ERROR,
                LogConstants::ACTION_VIEW
            );

            return back()->withErrors([
                'system' => '載入日誌失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 查看單一日誌詳情
     */
    public function show(Request $request, $id)
    {
        try {
            $logType = $request->get('type', 'system');
            $log = $this->getLogById($logType, $id);

            if (!$log) {
                return back()->withErrors(['log' => '日誌不存在']);
            }

            LogService::operationSuccess(
                '查看日誌詳情',
                [
                    'log_type' => $logType,
                    'log_id' => $id
                ],
                LogConstants::ACTION_VIEW
            );

            return Inertia::render('admin/LogDetail', [
                'log' => $log,
                'log_type' => $logType,
            ]);
        } catch (\Exception $e) {
            LogService::operationFailure(
                '查看日誌詳情失敗',
                [
                    'error' => $e->getMessage(),
                    'log_id' => $id
                ],
                LogConstants::LEVEL_ERROR,
                LogConstants::ACTION_VIEW
            );

            return back()->withErrors([
                'log' => '載入日誌詳情失敗'
            ]);
        }
    }

    /**
     * 清理日誌
     */
    public function cleanup(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'log_type' => 'required|in:system,email,login,operation',
            'days' => 'required|integer|min:1|max:365',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        try {
            $user = Auth::user();
            $logType = $request->log_type;
            $days = $request->days;

            $deletedCount = $this->cleanupLogsByType($logType, $days);

            LogService::securityOperation(
                "管理員 {$user->name} 清理了 {$logType} 日誌",
                [
                    'log_type' => $logType,
                    'days' => $days,
                    'deleted_count' => $deletedCount,
                    'admin_id' => $user->id,
                ]
            );

            return back()->with('success', "已清理 {$deletedCount} 筆 {$logType} 日誌");
        } catch (\Exception $e) {
            LogService::operationFailure(
                '清理日誌失敗',
                [
                    'error' => $e->getMessage(),
                    'log_type' => $request->log_type,
                    'days' => $request->days
                ],
                LogConstants::LEVEL_ERROR,
                LogConstants::ACTION_DELETE
            );

            return back()->withErrors([
                'cleanup' => '清理日誌失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 匯出日誌
     */
    public function export(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'log_type' => 'required|in:system,email,login,operation',
            'format' => 'required|in:csv,excel',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        try {
            $user = Auth::user();
            $logType = $request->log_type;
            $format = $request->format;

            // 這裡應該實作實際的匯出邏輯
            $filename = $this->exportLogsByType($logType, $format, $request->date_from, $request->date_to);

            LogService::securityOperation(
                "管理員 {$user->name} 匯出了 {$logType} 日誌",
                [
                    'log_type' => $logType,
                    'format' => $format,
                    'date_from' => $request->date_from,
                    'date_to' => $request->date_to,
                    'filename' => $filename,
                    'admin_id' => $user->id,
                ]
            );

            return response()->download(storage_path("app/exports/{$filename}"));
        } catch (\Exception $e) {
            LogService::operationFailure(
                '匯出日誌失敗',
                [
                    'error' => $e->getMessage(),
                    'log_type' => $request->log_type
                ],
                LogConstants::LEVEL_ERROR,
                LogConstants::ACTION_VIEW
            );

            return back()->withErrors([
                'export' => '匯出日誌失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 根據類型取得日誌
     */
    private function getLogsByType($logType, $search = null, $dateFrom = null, $dateTo = null, $page = 1, $perPage = 20)
    {
        switch ($logType) {
            case 'system':
                return $this->getSystemLogs($search, $dateFrom, $dateTo, $page, $perPage);
            case 'email':
                return $this->getEmailLogs($search, $dateFrom, $dateTo, $page, $perPage);
            case 'login':
                return $this->getLoginLogs($search, $dateFrom, $dateTo, $page, $perPage);
            case 'operation':
                return $this->getOperationLogs($search, $dateFrom, $dateTo, $page, $perPage);
            default:
                return collect();
        }
    }

    /**
     * 根據 ID 取得日誌
     */
    private function getLogById($logType, $id)
    {
        switch ($logType) {
            case 'system':
                return SystemLog::find($id);
            case 'email':
                return EmailLog::find($id);
            case 'login':
                return LoginLog::find($id);
            case 'operation':
                return SystemLog::where('operation_type', 'operation')->find($id);
            default:
                return null;
        }
    }

    /**
     * 根據類型清理日誌
     */
    private function cleanupLogsByType($logType, $days)
    {
        $cutoffDate = now()->subDays($days);

        switch ($logType) {
            case 'system':
                return SystemLog::where('created_at', '<', $cutoffDate)->delete();
            case 'email':
                return EmailLog::where('created_at', '<', $cutoffDate)->delete();
            case 'login':
                return LoginLog::where('created_at', '<', $cutoffDate)->delete();
            case 'operation':
                return SystemLog::where('operation_type', 'operation')
                    ->where('created_at', '<', $cutoffDate)
                    ->delete();
            default:
                return 0;
        }
    }

    /**
     * 取得日誌統計
     */
    private function getLogStatistics()
    {
        return [
            'system' => [
                'total' => SystemLog::count(),
                'today' => SystemLog::whereDate('created_at', today())->count(),
                // 'errors' => SystemLog::where('level', 'error')->count(),
            ],
            'email' => [
                'total' => EmailLog::count(),
                'today' => EmailLog::whereDate('created_at', today())->count(),
                'failed' => EmailLog::where('status', 'failed')->count(),
            ],
            'login' => [
                'total' => LoginLog::count(),
                'today' => LoginLog::whereDate('created_at', today())->count(),
                'failed' => LoginLog::where('success', false)->count(),
            ],
        ];
    }

    // 私有方法用於取得不同類型的日誌
    private function getSystemLogs($search, $dateFrom, $dateTo, $page, $perPage)
    {
        $query = SystemLog::query();

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('message', 'like', "%{$search}%")
                    ->orWhere('action', 'like', "%{$search}%");
            });
        }

        if ($dateFrom) {
            $query->whereDate('created_at', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->whereDate('created_at', '<=', $dateTo);
        }

        return $query->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);
    }

    private function getEmailLogs($search, $dateFrom, $dateTo, $page, $perPage)
    {
        $query = EmailLog::query();

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('to_email', 'like', "%{$search}%")
                    ->orWhere('subject', 'like', "%{$search}%");
            });
        }

        if ($dateFrom) {
            $query->whereDate('created_at', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->whereDate('created_at', '<=', $dateTo);
        }

        return $query->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);
    }

    private function getLoginLogs($search, $dateFrom, $dateTo, $page, $perPage)
    {
        $query = LoginLog::query();

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('email', 'like', "%{$search}%")
                    ->orWhere('ip_address', 'like', "%{$search}%");
            });
        }

        if ($dateFrom) {
            $query->whereDate('created_at', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->whereDate('created_at', '<=', $dateTo);
        }

        return $query->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);
    }

    private function getOperationLogs($search, $dateFrom, $dateTo, $page, $perPage)
    {
        $query = SystemLog::where('operation_type', 'operation');

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('message', 'like', "%{$search}%")
                    ->orWhere('action', 'like', "%{$search}%");
            });
        }

        if ($dateFrom) {
            $query->whereDate('created_at', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->whereDate('created_at', '<=', $dateTo);
        }

        return $query->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);
    }

    private function exportLogsByType($logType, $format, $dateFrom = null, $dateTo = null)
    {
        // 這裡應該實作實際的匯出邏輯
        // 暫時返回一個假的檔名
        $timestamp = now()->format('Y-m-d_H-i-s');
        return "{$logType}_logs_{$timestamp}.{$format}";
    }
}
