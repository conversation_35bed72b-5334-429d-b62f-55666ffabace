<?php

namespace App\Console\Commands;

use App\Services\LogService;
use App\Models\OperationLog;
use App\Enums\LogConstants;
use Illuminate\Console\Command;

class LogErrorsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'log:errors 
                            {--hours=24 : 查詢小時數}
                            {--limit=10 : 顯示數量限制}
                            {--level= : 錯誤級別 (error|critical|warning)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '顯示最近的錯誤日誌';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $hours = (int) $this->option('hours');
        $limit = (int) $this->option('limit');
        $level = $this->option('level');

        $this->info("🚨 最近 {$hours} 小時的錯誤日誌 (顯示 {$limit} 筆)");

        $errors = LogService::getRecentErrors($hours, $limit);

        if ($level) {
            $errors = $errors->where('level', $level);
        }

        if ($errors->isEmpty()) {
            $this->info("✅ 沒有發現錯誤記錄");
            return 0;
        }

        $this->info("發現 {$errors->count()} 筆錯誤記錄:");
        $this->line("");

        foreach ($errors as $index => $error) {
            $this->displayError($index + 1, $error);
            $this->line("");
        }

        // 統計錯誤級別
        $this->displayErrorSummary($errors);

        return 0;
    }

    /**
     * 顯示單個錯誤
     */
    private function displayError(int $index, OperationLog $error): void
    {
        $levelColor = $this->getLevelColor($error->level);

        $this->line("#{$index} [{$error->created_at}]");
        $this->line("<{$levelColor}>[{$error->level}]</> {$error->description}");

        if ($error->user_id) {
            $this->line("用戶: {$error->user_id}");
        }

        if ($error->route) {
            $this->line("路由: {$error->route}");
        }

        if ($error->ip_address) {
            $this->line("IP: {$error->ip_address}");
        }

        if (!empty($error->metadata)) {
            $this->line("詳情: " . json_encode($error->metadata, JSON_UNESCAPED_UNICODE));
        }
    }

    /**
     * 顯示錯誤摘要
     */
    private function displayErrorSummary($errors): void
    {
        $this->info("📊 錯誤摘要:");

        $summary = $errors->groupBy('level')->map->count();

        foreach ($summary as $level => $count) {
            $color = $this->getLevelColor($level);
            $this->line("<{$color}>{$level}</>: {$count} 筆");
        }

        // 最常見的錯誤類型
        $commonErrors = $errors->groupBy('type')->map->count()->sortDesc();

        if ($commonErrors->count() > 1) {
            $this->info("\n🔍 錯誤類型分布:");
            foreach ($commonErrors as $type => $count) {
                $this->line("{$type}: {$count} 筆");
            }
        }

        // 受影響的用戶
        $affectedUsers = $errors->whereNotNull('user_id')->pluck('user_id')->unique();
        if ($affectedUsers->count() > 0) {
            $this->info("\n👥 受影響用戶: {$affectedUsers->count()} 人");
        }
    }

    /**
     * 取得級別對應的顏色
     */
    private function getLevelColor(string $level): string
    {
        return match ($level) {
            LogConstants::LEVEL_CRITICAL => 'fg=red;options=bold',
            LogConstants::LEVEL_ERROR => 'fg=red',
            LogConstants::LEVEL_WARNING => 'fg=yellow',
            default => 'fg=white'
        };
    }
}
