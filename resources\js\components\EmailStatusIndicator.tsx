import React from 'react';
import { Badge } from '@/components/ui/Badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/Tooltip';
import { CheckCircle, XCircle, Clock, AlertCircle, Mail } from 'lucide-react';
import { EmailStatusInfo } from '@/types';

interface EmailStatusIndicatorProps {
    emailStatus?: {
        invitation: EmailStatusInfo;
        reminder: EmailStatusInfo;
        notification: EmailStatusInfo;
    };
    type: 'invitation' | 'reminder' | 'notification';
    showDetails?: boolean;
}

const EmailStatusIndicator: React.FC<EmailStatusIndicatorProps> = ({ emailStatus, type, showDetails = false }) => {
    if (!emailStatus) {
        return (
            <Badge variant="secondary" className="text-xs">
                <Mail className="mr-1 h-3 w-3" />
                未知狀態
            </Badge>
        );
    }

    const status = emailStatus[type];

    const getStatusConfig = (statusInfo: EmailStatusInfo) => {
        switch (statusInfo.status) {
            case 'sent':
                return {
                    icon: CheckCircle,
                    variant: 'default' as const,
                    text: '已發送',
                    color: 'text-green-600',
                };
            case 'failed':
                return {
                    icon: XCircle,
                    variant: 'destructive' as const,
                    text: '發送失敗',
                    color: 'text-red-600',
                };
            case 'pending':
                return {
                    icon: Clock,
                    variant: 'secondary' as const,
                    text: '待發送',
                    color: 'text-yellow-600',
                };
            case 'not_sent':
            default:
                return {
                    icon: AlertCircle,
                    variant: 'outline' as const,
                    text: '未發送',
                    color: 'text-gray-500',
                };
        }
    };

    const config = getStatusConfig(status);
    const Icon = config.icon;

    const formatDate = (dateString: string | null) => {
        if (!dateString) return '未發送';
        return new Date(dateString).toLocaleString('zh-TW', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const getTypeLabel = (type: string) => {
        switch (type) {
            case 'invitation':
                return '邀請信';
            case 'reminder':
                return '提醒信';
            case 'notification':
                return '通知信';
            default:
                return type;
        }
    };

    const tooltipContent = (
        <div className="space-y-2">
            <div className="font-medium">{getTypeLabel(type)}</div>
            <div className="space-y-1 text-sm">
                <div>狀態：{status.status_name || config.text}</div>
                <div>發送次數：{status.send_count}</div>
                <div>最後發送：{formatDate(status.last_sent_at)}</div>
                {status.error_message && <div className="text-red-400">錯誤：{status.error_message}</div>}
            </div>
        </div>
    );

    if (showDetails) {
        return (
            <div className="flex items-center space-x-2 rounded-lg border p-2">
                <Icon className={`h-4 w-4 ${config.color}`} />
                <div className="flex-1">
                    <div className="text-sm font-medium">{getTypeLabel(type)}</div>
                    <div className="text-xs text-gray-500">
                        {config.text} • {status.send_count} 次
                    </div>
                    {status.last_sent_at && <div className="text-xs text-gray-400">{formatDate(status.last_sent_at)}</div>}
                    {status.error_message && <div className="mt-1 text-xs text-red-500">{status.error_message}</div>}
                </div>
            </div>
        );
    }

    return (
        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger asChild>
                    <Badge variant={config.variant} className="cursor-help text-xs">
                        <Icon className="mr-1 h-3 w-3" />
                        {config.text}
                        {status.send_count > 0 && <span className="ml-1 text-xs opacity-75">({status.send_count})</span>}
                    </Badge>
                </TooltipTrigger>
                <TooltipContent side="top" className="max-w-xs">
                    {tooltipContent}
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    );
};

export default EmailStatusIndicator;
