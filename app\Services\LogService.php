<?php

namespace App\Services;

use App\Models\OperationLog;
use App\Models\SystemLog;
use App\Models\LoginLog;
use App\Models\EmailLog;
use App\Enums\LogConstants;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Exception;

/**
 * 優化的 Log 服務類別
 *
 * 提供統一的日誌記錄介面，支援批量操作、錯誤處理、異步記錄等功能
 */
class LogService
{
    /**
     * 是否啟用異步記錄
     */
    private static bool $asyncEnabled = false;

    /**
     * 批量記錄緩存
     */
    private static array $batchCache = [];

    #region: 操作日誌(operation_logs)

    /**
     * 記錄操作成功
     *
     * @param string $description 描述
     * @param array $metadata 其他元資料
     * @param string|null $action 操作動作
     * @return OperationLog|null
     */
    public static function operationSuccess(
        string $description,
        array $metadata = [],
        ?string $action = null
    ): ?OperationLog {
        return self::recordOperation(
            LogConstants::OPERATION_TYPE_OPERATION,
            $description,
            $metadata,
            LogConstants::LEVEL_INFO,
            $action
        );
    }

    /**
     * 記錄操作失敗
     *
     * @param string $description 描述
     * @param array $metadata 其他元資料
     * @param string $level 日誌級別
     * @param string|null $action 操作動作
     * @return OperationLog|null
     */
    public static function operationFailure(
        string $description,
        array $metadata = [],
        string $level = LogConstants::LEVEL_ERROR,
        ?string $action = null
    ): ?OperationLog {
        return self::recordOperation(
            LogConstants::OPERATION_TYPE_ERROR,
            $description,
            $metadata,
            $level,
            $action
        );
    }

    /**
     * 記錄安全相關操作
     *
     * @param string $description 描述
     * @param array $metadata 其他元資料
     * @param string $level 日誌級別
     * @return OperationLog|null
     */
    public static function securityOperation(
        string $description,
        array $metadata = [],
        string $level = LogConstants::LEVEL_WARNING
    ): ?OperationLog {
        return self::recordOperation(
            LogConstants::OPERATION_TYPE_SECURITY,
            $description,
            $metadata,
            $level,
            LogConstants::ACTION_VIEW
        );
    }

    /**
     * 統一的操作記錄方法
     */
    private static function recordOperation(
        string $type,
        string $description,
        array $metadata,
        string $level,
        ?string $action
    ): ?OperationLog {
        try {
            $userId = Auth::id();
            $reqInfo = self::getRequestInfo();

            $data = [
              'user_id' => $userId,
              'route' => $reqInfo['route'],
              'ip_address' => $reqInfo['ip'],
              'user_agent' => $reqInfo['ua'],
              'type' => $type,
              'description' => $description,
              'metadata' => array_merge($metadata, [
                'action' => $action,
                'timestamp' => now()->toISOString(),
              ]),
              'level' => $level,
            ];

            if (self::$asyncEnabled) {
                self::addToBatch('operation_logs', $data);
                return null;
            }

            return OperationLog::create($data);
        } catch (Exception $e) {
            Log::error('Failed to record operation log', [
              'error' => $e->getMessage(),
              'description' => $description,
              'type' => $type,
            ]);
            return null;
        }
    }

    #endregion

    #region: 系統日誌(system_logs)

    /**
     * 記錄系統日誌
     *
     * @param array|null $requestPayload 請求資料
     * @param array|null $responsePayload 回應資料
     * @param int|null $statusCode 狀態碼
     * @return SystemLog|null
     */
    public static function system(
        ?array $requestPayload = null,
        ?array $responsePayload = [],
        ?int $statusCode = null
    ): ?SystemLog {
        try {
            $userId = Auth::id();
            $reqInfo = self::getRequestInfo();

            $data = [
              'user_id' => $userId,
              'method' => $reqInfo['method'] ?? 'N/A',
              'route' => $reqInfo['route'] ?? 'N/A',
              'request_payload' => $requestPayload,
              'response_payload' => $responsePayload,
              'status_code' => $statusCode,
              'created_at' => now(),
            ];

            if (self::$asyncEnabled) {
                self::addToBatch('system_logs', $data);
                return null;
            }

            return SystemLog::create($data);
        } catch (Exception $e) {
            Log::error('Failed to record system log', [
              'error' => $e->getMessage(),
              'status_code' => $statusCode,
            ]);
            return null;
        }
    }

    /**
     * 記錄API訪問日誌
     */
    public static function apiAccess(
        array $requestData = [],
        array $responseData = [],
        int $statusCode = 200
    ): ?SystemLog {
        return self::system(
            $requestData,
            $responseData,
            $statusCode
        );
    }

    /**
     * 記錄中間件日誌
     */
    public static function middleware(
        string $middlewareName,
        array $data = [],
        int $statusCode = 200
    ): ?SystemLog {
        return self::system(
            ['middleware' => $middlewareName, 'data' => $data],
            [],
            $statusCode
        );
    }

    /**
     * 記錄指令執行日誌
     */
    public static function command(
        string $commandName,
        array $arguments = [],
        array $result = []
    ): ?SystemLog {
        return self::system(
            ['command' => $commandName, 'arguments' => $arguments],
            $result,
            null
        );
    }

    #endregion

    #region: 登入日誌(login_logs)

    /**
     * 記錄登入成功
     *
     * @param int|null $userId 使用者 ID
     * @return LoginLog|null
     */
    public static function loginSuccess(?int $userId = null): ?LoginLog
    {
        return self::recordLogin(true, null, $userId);
    }

    /**
     * 記錄登入失敗
     *
     * @param string $failureReason 失敗原因
     * @param int|null $userId 使用者 ID
     * @return LoginLog|null
     */
    public static function loginFailure(
        string $failureReason,
        ?int $userId = null
    ): ?LoginLog {
        return self::recordLogin(false, $failureReason, $userId);
    }

    /**
     * 統一的登入記錄方法
     */
    private static function recordLogin(
        bool $success,
        ?string $failureReason = null,
        ?int $userId = null
    ): ?LoginLog {
        try {
            $userId = $userId ?? Auth::id();

            $data = [
              'user_id' => $userId,
              'ip_address' => Request::ip(),
              'user_agent' => Request::userAgent(),
              'success' => $success,
              'failure_reason' => $failureReason,
              'login_at' => now(),
            ];

            if (self::$asyncEnabled) {
                self::addToBatch('login_logs', $data);
                return null;
            }

            return LoginLog::create($data);
        } catch (Exception $e) {
            Log::error('Failed to record login log', [
              'error' => $e->getMessage(),
              'success' => $success,
              'user_id' => $userId,
            ]);
            return null;
        }
    }

    #endregion

    #region: 批量處理和異步支援

    /**
     * 啟用異步記錄模式
     */
    public static function enableAsync(): void
    {
        self::$asyncEnabled = true;
    }

    /**
     * 停用異步記錄模式
     */
    public static function disableAsync(): void
    {
        self::$asyncEnabled = false;
    }

    /**
     * 添加到批量緩存
     */
    private static function addToBatch(string $table, array $data): void
    {
        if (!isset(self::$batchCache[$table])) {
            self::$batchCache[$table] = [];
        }

        self::$batchCache[$table][] = $data;

        // 當緩存達到一定數量時自動刷新
        if (count(self::$batchCache[$table]) >= LogConstants::DEFAULT_BATCH_SIZE) {
            self::flushBatch($table);
        }
    }

    /**
     * 刷新指定表的批量緩存
     */
    public static function flushBatch(?string $table = null): int
    {
        $totalInserted = 0;

        if ($table) {
            $totalInserted += self::insertBatch($table);
        } else {
            foreach (array_keys(self::$batchCache) as $tableName) {
                $totalInserted += self::insertBatch($tableName);
            }
        }

        return $totalInserted;
    }

    /**
     * 執行批量插入
     */
    private static function insertBatch(string $table): int
    {
        if (empty(self::$batchCache[$table])) {
            return 0;
        }

        try {
            $count = count(self::$batchCache[$table]);
            DB::table($table)->insert(self::$batchCache[$table]);
            self::$batchCache[$table] = [];
            return $count;
        } catch (Exception $e) {
            Log::error("Failed to batch insert logs for table: {$table}", [
              'error' => $e->getMessage(),
              'count' => count(self::$batchCache[$table]),
            ]);
            self::$batchCache[$table] = [];
            return 0;
        }
    }

    #endregion

    #region: 通用方法

    /**
     * 取得請求資訊
     */
    private static function getRequestInfo(): array
    {
        $req = request();
        return [
          'route' => $req->route()?->getName() ?? $req->route()?->uri() ?? 'unknown',
          'ip' => $req->ip(),
          'ua' => $req->userAgent(),
          'method' => $req->method(),
        ];
    }

    /**
     * 清理舊日誌
     *
     * @param string $modelClass 日誌 model 類別
     * @param int $daysToKeep 保留天數
     * @return int 刪除數量
     */
    public static function cleanupOldLogs(
        string $modelClass,
        int $daysToKeep = LogConstants::DEFAULT_LOG_RETENTION_DAYS
    ): int {
        try {
            $cutoffDate = now()->subDays($daysToKeep);
            $count = $modelClass::where('created_at', '<', $cutoffDate)->delete();

            Log::info("Cleaned up old logs", [
              'model' => $modelClass,
              'days_kept' => $daysToKeep,
              'deleted_count' => $count,
            ]);

            return $count;
        } catch (Exception $e) {
            Log::error("Failed to cleanup old logs", [
              'model' => $modelClass,
              'error' => $e->getMessage(),
            ]);
            return 0;
        }
    }

    /**
     * 取得日誌統計資料
     *
     * @param string $modelClass 日誌 model 類別
     * @param int $days 查詢天數
     * @return array
     */
    public static function getLogStatistics(string $modelClass, int $days = 30): array
    {
        try {
            $startDate = now()->subDays($days);
            $query = $modelClass::where('created_at', '>=', $startDate);

            $stats = [
              'total_logs' => $query->count(),
              'period_days' => $days,
              'start_date' => $startDate->toDateString(),
              'end_date' => now()->toDateString(),
            ];

            // 如果 model 有 type 欄位
            $fillableFields = (new $modelClass())->getFillable();
            if (in_array('type', $fillableFields)) {
                $stats['by_type'] = $query->selectRaw('type, COUNT(*) as count')
                  ->groupBy('type')
                  ->pluck('count', 'type')
                  ->toArray();
            }

            // 如果 model 有 level 欄位
            if (in_array('level', $fillableFields)) {
                $stats['by_level'] = $query->selectRaw('level, COUNT(*) as count')
                  ->groupBy('level')
                  ->pluck('count', 'level')
                  ->toArray();
            }

            // 如果 model 有 status 欄位 (email_logs)
            if (in_array('status', $fillableFields)) {
                $stats['by_status'] = $query->selectRaw('status, COUNT(*) as count')
                  ->groupBy('status')
                  ->pluck('count', 'status')
                  ->toArray();
            }

            // 每日統計
            $stats['daily_counts'] = $query->selectRaw('DATE(created_at) as date, COUNT(*) as count')
              ->groupBy('date')
              ->orderBy('date')
              ->pluck('count', 'date')
              ->toArray();

            return $stats;
        } catch (Exception $e) {
            Log::error("Failed to get log statistics", [
              'model' => $modelClass,
              'error' => $e->getMessage(),
            ]);
            return [];
        }
    }

    /**
     * 取得綜合統計資料
     */
    public static function getComprehensiveStats(int $days = 30): array
    {
        return [
          'operation_logs' => self::getLogStatistics(OperationLog::class, $days),
          'system_logs' => self::getLogStatistics(SystemLog::class, $days),
          'login_logs' => self::getLogStatistics(LoginLog::class, $days),
          'email_logs' => self::getLogStatistics(EmailLog::class, $days),
        ];
    }

    #endregion

    #region: 郵件日誌輔助方法

    /**
     * 記錄郵件發送成功
     */
    public static function emailSent(
        string $recipientEmail,
        string $subject,
        string $type = LogConstants::EMAIL_TYPE_NOTIFICATION,
        array $metadata = [],
        string $content = ''
    ): ?EmailLog {
        try {
            // 如果沒有提供內容，生成簡短的描述
            $logContent = $content ?: "郵件類型: {$type}, 主旨: {$subject}";

            return EmailLog::create([
              'recommendation_letter_id' => null,
              'recipient_email' => $recipientEmail,
              'subject' => $subject,
              'content' => $logContent,
              'email_type' => $type,
              'status' => LogConstants::EMAIL_STATUS_SENT,
              'sent_at' => now(),
              'metadata' => $metadata,
            ]);
        } catch (Exception $e) {
            Log::error('Failed to record email sent log', [
              'error' => $e->getMessage(),
              'recipient' => $recipientEmail,
            ]);
            return null;
        }
    }

    /**
     * 記錄郵件發送失敗
     */
    public static function emailFailed(
        string $recipientEmail,
        string $subject,
        string $errorMessage,
        string $type = LogConstants::EMAIL_TYPE_NOTIFICATION,
        array $metadata = [],
        string $content = ''
    ): ?EmailLog {
        try {
            // 如果沒有提供內容，生成簡短的描述
            $logContent = $content ?: "郵件類型: {$type}, 主旨: {$subject}, 錯誤: {$errorMessage}";

            return EmailLog::create([
              'recipient_email' => $recipientEmail,
              'subject' => $subject,
              'content' => $logContent,
              'email_type' => $type,
              'status' => LogConstants::EMAIL_STATUS_FAILED,
              'error_message' => $errorMessage,
              'metadata' => $metadata,
            ]);
        } catch (Exception $e) {
            Log::error('Failed to record email failed log', [
              'error' => $e->getMessage(),
              'recipient' => $recipientEmail,
            ]);
            return null;
        }
    }

    #endregion

    #region: 查詢輔助方法

    /**
     * 查詢最近的錯誤日誌
     */
    public static function getRecentErrors(int $hours = 24, int $limit = 50): Collection
    {
        $since = now()->subHours($hours);

        return OperationLog::where('level', LogConstants::LEVEL_ERROR)
          ->orWhere('level', LogConstants::LEVEL_CRITICAL)
          ->where('created_at', '>=', $since)
          ->orderBy('created_at', 'desc')
          ->limit($limit)
          ->get();
    }

    /**
     * 查詢使用者最近活動
     */
    public static function getUserRecentActivity(int $userId, int $days = 7): array
    {
        $since = now()->subDays($days);

        return [
          'operations' => OperationLog::where('user_id', $userId)
            ->where('created_at', '>=', $since)
            ->orderBy('created_at', 'desc')
            ->get(),
          'logins' => LoginLog::where('user_id', $userId)
            ->where('login_at', '>=', $since)
            ->orderBy('login_at', 'desc')
            ->get(),
        ];
    }

    #endregion
}
