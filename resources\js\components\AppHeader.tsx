import { LanguageSwitcher } from './LanguageSwitcher';
import { Link, router } from '@inertiajs/react';
import { useLanguage } from '@/hooks/useLanguage';
import { Button } from '@/components/ui/Button';
import { LogOut } from 'lucide-react';
import AppLogo from './AppLogo';

export function AppHeader() {
    const { t } = useLanguage();

    return (
        <div className="border-b border-sidebar-border/80">
            <div className="mx-auto flex h-16 items-center px-4 md:max-w-7xl">
                {/* 首頁 Logo (靠左) */}
                <Link href={route('dashboard')} prefetch className="flex items-center space-x-2">
                    <AppLogo />
                </Link>

                {/* 功能 (靠右) */}
                <div className="ml-auto flex items-center space-x-2">
                    {/* 語系切換 */}
                    <LanguageSwitcher />

                    {/* 登出按鈕 */}
                    <Button variant="ghost" className="flex items-center space-x-1" onClick={() => router.post(route('logout'))}>
                        <span className="hidden sm:inline">{t('通用.頁首.登出')}</span>
                        <LogOut className="size-4" />
                    </Button>
                </div>
            </div>
        </div>
    );
}
