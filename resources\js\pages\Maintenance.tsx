import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { AlertTriangle, Clock, Settings } from 'lucide-react';

interface MaintenanceProps {
    message?: string;
}

export default function Maintenance({ message = '系統目前正在維護中，請稍後再試' }: MaintenanceProps) {
    return (
        <>
            <Head title="系統維護中" />
            
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
                <div className="w-full max-w-md">
                    <Card className="shadow-lg border-0">
                        <CardHeader className="text-center pb-4">
                            <div className="mx-auto mb-4 w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center">
                                <Settings className="w-8 h-8 text-orange-600 animate-spin" />
                            </div>
                            <CardTitle className="text-2xl font-bold text-gray-900">
                                系統維護中
                            </CardTitle>
                            <CardDescription className="text-gray-600 mt-2">
                                System Under Maintenance
                            </CardDescription>
                        </CardHeader>
                        
                        <CardContent className="text-center space-y-6">
                            <div className="flex items-center justify-center space-x-2 text-orange-600">
                                <AlertTriangle className="w-5 h-5" />
                                <span className="font-medium">暫時無法提供服務</span>
                            </div>
                            
                            <div className="bg-gray-50 rounded-lg p-4">
                                <p className="text-gray-700 leading-relaxed">
                                    {message}
                                </p>
                            </div>
                            
                            <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
                                <Clock className="w-4 h-4" />
                                <span>預計維護時間：約 30 分鐘</span>
                            </div>
                            
                            <div className="border-t pt-4">
                                <p className="text-xs text-gray-400">
                                    如有緊急事務，請聯繫系統管理員
                                </p>
                                <p className="text-xs text-gray-400 mt-1">
                                    感謝您的耐心等候
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <div className="text-center mt-6">
                        <button 
                            onClick={() => window.location.reload()} 
                            className="text-sm text-blue-600 hover:text-blue-800 underline"
                        >
                            重新整理頁面
                        </button>
                    </div>
                </div>
            </div>
        </>
    );
}



