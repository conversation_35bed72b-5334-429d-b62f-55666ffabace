<?php

namespace Tests\Unit;

use App\Services\LogService;
use App\Enums\LogConstants;
use App\Models\OperationLog;
use App\Models\SystemLog;
use App\Models\LoginLog;
use App\Models\EmailLog;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Tests\TestCase;

class LogServiceTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create([
            'role' => 'admin'
        ]);

        Auth::login($this->user);
    }

    /** @test */
    public function it_can_record_operation_success()
    {
        $description = '測試操作成功';
        $metadata = ['test_key' => 'test_value'];
        $action = LogConstants::ACTION_CREATE;

        $log = LogService::operationSuccess($description, $metadata, $action);

        $this->assertInstanceOf(OperationLog::class, $log);
        $this->assertEquals($description, $log->description);
        $this->assertEquals(LogConstants::OPERATION_TYPE_OPERATION, $log->type);
        $this->assertEquals(LogConstants::LEVEL_INFO, $log->level);
        $this->assertEquals($this->user->id, $log->user_id);
        $this->assertArrayHasKey('action', $log->metadata);
        $this->assertEquals($action, $log->metadata['action']);
    }

    /** @test */
    public function it_can_record_operation_failure()
    {
        $description = '測試操作失敗';
        $metadata = ['error' => 'test error'];
        $level = LogConstants::LEVEL_ERROR;

        $log = LogService::operationFailure($description, $metadata, $level);

        $this->assertInstanceOf(OperationLog::class, $log);
        $this->assertEquals($description, $log->description);
        $this->assertEquals(LogConstants::OPERATION_TYPE_ERROR, $log->type);
        $this->assertEquals($level, $log->level);
        $this->assertEquals($this->user->id, $log->user_id);
    }

    /** @test */
    public function it_can_record_security_operation()
    {
        $description = '安全操作測試';
        $metadata = ['ip' => '***********'];

        $log = LogService::securityOperation($description, $metadata);

        $this->assertInstanceOf(OperationLog::class, $log);
        $this->assertEquals($description, $log->description);
        $this->assertEquals(LogConstants::OPERATION_TYPE_SECURITY, $log->type);
        $this->assertEquals(LogConstants::LEVEL_WARNING, $log->level);
    }

    /** @test */
    public function it_can_record_system_log()
    {
        $requestPayload = ['endpoint' => '/api/test'];
        $responsePayload = ['status' => 'success'];
        $statusCode = 200;

        $log = LogService::system($requestPayload, $responsePayload, $statusCode);

        $this->assertInstanceOf(SystemLog::class, $log);
        $this->assertEquals($requestPayload, $log->request_payload);
        $this->assertEquals($responsePayload, $log->response_payload);
        $this->assertEquals($statusCode, $log->status_code);
        $this->assertEquals($this->user->id, $log->user_id);
    }

    /** @test */
    public function it_can_record_api_access()
    {
        $requestData = ['method' => 'GET'];
        $responseData = ['data' => 'test'];
        $statusCode = 200;

        $log = LogService::apiAccess($requestData, $responseData, $statusCode);

        $this->assertInstanceOf(SystemLog::class, $log);
        $this->assertEquals($requestData, $log->request_payload);
        $this->assertEquals($responseData, $log->response_payload);
        $this->assertEquals($statusCode, $log->status_code);
    }

    /** @test */
    public function it_can_record_login_success()
    {
        $log = LogService::loginSuccess($this->user->id);

        $this->assertInstanceOf(LoginLog::class, $log);
        $this->assertTrue($log->success);
        $this->assertEquals($this->user->id, $log->user_id);
        $this->assertNull($log->failure_reason);
    }

    /** @test */
    public function it_can_record_login_failure()
    {
        $failureReason = '密碼錯誤';

        $log = LogService::loginFailure($failureReason, $this->user->id);

        $this->assertInstanceOf(LoginLog::class, $log);
        $this->assertFalse($log->success);
        $this->assertEquals($failureReason, $log->failure_reason);
        $this->assertEquals($this->user->id, $log->user_id);
    }

    /** @test */
    public function it_can_record_email_sent()
    {
        $recipientEmail = '<EMAIL>';
        $subject = '測試郵件';
        $type = LogConstants::EMAIL_TYPE_NOTIFICATION;
        $metadata = ['template' => 'test'];
        $content = '測試郵件內容';

        $log = LogService::emailSent($recipientEmail, $subject, $type, $metadata, $content);

        $this->assertInstanceOf(EmailLog::class, $log);
        $this->assertEquals($recipientEmail, $log->recipient_email);
        $this->assertEquals($subject, $log->subject);
        $this->assertEquals($type, $log->email_type);
        $this->assertEquals(LogConstants::EMAIL_STATUS_SENT, $log->status);
        $this->assertEquals($metadata, $log->metadata);
        $this->assertEquals($content, $log->content);
    }

    /** @test */
    public function it_can_record_email_failed()
    {
        $recipientEmail = '<EMAIL>';
        $subject = '測試郵件';
        $errorMessage = 'SMTP連接失敗';
        $type = LogConstants::EMAIL_TYPE_NOTIFICATION;
        $content = '測試郵件內容';

        $log = LogService::emailFailed($recipientEmail, $subject, $errorMessage, $type, [], $content);

        $this->assertInstanceOf(EmailLog::class, $log);
        $this->assertEquals($recipientEmail, $log->recipient_email);
        $this->assertEquals($subject, $log->subject);
        $this->assertEquals($errorMessage, $log->error_message);
        $this->assertEquals(LogConstants::EMAIL_STATUS_FAILED, $log->status);
        $this->assertEquals($content, $log->content);
    }

    /** @test */
    public function it_can_record_email_with_default_content()
    {
        $recipientEmail = '<EMAIL>';
        $subject = '測試郵件';
        $type = LogConstants::EMAIL_TYPE_INVITATION;

        // 測試不提供content參數時的預設內容
        $log = LogService::emailSent($recipientEmail, $subject, $type);

        $this->assertInstanceOf(EmailLog::class, $log);
        $this->assertEquals($recipientEmail, $log->recipient_email);
        $this->assertEquals($subject, $log->subject);
        $this->assertEquals($type, $log->email_type);
        $this->assertEquals(LogConstants::EMAIL_STATUS_SENT, $log->status);
        $this->assertStringContainsString($type, $log->content);
        $this->assertStringContainsString($subject, $log->content);
    }

    /** @test */
    public function it_can_enable_and_disable_async_mode()
    {
        // 測試啟用異步模式
        LogService::enableAsync();

        $log = LogService::operationSuccess('異步測試');
        $this->assertNull($log); // 異步模式下應該返回null

        // 測試停用異步模式
        LogService::disableAsync();

        $log = LogService::operationSuccess('同步測試');
        $this->assertInstanceOf(OperationLog::class, $log);
    }

    /** @test */
    public function it_can_get_log_statistics()
    {
        // 創建一些測試數據
        OperationLog::factory()->count(5)->create([
            'type' => LogConstants::OPERATION_TYPE_OPERATION,
            'level' => LogConstants::LEVEL_INFO,
        ]);

        OperationLog::factory()->count(3)->create([
            'type' => LogConstants::OPERATION_TYPE_ERROR,
            'level' => LogConstants::LEVEL_ERROR,
        ]);

        $stats = LogService::getLogStatistics(OperationLog::class, 30);

        $this->assertIsArray($stats);
        $this->assertArrayHasKey('total_logs', $stats);
        $this->assertArrayHasKey('by_type', $stats);
        $this->assertArrayHasKey('by_level', $stats);
        $this->assertEquals(8, $stats['total_logs']);
    }

    /** @test */
    public function it_can_get_comprehensive_stats()
    {
        $stats = LogService::getComprehensiveStats(30);

        $this->assertIsArray($stats);
        $this->assertArrayHasKey('operation_logs', $stats);
        $this->assertArrayHasKey('system_logs', $stats);
        $this->assertArrayHasKey('login_logs', $stats);
        $this->assertArrayHasKey('email_logs', $stats);
    }

    /** @test */
    public function it_can_cleanup_old_logs()
    {
        // 創建舊的日誌記錄
        OperationLog::factory()->create([
            'created_at' => now()->subDays(800)
        ]);

        OperationLog::factory()->create([
            'created_at' => now()->subDays(100)
        ]);

        $deletedCount = LogService::cleanupOldLogs(OperationLog::class, 730);

        $this->assertEquals(1, $deletedCount);
        $this->assertEquals(1, OperationLog::count());
    }

    /** @test */
    public function it_can_get_recent_errors()
    {
        OperationLog::factory()->count(3)->create([
            'level' => LogConstants::LEVEL_ERROR,
            'created_at' => now()->subHours(2)
        ]);

        OperationLog::factory()->create([
            'level' => LogConstants::LEVEL_INFO,
            'created_at' => now()->subHours(2)
        ]);

        $errors = LogService::getRecentErrors(24, 10);

        $this->assertCount(3, $errors);
        $errors->each(function ($error) {
            $this->assertEquals(LogConstants::LEVEL_ERROR, $error->level);
        });
    }

    /** @test */
    public function it_can_get_user_recent_activity()
    {
        OperationLog::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'created_at' => now()->subDays(2)
        ]);

        LoginLog::factory()->create([
            'user_id' => $this->user->id,
            'login_at' => now()->subDays(1)
        ]);

        $activity = LogService::getUserRecentActivity($this->user->id, 7);

        $this->assertIsArray($activity);
        $this->assertArrayHasKey('operations', $activity);
        $this->assertArrayHasKey('logins', $activity);
        $this->assertCount(2, $activity['operations']);
        $this->assertCount(1, $activity['logins']);
    }
}
