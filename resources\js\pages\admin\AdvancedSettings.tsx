import React, { useState } from 'react';
import { Head, useForm } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Switch } from '@/components/ui/Switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs';
import { Alert, AlertDescription } from '@/components/ui/Alert';
import { Separator } from '@/components/ui/Separator';
import { Shield, Upload, FileText, Database, AlertTriangle } from 'lucide-react';

interface AdvancedSettingsProps {
    settings: {
        log: {
            retention_days: number;
            batch_size: number;
            async_enabled: boolean;
            email_retry_limit: number;
            cleanup_enabled: boolean;
        };
        security: {
            rate_limit_enabled: boolean;
            rate_limit_attempts: number;
            login_max_attempts: number;
            login_lockout_time: number;
        };
        upload: {
            max_size: number;
            allowed_types: string;
            backup_enabled: boolean;
        };
        recommendation: {
            max_letters_per_applicant: number;
            default_deadline_days: number;
        };
    };
}

/**
 * 進階設定頁面
 */
export default function AdvancedSettings({ settings }: AdvancedSettingsProps) {
    const [activeTab, setActiveTab] = useState('log');

    // Log 設定表單
    const logForm = useForm({
        retention_days: settings.log.retention_days,
        batch_size: settings.log.batch_size,
        async_enabled: settings.log.async_enabled,
        email_retry_limit: settings.log.email_retry_limit,
        cleanup_enabled: settings.log.cleanup_enabled,
    });

    // 安全性設定表單
    const securityForm = useForm({
        rate_limit_enabled: settings.security.rate_limit_enabled,
        rate_limit_attempts: settings.security.rate_limit_attempts,
        login_max_attempts: settings.security.login_max_attempts,
        login_lockout_time: settings.security.login_lockout_time,
    });

    // 上傳設定表單
    const uploadForm = useForm({
        max_size: settings.upload.max_size,
        allowed_types: settings.upload.allowed_types,
        backup_enabled: settings.upload.backup_enabled,
    });

    // 推薦函設定表單
    const recommendationForm = useForm({
        max_letters_per_applicant: settings.recommendation.max_letters_per_applicant,
        default_deadline_days: settings.recommendation.default_deadline_days,
    });

    const handleLogSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        logForm.post(route('admin.advanced-settings.log.update'));
    };

    const handleSecuritySubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // securityForm.post(route('admin.advanced-settings.security.update'));
        console.log(securityForm.data);
    };

    const handleUploadSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        uploadForm.post(route('admin.advanced-settings.upload.update'));
    };

    const handleRecommendationSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        recommendationForm.post(route('admin.advanced-settings.recommendation.update'));
    };

    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: '系統設定', href: '/admin/system-settings' },
    ];

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="進階設定" description="管理系統的進階功能設定，包括日誌、安全性、上傳和業務邏輯等">
            <Head title="進階系統設定" />

            <div className="space-y-6 p-6">
                <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                        <strong>注意：</strong>修改這些設定可能會影響系統效能和安全性，請謹慎操作。
                    </AlertDescription>
                </Alert>

                <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
                    <TabsList className="grid w-full grid-cols-4">
                        <TabsTrigger value="log" className="flex items-center gap-2">
                            <Database className="h-4 w-4" />
                            日誌系統
                        </TabsTrigger>
                        <TabsTrigger value="security" className="flex items-center gap-2">
                            <Shield className="h-4 w-4" />
                            安全性
                        </TabsTrigger>
                        <TabsTrigger value="upload" className="flex items-center gap-2">
                            <Upload className="h-4 w-4" />
                            檔案上傳
                        </TabsTrigger>
                        <TabsTrigger value="recommendation" className="flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            推薦函業務
                        </TabsTrigger>
                    </TabsList>

                    {/* Log 系統設定 */}
                    <TabsContent value="log">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Database className="h-5 w-5" />
                                    日誌系統設定
                                </CardTitle>
                                <CardDescription>管理系統日誌的保留、處理和清理設定</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleLogSubmit} className="space-y-6">
                                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="retention_days">日誌保留天數</Label>
                                            <Input
                                                id="retention_days"
                                                type="number"
                                                min="1"
                                                max="3650"
                                                value={logForm.data.retention_days}
                                                onChange={(e) => logForm.setData('retention_days', parseInt(e.target.value))}
                                                className={logForm.errors.retention_days ? 'border-red-500' : ''}
                                            />
                                            {logForm.errors.retention_days && <p className="text-sm text-red-500">{logForm.errors.retention_days}</p>}
                                            <p className="text-sm text-muted-foreground">超過此天數的日誌將被自動清理 (1-3650 天)</p>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="batch_size">批量處理大小</Label>
                                            <Input
                                                id="batch_size"
                                                type="number"
                                                min="1"
                                                max="1000"
                                                value={logForm.data.batch_size}
                                                onChange={(e) => logForm.setData('batch_size', parseInt(e.target.value))}
                                                className={logForm.errors.batch_size ? 'border-red-500' : ''}
                                            />
                                            {logForm.errors.batch_size && <p className="text-sm text-red-500">{logForm.errors.batch_size}</p>}
                                            <p className="text-sm text-muted-foreground">批量處理日誌的數量 (1-1000)</p>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="email_retry_limit">郵件重試次數</Label>
                                            <Input
                                                id="email_retry_limit"
                                                type="number"
                                                min="1"
                                                max="10"
                                                value={logForm.data.email_retry_limit}
                                                onChange={(e) => logForm.setData('email_retry_limit', parseInt(e.target.value))}
                                                className={logForm.errors.email_retry_limit ? 'border-red-500' : ''}
                                            />
                                            {logForm.errors.email_retry_limit && (
                                                <p className="text-sm text-red-500">{logForm.errors.email_retry_limit}</p>
                                            )}
                                            <p className="text-sm text-muted-foreground">郵件發送失敗時的重試次數 (1-10)</p>
                                        </div>
                                    </div>

                                    <Separator />

                                    <div className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>啟用異步日誌</Label>
                                                <p className="text-sm text-muted-foreground">啟用後日誌將在背景處理，提升效能但可能延遲記錄</p>
                                            </div>
                                            <Switch
                                                checked={logForm.data.async_enabled}
                                                onCheckedChange={(checked) => logForm.setData('async_enabled', checked)}
                                            />
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>啟用自動清理</Label>
                                                <p className="text-sm text-muted-foreground">自動清理超過保留期限的舊日誌</p>
                                            </div>
                                            <Switch
                                                checked={logForm.data.cleanup_enabled}
                                                onCheckedChange={(checked) => logForm.setData('cleanup_enabled', checked)}
                                            />
                                        </div>
                                    </div>

                                    <div className="flex justify-end">
                                        <Button type="submit" disabled={logForm.processing}>
                                            {logForm.processing ? '更新中...' : '更新 Log 設定'}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* 安全性設定 */}
                    <TabsContent value="security">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Shield className="h-5 w-5" />
                                    安全性設定
                                </CardTitle>
                                <CardDescription>管理系統的安全防護機制和存取控制</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSecuritySubmit} className="space-y-6">
                                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="rate_limit_attempts">速率限制次數</Label>
                                            <Input
                                                id="rate_limit_attempts"
                                                type="number"
                                                min="1"
                                                max="1000"
                                                value={securityForm.data.rate_limit_attempts}
                                                onChange={(e) => securityForm.setData('rate_limit_attempts', parseInt(e.target.value))}
                                                className={securityForm.errors.rate_limit_attempts ? 'border-red-500' : ''}
                                            />
                                            {securityForm.errors.rate_limit_attempts && (
                                                <p className="text-sm text-red-500">{securityForm.errors.rate_limit_attempts}</p>
                                            )}
                                            <p className="text-sm text-muted-foreground">每分鐘允許的請求次數 (1-1000)</p>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="login_max_attempts">登入最大嘗試次數</Label>
                                            <Input
                                                id="login_max_attempts"
                                                type="number"
                                                min="1"
                                                max="20"
                                                value={securityForm.data.login_max_attempts}
                                                onChange={(e) => securityForm.setData('login_max_attempts', parseInt(e.target.value))}
                                                className={securityForm.errors.login_max_attempts ? 'border-red-500' : ''}
                                            />
                                            {securityForm.errors.login_max_attempts && (
                                                <p className="text-sm text-red-500">{securityForm.errors.login_max_attempts}</p>
                                            )}
                                            <p className="text-sm text-muted-foreground">登入失敗後鎖定前的最大嘗試次數 (1-20)</p>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="login_lockout_time">登入鎖定時間</Label>
                                            <Input
                                                id="login_lockout_time"
                                                type="number"
                                                min="1"
                                                max="1440"
                                                value={securityForm.data.login_lockout_time}
                                                onChange={(e) => securityForm.setData('login_lockout_time', parseInt(e.target.value))}
                                                className={securityForm.errors.login_lockout_time ? 'border-red-500' : ''}
                                            />
                                            {securityForm.errors.login_lockout_time && (
                                                <p className="text-sm text-red-500">{securityForm.errors.login_lockout_time}</p>
                                            )}
                                            <p className="text-sm text-muted-foreground">登入失敗後的鎖定時間，單位：分鐘 (1-1440)</p>
                                        </div>
                                    </div>

                                    <Separator />

                                    <div className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>啟用速率限制</Label>
                                                <p className="text-sm text-muted-foreground">限制每個 IP 的請求頻率，防止 DDoS 攻擊</p>
                                            </div>
                                            <Switch
                                                checked={securityForm.data.rate_limit_enabled}
                                                onCheckedChange={(checked) => securityForm.setData('rate_limit_enabled', checked)}
                                            />
                                        </div>
                                    </div>

                                    <div className="flex justify-end">
                                        <Button type="submit" disabled={securityForm.processing}>
                                            {securityForm.processing ? '更新中...' : '更新安全性設定'}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* 上傳設定 */}
                    <TabsContent value="upload">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Upload className="h-5 w-5" />
                                    檔案上傳設定
                                </CardTitle>
                                <CardDescription>管理檔案上傳的限制、類型和安全設定</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleUploadSubmit} className="space-y-6">
                                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="max_size">最大檔案大小 (KB)</Label>
                                            <Input
                                                id="max_size"
                                                type="number"
                                                min="1"
                                                max="102400"
                                                value={uploadForm.data.max_size}
                                                onChange={(e) => uploadForm.setData('max_size', parseInt(e.target.value))}
                                                className={uploadForm.errors.max_size ? 'border-red-500' : ''}
                                            />
                                            {uploadForm.errors.max_size && <p className="text-sm text-red-500">{uploadForm.errors.max_size}</p>}
                                            <p className="text-sm text-muted-foreground">單個檔案的最大大小，單位：KB (1-102400，即 100MB)</p>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="allowed_types">允許的檔案類型</Label>
                                            <Input
                                                id="allowed_types"
                                                type="text"
                                                value={uploadForm.data.allowed_types}
                                                onChange={(e) => uploadForm.setData('allowed_types', e.target.value)}
                                                className={uploadForm.errors.allowed_types ? 'border-red-500' : ''}
                                                placeholder="pdf,doc,docx"
                                            />
                                            {uploadForm.errors.allowed_types && (
                                                <p className="text-sm text-red-500">{uploadForm.errors.allowed_types}</p>
                                            )}
                                            <p className="text-sm text-muted-foreground">用逗號分隔，支援：pdf, doc, docx, jpg, jpeg, png, gif</p>
                                        </div>
                                    </div>

                                    <Separator />

                                    <div className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label>啟用檔案備份</Label>
                                                <p className="text-sm text-muted-foreground">自動備份上傳的檔案到備份儲存位置</p>
                                            </div>
                                            <Switch
                                                checked={uploadForm.data.backup_enabled}
                                                onCheckedChange={(checked) => uploadForm.setData('backup_enabled', checked)}
                                            />
                                        </div>
                                    </div>

                                    <div className="flex justify-end">
                                        <Button type="submit" disabled={uploadForm.processing}>
                                            {uploadForm.processing ? '更新中...' : '更新上傳設定'}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* 推薦函業務設定 */}
                    <TabsContent value="recommendation">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <FileText className="h-5 w-5" />
                                    推薦函業務設定
                                </CardTitle>
                                <CardDescription>管理推薦函系統的業務邏輯和限制</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleRecommendationSubmit} className="space-y-6">
                                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="max_letters_per_applicant">每人最多推薦函數量</Label>
                                            <Input
                                                id="max_letters_per_applicant"
                                                type="number"
                                                min="1"
                                                max="20"
                                                value={recommendationForm.data.max_letters_per_applicant}
                                                onChange={(e) => recommendationForm.setData('max_letters_per_applicant', parseInt(e.target.value))}
                                                className={recommendationForm.errors.max_letters_per_applicant ? 'border-red-500' : ''}
                                            />
                                            {recommendationForm.errors.max_letters_per_applicant && (
                                                <p className="text-sm text-red-500">{recommendationForm.errors.max_letters_per_applicant}</p>
                                            )}
                                            <p className="text-sm text-muted-foreground">每位申請人最多可以申請的推薦函數量 (1-20)</p>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="default_deadline_days">預設截止天數</Label>
                                            <Input
                                                id="default_deadline_days"
                                                type="number"
                                                min="1"
                                                max="365"
                                                value={recommendationForm.data.default_deadline_days}
                                                onChange={(e) => recommendationForm.setData('default_deadline_days', parseInt(e.target.value))}
                                                className={recommendationForm.errors.default_deadline_days ? 'border-red-500' : ''}
                                            />
                                            {recommendationForm.errors.default_deadline_days && (
                                                <p className="text-sm text-red-500">{recommendationForm.errors.default_deadline_days}</p>
                                            )}
                                            <p className="text-sm text-muted-foreground">推薦函的預設截止天數，可被個別設定覆蓋 (1-365)</p>
                                        </div>
                                    </div>

                                    <Alert>
                                        <AlertTriangle className="h-4 w-4" />
                                        <AlertDescription>
                                            <strong>注意：</strong>修改這些設定會影響新建立的推薦函申請，現有的申請不會受到影響。
                                        </AlertDescription>
                                    </Alert>

                                    <div className="flex justify-end">
                                        <Button type="submit" disabled={recommendationForm.processing}>
                                            {recommendationForm.processing ? '更新中...' : '更新推薦函設定'}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </AdminLayout>
    );
}
