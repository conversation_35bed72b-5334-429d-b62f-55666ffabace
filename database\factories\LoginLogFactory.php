<?php

namespace Database\Factories;

use App\Models\LoginLog;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LoginLog>
 */
class LoginLogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = LoginLog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $success = $this->faker->boolean(80); // 80% 成功率

        return [
            'user_id' => User::factory(),
            'ip_address' => $this->faker->ipv4(),
            'user_agent' => $this->faker->userAgent(),
            'success' => $success,
            'failure_reason' => $success ? null : $this->faker->randomElement([
                '密碼錯誤',
                '帳號不存在',
                '帳號已被鎖定',
                '登入權杖無效',
                '系統維護中'
            ]),
            'login_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'created_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the login was successful.
     */
    public function successful(): static
    {
        return $this->state(fn (array $attributes) => [
            'success' => true,
            'failure_reason' => null,
        ]);
    }

    /**
     * Indicate that the login failed.
     */
    public function failed(string $reason = '密碼錯誤'): static
    {
        return $this->state(fn (array $attributes) => [
            'success' => false,
            'failure_reason' => $reason,
        ]);
    }

    /**
     * Indicate that the login is from a specific IP.
     */
    public function fromIp(string $ip): static
    {
        return $this->state(fn (array $attributes) => [
            'ip_address' => $ip,
        ]);
    }

    /**
     * Indicate that the login is recent.
     */
    public function recent(int $hours = 24): static
    {
        return $this->state(fn (array $attributes) => [
            'login_at' => $this->faker->dateTimeBetween("-{$hours} hours", 'now'),
            'created_at' => $this->faker->dateTimeBetween("-{$hours} hours", 'now'),
        ]);
    }

    /**
     * Indicate that the login is today.
     */
    public function today(): static
    {
        return $this->state(fn (array $attributes) => [
            'login_at' => $this->faker->dateTimeBetween('today', 'now'),
            'created_at' => $this->faker->dateTimeBetween('today', 'now'),
        ]);
    }

    /**
     * Indicate that the login is for a specific user.
     */
    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Indicate that the login is old.
     */
    public function old(int $days = 30): static
    {
        return $this->state(fn (array $attributes) => [
            'login_at' => $this->faker->dateTimeBetween("-{$days} days", "-1 day"),
            'created_at' => $this->faker->dateTimeBetween("-{$days} days", "-1 day"),
        ]);
    }
}
