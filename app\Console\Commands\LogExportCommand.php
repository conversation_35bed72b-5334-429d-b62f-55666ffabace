<?php

namespace App\Console\Commands;

use App\Models\OperationLog;
use App\Models\SystemLog;
use App\Models\LoginLog;
use App\Models\EmailLog;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class LogExportCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'log:export 
                            {--type=operation : 日誌類型 (operation|system|login|email)}
                            {--days=30 : 匯出天數}
                            {--format=csv : 匯出格式 (csv|json)}
                            {--output= : 輸出檔案路徑}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '匯出日誌資料';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->option('type');
        $days = (int) $this->option('days');
        $format = $this->option('format');
        $output = $this->option('output');

        $this->info("📤 匯出最近 {$days} 天的 {$type} 日誌...");

        $modelClass = $this->getModelClass($type);
        if (!$modelClass) {
            $this->error("❌ 不支援的日誌類型: {$type}");
            return 1;
        }

        // 查詢資料
        $logs = $modelClass::where('created_at', '>=', now()->subDays($days))
            ->orderBy('created_at', 'desc')
            ->get();

        if ($logs->isEmpty()) {
            $this->warn("⚠️  沒有找到符合條件的日誌記錄");
            return 0;
        }

        $this->info("找到 {$logs->count()} 筆記錄");

        // 生成檔案名稱
        if (!$output) {
            $timestamp = now()->format('Y-m-d_H-i-s');
            $output = "logs/{$type}_logs_{$timestamp}.{$format}";
        }

        // 匯出資料
        try {
            if ($format === 'csv') {
                $this->exportToCsv($logs, $output, $type);
            } else {
                $this->exportToJson($logs, $output);
            }

            $fullPath = Storage::path($output);
            $this->info("✅ 匯出完成！");
            $this->line("檔案位置: {$fullPath}");
            $this->line("檔案大小: " . $this->formatBytes(Storage::size($output)));

        } catch (\Exception $e) {
            $this->error("❌ 匯出失敗: " . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * 匯出為 CSV 格式
     */
    private function exportToCsv($logs, string $output, string $type): void
    {
        $headers = $this->getCsvHeaders($type);
        $csvData = [];

        // 添加標題行
        $csvData[] = $headers;

        // 添加資料行
        foreach ($logs as $log) {
            $csvData[] = $this->formatLogForCsv($log, $type);
        }

        // 轉換為 CSV 字串
        $csvContent = '';
        foreach ($csvData as $row) {
            $csvContent .= '"' . implode('","', $row) . '"' . "\n";
        }

        Storage::put($output, $csvContent);
    }

    /**
     * 匯出為 JSON 格式
     */
    private function exportToJson($logs, string $output): void
    {
        $jsonData = [
            'exported_at' => now()->toISOString(),
            'total_records' => $logs->count(),
            'data' => $logs->toArray()
        ];

        Storage::put($output, json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }

    /**
     * 取得 CSV 標題
     */
    private function getCsvHeaders(string $type): array
    {
        $common = ['ID', '建立時間', '更新時間'];

        return match ($type) {
            'operation' => array_merge($common, ['用戶ID', '類型', '描述', '級別', '路由', 'IP位址', '元資料']),
            'system' => array_merge($common, ['用戶ID', '方法', '路由', '狀態碼', '請求資料', '回應資料']),
            'login' => array_merge($common, ['用戶ID', '成功', '失敗原因', '登入時間', 'IP位址', '用戶代理']),
            'email' => array_merge($common, ['推薦函ID', '收件人', '主旨', '類型', '狀態', '發送時間', '錯誤訊息']),
            default => $common
        };
    }

    /**
     * 格式化日誌為 CSV 行
     */
    private function formatLogForCsv($log, string $type): array
    {
        $common = [
            $log->id,
            $log->created_at,
            $log->updated_at
        ];

        $specific = match ($type) {
            'operation' => [
                $log->user_id,
                $log->type,
                $log->description,
                $log->level,
                $log->route,
                $log->ip_address,
                json_encode($log->metadata, JSON_UNESCAPED_UNICODE)
            ],
            'system' => [
                $log->user_id,
                $log->method,
                $log->route,
                $log->status_code,
                json_encode($log->request_payload, JSON_UNESCAPED_UNICODE),
                json_encode($log->response_payload, JSON_UNESCAPED_UNICODE)
            ],
            'login' => [
                $log->user_id,
                $log->success ? '是' : '否',
                $log->failure_reason,
                $log->login_at,
                $log->ip_address,
                $log->user_agent
            ],
            'email' => [
                $log->recommendation_letter_id,
                $log->recipient_email,
                $log->subject,
                $log->email_type,
                $log->status,
                $log->sent_at,
                $log->error_message
            ],
            default => []
        };

        return array_merge($common, $specific);
    }

    /**
     * 取得模型類別
     */
    private function getModelClass(string $type): ?string
    {
        $types = [
            'operation' => OperationLog::class,
            'system' => SystemLog::class,
            'login' => LoginLog::class,
            'email' => EmailLog::class,
        ];

        return $types[$type] ?? null;
    }

    /**
     * 格式化檔案大小
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
