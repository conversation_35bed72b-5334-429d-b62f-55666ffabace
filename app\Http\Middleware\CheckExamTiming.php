<?php

namespace App\Http\Middleware;

use App\Models\SystemSetting;
use App\Services\LogService;
use App\Enums\LogConstants;
use App\Models\User;
use App\Models\Applicant;
use App\Models\Recommender;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

/**
 * 考試時序檢查中間件
 *
 * 檢查考試開放時間，用於控制用戶的操作限制
 */
class CheckExamTiming
{
    /**
     * 處理傳入的請求
     *
     * @param Request $request
     * @param Closure $next
     * @param string ...$permissions 需要檢查的權限
     * @return Response
     */
    public function handle(Request $request, Closure $next, string ...$permissions): Response
    {
        /** @var User|null $user */
        $user = Auth::user();

        // 管理員不受時序限制
        if ($user && $user->isAdmin()) {
            return $next($request);
        }

        // 檢查用戶特定考試期間限制
        if (in_array('user_exam_period', $permissions) && $user) {
            $examPeriodCheck = $this->checkUserExamPeriod($user);

            if (!$examPeriodCheck['can_access']) {
                LogService::operationFailure(
                    $examPeriodCheck['reason'],
                    [
                        'exam_id' => $examPeriodCheck['exam_id'],
                        'exam_year' => $examPeriodCheck['exam_year'],
                        'user_id' => $user->id,
                        'middleware' => 'CheckExamTiming'
                    ],
                    LogConstants::LEVEL_ERROR
                );

                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => '您的考試期間尚未開放或已結束',
                        'error_code' => 'USER_EXAM_PERIOD_CLOSED'
                    ], 403);
                }

                return back()->withErrors([
                    'system' => '您的考試期間尚未開放或已結束'
                ]);
            }
        }

        return $next($request);
    }

    /**
     * 檢查用戶的考試期間是否開放
     *
     * @param User $user
     * @return array
     */
    private function checkUserExamPeriod(User $user): array
    {
        if ($user->isApplicant() && $applicantId = Session::get('applicant_id')) {
            if ($applicant = Applicant::find($applicantId)) {
                return SystemSetting::canUserAccessByExamPeriod($applicant->exam_id, $applicant->exam_year);
            }
        }

        if ($user->isRecommender() && $recommenderId = Session::get('recommender_id')) {
            if ($recommender = Recommender::find($recommenderId)) {
                return SystemSetting::canUserAccessByExamPeriod($recommender->exam_id, $recommender->exam_year);
            }
        }

        return [
            'can_access' => false,
            'reason' => '無法檢查考試期間，找不到對應的考生或推薦人資料',
            'exam_id' => null,
            'exam_year' => null
        ];
    }
}
