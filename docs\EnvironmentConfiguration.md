# 推薦函系統 - 環境配置指南

## 📋 概述

本文檔說明推薦函系統的環境變數配置，包含開發、測試和正式環境的設定指南。

## 🗂️ 配置檔案

### 主要配置檔案

- **`.env`** - 實際使用的環境配置 (需自行建立)
- **`.env.example`** - 主要環境配置"範例" (開發/正式環境)

## 🔧 重要配置說明

### 1. 基本應用程式設定

#### APP_DEBUG (除錯模式)

```bash
APP_DEBUG=true     # 開發/測試環境
APP_DEBUG=false    # 正式環境 (重要！)
```

#### APP_KEY (應用程式金鑰)

```bash
# 自動生成
php artisan key:generate

# 手動設定 (32字元隨機字串)
APP_KEY=base64:your-32-character-secret-key
```

### 2. 資料庫配置

#### 開發環境 (MySQL)

```bash
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=db_name
DB_USERNAME=root
DB_PASSWORD=
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci
```

#### 測試環境 (SQLite)

```bash
DB_CONNECTION=sqlite
DB_DATABASE=:memory:  # 記憶體資料庫
# 或使用檔案資料庫
# DB_DATABASE=database/testing.sqlite
```

### 3. 郵件配置

#### 開發環境 (Log)

```bash
MAIL_MAILER=log  # 郵件寫入日誌檔案
```

#### 正式環境 (EMAIL_API)

```bash
MAIL_EXTERNAL_API_ENABLED=true
MAIL_EXTERNAL_API_URL=your-api-url/sendmail.php
MAIL_EXTERNAL_API_ACCOUNT=your-api-account
MAIL_EXTERNAL_API_PASSWORD=your-api-password
MAIL_EXTERNAL_API_REPLY_TO="${MAIL_FROM_ADDRESS}"
```

### 5. Log 系統配置

#### 開發環境

```bash
LOG_LEVEL=debug
LOG_CLEANUP_ENABLED=true
```

#### 正式環境

```bash
LOG_LEVEL=info        # 或 warning
LOG_CLEANUP_ENABLED=true
```

### 6. 安全性配置

#### 正式環境

```bash
SESSION_SECURE_COOKIE=true   # 僅 HTTPS
```

### 2. IP 白名單

```bash
# 限制 API 存取 IP
EAPAPI_API_IP=*************
EXAM_API_IP=*************
```
