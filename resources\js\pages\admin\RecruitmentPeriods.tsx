import AdminLayout from '@/layouts/AdminLayout';
import { Head, useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/Tabs';
import { Calendar, RefreshCw, CheckCircle, XCircle, Database, Activity, Settings } from 'lucide-react';
import { useState } from 'react';
import { router } from '@inertiajs/react';
import DataTable from '@/components/admin/DataTable';
import StatsCards from '@/components/admin/StatsCards';
import { Label } from '@/components/ui/Label';
import { Input } from '@/components/ui/Input';
import { Separator } from '@radix-ui/react-separator';
import { Switch } from '@/components/ui/Switch';

interface PeriodInfo {
    exists: boolean;
    exam_id: string;
    exam_name?: string;
    start_time?: string;
    end_time?: string;
    status: 'not_started' | 'active' | 'ended' | 'unknown';
    is_active: boolean;
    days_until_start?: number;
    days_until_end?: number;
    synced_at?: string;
    error?: string;
}

interface RecruitmentPeriodsProps {
    periods: PeriodInfo[];
    last_sync_time: string | null;
    needs_resync: boolean;
    active_periods_count: number;
    breadcrumbs: { title: string; href: string }[];
    settings?: {
        sync_exam_ids: string;
        sync_enabled: boolean;
        sync_interval_hours: number;
    };
    recruitment_periods?: Array<{
        exam_id: string;
        exam_name: string;
        app_date1_start: string;
        app_date1_end: string;
        synced_at: string;
    }>;
}

export default function RecruitmentPeriods({
    settings,
    periods,
    last_sync_time,
    needs_resync,
    active_periods_count,
    breadcrumbs,
}: RecruitmentPeriodsProps) {
    const [isLoading, setIsLoading] = useState(false);
    const [activeTab, setActiveTab] = useState('overview');

    // 處理同步
    const handleSync = () => {
        setIsLoading(true);
        router.post(
            route('admin.recruitment-periods.sync'),
            {},
            {
                onFinish: () => setIsLoading(false),
            },
        );
    };

    // 處理清除快取
    const handleClearCache = () => {
        setIsLoading(true);
        router.post(
            route('admin.recruitment-periods.clear-cache'),
            {},
            {
                onFinish: () => setIsLoading(false),
            },
        );
    };

    // 獲取狀態徽章
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'active':
                return <Badge className="bg-green-100 text-green-800">進行中</Badge>;
            case 'not_started':
                return <Badge className="bg-blue-100 text-blue-800">未開始</Badge>;
            case 'ended':
                return <Badge className="bg-gray-100 text-gray-800">已結束</Badge>;
            default:
                return <Badge className="bg-red-100 text-red-800">狀態異常</Badge>;
        }
    };

    // 格式化時間
    const formatDateTime = (dateTime: string | undefined) => {
        if (!dateTime) return '未知';
        return new Date(dateTime).toLocaleString('zh-TW');
    };

    // 統計資料
    const stats = [
        {
            title: '總招生期間',
            value: periods.length,
            icon: Calendar,
            color: 'default' as const,
        },
        {
            title: '進行中',
            value: active_periods_count,
            icon: CheckCircle,
            color: 'green' as const,
        },
        {
            title: '已結束',
            value: periods.filter((p) => p.status === 'ended').length,
            icon: XCircle,
            color: 'default' as const,
        },
    ];

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        form.post(route('admin.recruitment-periods.api-settings'));
    };

    const form = useForm({
        sync_exam_ids: settings?.sync_exam_ids || '',
        sync_enabled: settings?.sync_enabled || false,
        sync_interval_hours: settings?.sync_interval_hours || 24,
    });

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="招生期間管理" description="管理和監控招生期間資料">
            <Head title="招生期間管理" />

            <div className="space-y-6 p-6">
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="overview">總覽</TabsTrigger>
                        <TabsTrigger value="periods">期間列表</TabsTrigger>
                    </TabsList>

                    <TabsContent value="overview" className="space-y-6 pt-4">
                        {/* 統計卡片 */}
                        <StatsCards stats={stats} columns={3} />

                        {/* 同步狀態 */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Settings className="h-5 w-5" />
                                    API 同步設定
                                </CardTitle>
                                <CardDescription>設定要同步的考試代碼和同步間隔</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit} className="space-y-6">
                                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="sync_exam_ids">考試代碼列表</Label>
                                            <Input
                                                id="sync_exam_ids"
                                                type="text"
                                                value={form.data.sync_exam_ids}
                                                onChange={(e) => form.setData('sync_exam_ids', e.target.value)}
                                                className={form.errors.sync_exam_ids ? 'border-red-500' : ''}
                                                placeholder="1,2,D1,D2"
                                            />
                                            {form.errors.sync_exam_ids && <p className="text-sm text-red-500">{form.errors.sync_exam_ids}</p>}
                                            <p className="text-sm text-muted-foreground">用逗號分隔多個考試代碼，只允許英數字和底線</p>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="sync_interval_hours">同步間隔 (小時)</Label>
                                            <Input
                                                id="sync_interval_hours"
                                                type="number"
                                                min="1"
                                                max="168"
                                                value={form.data.sync_interval_hours}
                                                onChange={(e) => form.setData('sync_interval_hours', parseInt(e.target.value))}
                                                className={form.errors.sync_interval_hours ? 'border-red-500' : ''}
                                            />
                                            {form.errors.sync_interval_hours && (
                                                <p className="text-sm text-red-500">{form.errors.sync_interval_hours}</p>
                                            )}
                                            <p className="text-sm text-muted-foreground">自動同步的間隔時間 (1-168 小時)</p>
                                        </div>
                                    </div>

                                    <Separator />

                                    <div className="flex items-center justify-between">
                                        <div className="space-y-0.5">
                                            <Label>啟用 API 同步</Label>
                                            <p className="text-sm text-muted-foreground">啟用後系統將定期同步外部 API 資料</p>
                                        </div>
                                        <Switch
                                            checked={form.data.sync_enabled}
                                            onCheckedChange={(checked) => form.setData('sync_enabled', checked)}
                                        />
                                    </div>

                                    <div className="flex items-center justify-between text-center">
                                        <div className="flex items-center gap-2">
                                            <Button type="button" variant="outline" onClick={handleSync} disabled={isLoading}>
                                                <RefreshCw className="mr-2 h-4 w-4" />
                                                {isLoading ? '同步中...' : '立即同步'}
                                            </Button>
                                            <Button type="button" variant="outline" onClick={handleClearCache} disabled={isLoading}>
                                                <Database className="mr-2 h-4 w-4" />
                                                清除快取
                                            </Button>
                                            {/* 上次同步時間 */}
                                            {last_sync_time ? (
                                                <p className="text-sm text-muted-foreground">上次同步時間: {formatDateTime(last_sync_time)}</p>
                                            ) : (
                                                <p className="text-sm text-muted-foreground">尚未同步過</p>
                                            )}
                                        </div>
                                        <Button type="submit" disabled={form.processing}>
                                            {form.processing ? '更新中...' : '更新設定'}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                        {/* 進行中的招生期間 */}
                        {active_periods_count > 0 && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Activity className="h-5 w-5" />
                                        進行中的招生期間
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {periods
                                            .filter((period) => period.is_active)
                                            .map((period) => (
                                                <div key={period.exam_id} className="flex items-center justify-between rounded-lg border p-3">
                                                    <div>
                                                        <p className="font-medium">{period.exam_name || period.exam_id}</p>
                                                        <p className="text-sm text-gray-500">
                                                            {formatDateTime(period.start_time)} - {formatDateTime(period.end_time)}
                                                        </p>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        {getStatusBadge(period.status)}
                                                        {period.days_until_end && (
                                                            <span className="text-sm text-gray-500">剩餘 {Math.round(period.days_until_end)} 天</span>
                                                        )}
                                                    </div>
                                                </div>
                                            ))}
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </TabsContent>

                    <TabsContent value="periods" className="space-y-6 pt-4">
                        {/* 招生期間列表 */}
                        <DataTable
                            data={periods}
                            title="招生期間列表"
                            description={`共 ${periods.length} 個招生期間`}
                            emptyMessage="沒有招生期間資料"
                            columns={[
                                {
                                    key: 'exam_id',
                                    label: '考試ID',
                                    sortable: true,
                                },
                                {
                                    key: 'exam_name',
                                    label: '考試名稱',
                                    sortable: true,
                                    render: (value, row) => value || row.exam_id,
                                },
                                {
                                    key: 'start_time',
                                    label: '開始時間',
                                    sortable: true,
                                    render: (value) => formatDateTime(value),
                                },
                                {
                                    key: 'end_time',
                                    label: '結束時間',
                                    sortable: true,
                                    render: (value) => formatDateTime(value),
                                },
                                {
                                    key: 'status',
                                    label: '狀態',
                                    sortable: true,
                                    render: (value) => getStatusBadge(value),
                                },
                                {
                                    key: 'synced_at',
                                    label: '同步時間',
                                    sortable: true,
                                    render: (value) => formatDateTime(value),
                                },
                            ]}
                            exportable={true}
                        />
                    </TabsContent>
                </Tabs>
            </div>
        </AdminLayout>
    );
}
