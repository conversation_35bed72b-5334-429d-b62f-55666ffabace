<?php

namespace App\Models;

use App\Enums\LogConstants;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 郵件記錄模型
 *
 * 記錄系統發送的所有郵件，包括邀請信、提醒信等
 */
class EmailLog extends Model
{
    use HasFactory;

    /**
     * 資料表名稱
     */
    protected $table = 'email_logs';

    /**
     * 可批量賦值的屬性
     *
     * @var array<string>
     */
    protected $fillable = [
        'recommendation_letter_id', // 關聯推薦函 ID
        'recipient_email',          // 收件人電子郵件
        'recipient_name',           // 收件人姓名
        'subject',                  // 郵件主旨
        'content',                  // 郵件內容
        'email_type',              // 郵件類型
        'status',                  // 發送狀態
        'error_message',           // 錯誤訊息
        'sent_at',                 // 發送時間
        'retry_count',             // 重試次數
        'metadata',                // 額外資料
    ];

    /**
     * 屬性類型轉換
     *
     * @var array<string, string>
     */
    protected $casts = [
        'sent_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * 郵件類型常數
     */
    public const TYPE_INVITATION = LogConstants::EMAIL_TYPE_INVITATION;       // 邀請信
    public const TYPE_REMINDER = LogConstants::EMAIL_TYPE_REMINDER;           // 提醒信
    public const TYPE_NOTIFICATION = LogConstants::EMAIL_TYPE_NOTIFICATION;   // 通知信
    public const TYPE_ALERT = LogConstants::EMAIL_TYPE_ALERT;                 // 系統異常通知
    public const TYPE_WELCOME = LogConstants::EMAIL_TYPE_WELCOME;             // 歡迎信
    public const TYPE_RESET_PASSWORD = LogConstants::EMAIL_TYPE_RESET_PASSWORD; // 密碼重設

    /**
     * 郵件狀態常數
     */
    public const STATUS_PENDING = LogConstants::EMAIL_STATUS_PENDING; // 待發送(初始狀態)
    public const STATUS_SENT = LogConstants::EMAIL_STATUS_SENT;       // 已發送
    public const STATUS_FAILED = LogConstants::EMAIL_STATUS_FAILED;   // 發送失敗
    public const STATUS_BOUNCED = LogConstants::EMAIL_STATUS_BOUNCED; // 退信

    /**
     * 取得郵件記錄關聯的推薦函
     *
     * @return BelongsTo
     */
    public function recommendationLetter(): BelongsTo
    {
        return $this->belongsTo(RecommendationLetter::class);
    }

    /**
     * 檢查郵件是否已發送
     *
     * @return bool
     */
    public function isSent(): bool
    {
        return $this->status === self::STATUS_SENT;
    }

    /**
     * 檢查郵件是否發送失敗
     *
     * @return bool
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }



    /**
     * 取得郵件類型的中文顯示
     *
     * @return string
     */
    public function getTypeDisplayAttribute(): string
    {
        return match ($this->email_type) {
            self::TYPE_INVITATION => '邀請信',
            self::TYPE_REMINDER => '提醒信',
            self::TYPE_NOTIFICATION => '通知信',
            default => '未知類型',
        };
    }

    /**
     * 取得郵件狀態的中文顯示
     *
     * @return string
     */
    public function getStatusDisplayAttribute(): string
    {
        return match ($this->status) {
            self::STATUS_PENDING => '待發送',
            self::STATUS_SENT => '已發送',
            self::STATUS_FAILED => '發送失敗',
            self::STATUS_BOUNCED => '退信',
            default => '未知狀態',
        };
    }

    /**
     * 記錄邀請郵件
     */
    public static function logInvitation(int $recommendationLetterId, string $email, string $name, string $subject, string $content = ''): EmailLog
    {
        return self::create([
            'recommendation_letter_id' => $recommendationLetterId,
            'recipient_email' => $email,
            'recipient_name' => $name,
            'email_type' => self::TYPE_INVITATION,
            'subject' => $subject,
            'content' => $content ?: '邀請郵件內容',
            'status' => self::STATUS_SENT,
            'sent_at' => now(),
        ]);
    }

    /**
     * 記錄提醒郵件
     */
    public static function logReminder(int $recommendationLetterId, string $email, string $name, string $subject, string $content = ''): EmailLog
    {
        return self::create([
            'recommendation_letter_id' => $recommendationLetterId,
            'recipient_email' => $email,
            'recipient_name' => $name,
            'email_type' => self::TYPE_REMINDER,
            'subject' => $subject,
            'content' => $content ?: '提醒郵件內容',
            'status' => self::STATUS_SENT,
            'sent_at' => now(),
        ]);
    }

    /**
     * 記錄通知郵件
     */
    public static function logNotification(string $email, string $name, string $subject, string $content = '', string $type = self::TYPE_NOTIFICATION): EmailLog
    {
        return self::create([
            'recipient_email' => $email,
            'recipient_name' => $name,
            'email_type' => $type,
            'subject' => $subject,
            'content' => $content ?: '通知郵件內容',
            'status' => self::STATUS_SENT,
            'sent_at' => now(),
        ]);
    }

    #region: 查詢範圍 (Scopes)

    /**
     * 範圍查詢：按狀態
     */
    public function scopeOfStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 範圍查詢：按類型
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('email_type', $type);
    }

    /**
     * 範圍查詢：已發送
     */
    public function scopeSent($query)
    {
        return $query->where('status', self::STATUS_SENT);
    }

    /**
     * 範圍查詢：發送失敗
     */
    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    /**
     * 範圍查詢：待發送
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * 範圍查詢：可重試的失敗郵件
     */
    public function scopeRetryable($query)
    {
        return $query->where('status', self::STATUS_FAILED)
            ->where('retry_count', '<', LogConstants::DEFAULT_EMAIL_RETRY_LIMIT);
    }

    /**
     * 範圍查詢：最近的記錄
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * 範圍查詢：按收件人
     */
    public function scopeForRecipient($query, string $email)
    {
        return $query->where('recipient_email', $email);
    }

    #endregion

    #region: 統計方法

    /**
     * 獲取基本統計數據
     */
    public static function getStats(int $days = 30): array
    {
        $query = self::where('created_at', '>=', now()->subDays($days));

        return [
            'total' => $query->count(),
            'sent' => $query->where('status', self::STATUS_SENT)->count(),
            'failed' => $query->where('status', self::STATUS_FAILED)->count(),
            'pending' => $query->where('status', self::STATUS_PENDING)->count(),
            'bounced' => $query->where('status', self::STATUS_BOUNCED)->count(),
            'success_rate' => self::getSuccessRate($days),
        ];
    }

    /**
     * 取得成功率
     */
    public static function getSuccessRate(int $days = 30): float
    {
        $total = self::where('created_at', '>=', now()->subDays($days))->count();
        $sent = self::where('created_at', '>=', now()->subDays($days))
            ->where('status', self::STATUS_SENT)
            ->count();

        return $total > 0 ? round(($sent / $total) * 100, 2) : 0;
    }

    /**
     * 取得類型統計
     */
    public static function getTypeStats(int $days = 30): array
    {
        return self::where('created_at', '>=', now()->subDays($days))
            ->selectRaw(
                'email_type,
                        COUNT(*) as total,
                        SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as sent,
                        SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as failed',
                [self::STATUS_SENT, self::STATUS_FAILED]
            )
            ->groupBy('email_type')
            ->get()
            ->keyBy('email_type')
            ->toArray();
    }

    /**
     * 取得每日發送統計
     */
    public static function getDailyStats(int $days = 30): array
    {
        return self::where('created_at', '>=', now()->subDays($days))
            ->selectRaw(
                'DATE(created_at) as date,
                        COUNT(*) as total,
                        SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as sent,
                        SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as failed',
                [self::STATUS_SENT, self::STATUS_FAILED]
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date')
            ->toArray();
    }

    /**
     * 取得重試統計
     */
    public static function getRetryStats(int $days = 30): array
    {
        return self::where('created_at', '>=', now()->subDays($days))
            ->selectRaw('retry_count, COUNT(*) as count')
            ->groupBy('retry_count')
            ->orderBy('retry_count')
            ->pluck('count', 'retry_count')
            ->toArray();
    }

    /**
     * 取得失敗原因統計
     */
    public static function getFailureReasonStats(int $days = 30): array
    {
        return self::failed()
            ->where('created_at', '>=', now()->subDays($days))
            ->whereNotNull('error_message')
            ->selectRaw('error_message, COUNT(*) as count')
            ->groupBy('error_message')
            ->orderByDesc('count')
            ->limit(10)
            ->pluck('count', 'error_message')
            ->toArray();
    }

    /**
     * 取得收件人統計
     */
    public static function getRecipientStats(int $days = 30, int $limit = 20): array
    {
        return self::where('created_at', '>=', now()->subDays($days))
            ->selectRaw(
                'recipient_email,
                        COUNT(*) as total,
                        SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as sent,
                        SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as failed',
                [self::STATUS_SENT, self::STATUS_FAILED]
            )
            ->groupBy('recipient_email')
            ->orderByDesc('total')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    #endregion

    #region: 輔助方法

    /**
     * 檢查是否可以重試
     */
    public function canRetry(): bool
    {
        return $this->status === self::STATUS_FAILED &&
            $this->retry_count < LogConstants::DEFAULT_EMAIL_RETRY_LIMIT;
    }

    /**
     * 增加重試次數
     */
    public function incrementRetryCount(): bool
    {
        return $this->increment('retry_count');
    }

    /**
     * 標記為已發送
     */
    public function markAsSent(): bool
    {
        return $this->update([
            'status' => self::STATUS_SENT,
            'sent_at' => now(),
            'error_message' => null,
        ]);
    }

    /**
     * 標記為發送失敗
     */
    public function markAsFailed(string $errorMessage): bool
    {
        return $this->update([
            'status' => self::STATUS_FAILED,
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * 取得狀態文字
     */
    public function getStatusTextAttribute(): string
    {
        return LogConstants::getEmailStatuses()[$this->status] ?? $this->status;
    }

    /**
     * 取得類型文字
     */
    public function getTypeTextAttribute(): string
    {
        return LogConstants::getEmailTypes()[$this->email_type] ?? $this->email_type;
    }

    /**
     * 檢查是否為高優先級郵件
     */
    public function isHighPriority(): bool
    {
        return in_array($this->email_type, [
            self::TYPE_ALERT,
            self::TYPE_RESET_PASSWORD,
        ]);
    }

    #endregion
}
