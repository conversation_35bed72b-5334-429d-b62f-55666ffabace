import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { useLanguage } from '@/hooks/useLanguage';
import { LucideIcon } from 'lucide-react';

interface EmptyStateProps {
    icon?: LucideIcon;
    title: string;
    description?: string;
    action?: {
        label: string;
        onClick: () => void;
        variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
    };
    className?: string;
    size?: 'sm' | 'md' | 'lg';
}

// 通用空狀態結構與UI樣式
export default function EmptyState({ icon: Icon, title, description, action, className = '', size = 'md' }: EmptyStateProps) {
    const sizeClasses = {
        sm: {
            container: 'p-2',
            icon: 'h-8 w-8',
            title: 'text-base',
            description: 'text-sm',
            spacing: 'space-y-3',
        },
        md: {
            container: 'p-2',
            icon: 'h-12 w-12',
            title: 'text-lg',
            description: 'text-base',
            spacing: 'space-y-4',
        },
        lg: {
            container: 'p-2',
            icon: 'h-16 w-16',
            title: 'text-xl',
            description: 'text-lg',
            spacing: 'space-y-6',
        },
    };

    const classes = sizeClasses[size];

    return (
        <Card className={`border-2 border-dashed border-gray-300 ${className}`}>
            <CardContent className={`text-center ${classes.container}`}>
                <div className={classes.spacing}>
                    {Icon && (
                        <div className="flex justify-center">
                            <Icon className={`${classes.icon} text-gray-400`} />
                        </div>
                    )}

                    <div>
                        <h3 className={`font-semibold text-gray-900 ${classes.title}`}>{title}</h3>
                        {description && <p className={`mt-2 text-gray-600 ${classes.description}`}>{description}</p>}
                    </div>

                    {action && (
                        <div className="flex justify-center">
                            <Button onClick={action.onClick} variant={action.variant || 'default'} className="mt-2">
                                {action.label}
                            </Button>
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}

// Predefined empty states for common scenarios
export const NoDataEmptyState = ({
    title = '暫無資料',
    description = '目前沒有任何資料可顯示',
    className = '',
}: {
    title?: string;
    description?: string;
    className?: string;
}) => <EmptyState title={title} description={description} className={className} size="md" />;

// 推薦函為空
export const NoRecommendationsEmptyState = ({
    onCreateNew,
    userRole = 'applicant',
}: {
    onCreateNew?: () => void;
    userRole?: 'applicant' | 'recommender';
}) => {
    const { t } = useLanguage();
    const content = {
        applicant: {
            title: t('儀表板.推薦人.空狀態.標題'),
            description: t('儀表板.推薦人.空狀態.說明'),
            actionLabel: t('儀表板.推薦人.空狀態.按鈕'),
        },
        recommender: {
            title: t('儀表板.考生.空狀態.標題'),
            description: t('儀表板.考生.空狀態.說明'),
            actionLabel: t('儀表板.考生.空狀態.按鈕'),
        },
    };

    const config = content[userRole];

    return (
        <EmptyState
            title={config.title}
            description={config.description}
            action={
                onCreateNew && config.actionLabel
                    ? {
                          label: config.actionLabel,
                          onClick: onCreateNew,
                      }
                    : undefined
            }
            size="md"
        />
    );
};

// 問卷模板為空
export const NoTemplatesEmptyState = ({ onCreateNew }: { onCreateNew?: () => void }) => (
    <EmptyState
        title="尚無問卷模板"
        description="開始創建您的第一個問卷模板"
        action={
            onCreateNew
                ? {
                      label: '新增模板',
                      onClick: onCreateNew,
                  }
                : undefined
        }
        size="md"
    />
);

// 搜尋結果為空
export const SearchEmptyState = ({ searchTerm, onClearSearch }: { searchTerm: string; onClearSearch?: () => void }) => (
    <EmptyState
        title="找不到符合條件的結果"
        description={`沒有找到包含「${searchTerm}」的項目`}
        action={
            onClearSearch
                ? {
                      label: '清除搜尋',
                      onClick: onClearSearch,
                      variant: 'outline',
                  }
                : undefined
        }
        size="sm"
    />
);

// 錯誤或載入失敗的空狀態
export const ErrorEmptyState = ({
    title = '載入失敗',
    description = '無法載入資料，請稍後再試',
    onRetry,
}: {
    title?: string;
    description?: string;
    onRetry?: () => void;
}) => (
    <EmptyState
        title={title}
        description={description}
        action={
            onRetry
                ? {
                      label: '重新載入',
                      onClick: onRetry,
                      variant: 'outline',
                  }
                : undefined
        }
        size="md"
        className="border-red-200 bg-red-50"
    />
);

// 載入中的空狀態
export const LoadingEmptyState = ({ title = '載入中...', description = '正在載入資料，請稍候' }: { title?: string; description?: string }) => (
    <Card className="border-2 border-dashed border-gray-300">
        <CardContent className="p-8 text-center">
            <div className="space-y-4">
                <div className="flex justify-center">
                    <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
                </div>
                <div>
                    <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
                    <p className="mt-2 text-base text-gray-600">{description}</p>
                </div>
            </div>
        </CardContent>
    </Card>
);

// 繳費狀態的空狀態
export const NoPayState = () => {
    const { t } = useLanguage();
    const content = {
        title: t('儀表板.空狀態.繳費.標題'),
        description: t('儀表板.空狀態.繳費.說明'),
        actionLabel: t('儀表板.空狀態.繳費.按鈕'),
    };

    return <EmptyState title={content.title} description={content.description} size="md" />;
};
