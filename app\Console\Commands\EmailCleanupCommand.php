<?php

namespace App\Console\Commands;

use App\Models\EmailLog;
use App\Services\LogService;
use App\Enums\LogConstants;
use Illuminate\Console\Command;

class EmailCleanupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:cleanup 
                            {--days=90 : 保留天數}
                            {--status= : 指定清理的狀態 (sent|failed|bounced)}
                            {--dry-run : 僅顯示將要刪除的記錄數量，不實際刪除}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '清理舊的郵件記錄';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        $status = $this->option('status');
        $dryRun = $this->option('dry-run');

        $this->info("🧹 清理 {$days} 天前的郵件記錄...");

        if ($dryRun) {
            $this->warn('⚠️  DRY RUN 模式 - 不會實際刪除記錄');
        }

        // 建立查詢
        $query = EmailLog::where('created_at', '<', now()->subDays($days));

        if ($status) {
            $query->where('status', $status);
        }

        if ($dryRun) {
            $count = $query->count();
            $this->info("將刪除 {$count} 筆郵件記錄");

            // 顯示詳細統計
            $this->showCleanupStats($days, $status);

            return 0;
        }

        // 執行刪除
        $deleted = $query->delete();

        $this->info("✅ 清理完成！已刪除 {$deleted} 筆郵件記錄");

        // 記錄操作
        LogService::operationSuccess(
            '清理舊郵件記錄',
            [
                'days' => $days,
                'status' => $status,
                'deleted_count' => $deleted
            ],
            LogConstants::ACTION_DELETE
        );

        return 0;
    }

    /**
     * 顯示清理統計資訊
     */
    private function showCleanupStats(int $days, ?string $status): void
    {
        $cutoffDate = now()->subDays($days);

        $this->info("\n📊 清理統計 (截止日期: {$cutoffDate->format('Y-m-d H:i:s')}):");

        // 按狀態統計
        $statusStats = EmailLog::where('created_at', '<', $cutoffDate)
            ->when($status, fn ($q) => $q->where('status', $status))
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status');

        foreach ($statusStats as $emailStatus => $count) {
            $this->line("  {$emailStatus}: {$count} 筆");
        }

        // 按類型統計
        $this->info("\n按郵件類型:");
        $typeStats = EmailLog::where('created_at', '<', $cutoffDate)
            ->when($status, fn ($q) => $q->where('status', $status))
            ->selectRaw('email_type, COUNT(*) as count')
            ->groupBy('email_type')
            ->pluck('count', 'email_type');

        foreach ($typeStats as $type => $count) {
            $this->line("  {$type}: {$count} 筆");
        }

        // 最舊的記錄
        $oldest = EmailLog::where('created_at', '<', $cutoffDate)
            ->when($status, fn ($q) => $q->where('status', $status))
            ->orderBy('created_at')
            ->first();

        if ($oldest) {
            $this->info("\n最舊記錄: {$oldest->created_at} ({$oldest->created_at->diffForHumans()})");
        }
    }
}
