<?php

namespace App\Console\Commands;

use App\Services\LogService;
use App\Models\OperationLog;
use App\Models\SystemLog;
use App\Models\LoginLog;
use App\Models\EmailLog;
use Illuminate\Console\Command;

class LogCleanupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'log:cleanup 
                            {--days=30 : 保留天數}
                            {--type= : 指定清理的日誌類型 (operation|system|login|email)}
                            {--dry-run : 僅顯示將要刪除的記錄數量，不實際刪除}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '清理舊的日誌記錄';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        $type = $this->option('type');
        $dryRun = $this->option('dry-run');

        $this->info("🧹 開始清理 {$days} 天前的日誌記錄...");

        if ($dryRun) {
            $this->warn('⚠️  DRY RUN 模式 - 不會實際刪除記錄');
        }

        $totalDeleted = 0;
        $logTypes = $this->getLogTypes($type);

        foreach ($logTypes as $logType => $modelClass) {
            $this->info("📊 處理 {$logType} 日誌...");

            if ($dryRun) {
                $count = $this->getOldRecordsCount($modelClass, $days);
                $this->line("   將刪除 {$count} 筆記錄");
            } else {
                $deleted = LogService::cleanupOldLogs($modelClass, $days);
                $this->line("   已刪除 {$deleted} 筆記錄");
                $totalDeleted += $deleted;
            }
        }

        if (!$dryRun) {
            $this->info("✅ 清理完成！總共刪除 {$totalDeleted} 筆記錄");
        } else {
            $this->info("ℹ️  DRY RUN 完成！使用 --no-dry-run 執行實際清理");
        }

        return 0;
    }

    /**
     * 取得要處理的日誌類型
     */
    private function getLogTypes(?string $type): array
    {
        $allTypes = [
            'operation' => OperationLog::class,
            'system' => SystemLog::class,
            'login' => LoginLog::class,
            'email' => EmailLog::class,
        ];

        if ($type && isset($allTypes[$type])) {
            return [$type => $allTypes[$type]];
        }

        return $allTypes;
    }

    /**
     * 取得舊記錄數量
     */
    private function getOldRecordsCount(string $modelClass, int $days): int
    {
        return $modelClass::where('created_at', '<', now()->subDays($days))->count();
    }
}
