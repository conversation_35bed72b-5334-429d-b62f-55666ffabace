## ✅ Laravel 12.x：使用 Job 任務（Queue Job）簡易說明文件

---

### 🧩 **Job 是什麼？**

Job 是 Laravel 中的 **非同步任務單元**，用來處理耗時或背景執行的邏輯，例如：

- 發送郵件
- 同步 API
- 匯出報表
- 影像處理
- 推播通知

用於處理較耗時的任務，避免阻塞主執行緒，提升應用程式的響應速度。

---

## ✅ 1. 建立 Job 類別

使用 Artisan 指令：

```bash
php artisan make:job MyFirstJob
```

這會建立一個檔案於：

```
app/Jobs/MyFirstJob.php
```

---

## ✅ 2. 編寫 Job 邏輯

在產生的 `handle()` 方法中撰寫任務內容：

```php
<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class MyFirstJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任務超時時間（秒）
     */
    public $timeout = 1800; // 30分鐘

    /**
     * 最大重試次數
     */
    public $tries = 1;

    /**
     * 任務ID
     */
    protected string $taskId;

    /**
     * 合併參數
     */
    protected array $parameters;

    /**
     * 創建新的Job實例
     *
     * @param string $taskId 任務ID
     * @param array $parameters 壓縮參數
     */
    public function __construct(string $taskId, array $parameters)
    {
        $this->taskId = $taskId;
        $this->parameters = $parameters;
    }

    /**
     * 執行Job
     *
     * @return void
     */
    public function handle(): void
    {
        Log::info('任務開始執行', ['task_id' => $this->taskId]);

        // 假設 $task 是你自己管理的任務實體
        $task = MyTask::findByTaskId($this->taskId);

        try {
          // TODO: 加入你的任務邏輯
          Log::info('任務執行完成', ['task_id' => $this->taskId]);

          // $task->markAsReady();
        } catch (\Throwable $e) {
          Log::error('任務執行失敗', ['task_id' => $this->taskId, 'error' => $e->getMessage()]);

          // $task->markAsFailed($e->getMessage());
        }
    }
}
```

---

## ✅ 3. 觸發 Job

你可以在任何地方呼叫：

```php
use App\Jobs\MyFirstJob;

MyFirstJob::dispatch('task-uuid-123', ['option' => 'value']);
```

可選：延遲執行、設定佇列名稱：

```php
MyFirstJob::dispatch('task-uuid-123', ['option' => 'value'])->delay(now()->addMinutes(5))->onQueue('sync');
```

---

## ✅ 4. 設定 Queue 驅動

在 `.env` 中設定：

```env
QUEUE_CONNECTION=database
```

常用選項：

| 驅動       | 說明                       |
| ---------- | -------------------------- |
| `sync`     | 同步執行（除錯用）         |
| `database` | 使用資料庫佇列（穩定常用） |
| `redis`    | Redis 佇列（高效能）       |
| `sqs`      | AWS Simple Queue Service   |

---

## ✅ 5. 建立資料表（若用 database）

Laravel 12 新專案通常預設就已包含 `jobs` 的 migration，若無才需執行：

```bash
php artisan queue:table
php artisan migrate
```

這會建立 `jobs` 表來儲存待執行的任務。

---

## ✅ 6. 啟動 Queue Worker

用來「處理」這些佇列任務：

```bash
php artisan queue:work
```

📌 若你要背景執行，可配合 Supervisor（Linux）或 Windows 工作排程器。

---

## ✅ 7. Job 重試 & 失敗處理

Laravel 預設會重試失敗任務：

- 使用 `tries` 屬性控制最大重試次數
- `failed()` 方法可自定義失敗時的操作

```php
public $tries = 3;

public function failed(\Throwable $e)
{
    Log::error('Job 最終失敗', ['error' => $e->getMessage()]);
}
```

---

## ✅ 8. 常用 Artisan 指令

| 指令                           | 說明                         |
| ------------------------------ | ---------------------------- |
| `php artisan queue:work`       | 處理佇列任務                 |
| `php artisan queue:restart`    | 重啟所有 workers（部署常用） |
| `php artisan queue:retry {id}` | 重新執行失敗任務             |
| `php artisan queue:failed`     | 查看失敗任務                 |
| `php artisan queue:flush`      | 清空失敗任務紀錄             |

---

## ✅ Job 使用流程圖（簡化版）

```
[ 1. 建立 Job 類別 ]
            ↓
[ 2. 撰寫 handle() ]
            ↓
[ 3. dispatch() 呼叫 ]
            ↓
[ 4. Laravel 放入 Queue（DB, Redis, etc） ]
            ↓
[ 5. queue:work 處理 Job ]
            ↓
[ 6. 任務成功/失敗 → 重試/failed 處理 ]
```

---

## ✅ 總結表：Laravel Job 使用步驟

| 步驟                             | 說明                            |
| -------------------------------- | ------------------------------- |
| 1️⃣ 建立 Job                      | `php artisan make:job MyJob`    |
| 2️⃣ 撰寫 handle() 任務邏輯        | 例如：API 呼叫、寄信等          |
| 3️⃣ 呼叫 dispatch() 觸發          | 可延遲、指定佇列                |
| 4️⃣ 設定 `.env` 的佇列驅動        | 建議用 `database` 或 `redis`    |
| 5️⃣ 執行 `php artisan queue:work` | 實際開始處理佇列任務            |
| 6️⃣ 監控與處理失敗任務            | 使用 `failed()` 或 Artisan 指令 |

---

## 常見問題

- Job已經透過dispatch派發，但未被處理
    - 可能是佇列worker未啟動，或佇列設定有誤 << 重要
    - 檢查佇列設定，並執行`php artisan queue:work`啟動worker
- Job被重複執行
    - 檢查Job中的`$tries`設定，避免無限重試
    - 確保Job中的邏輯可重複執行（幂等性）
