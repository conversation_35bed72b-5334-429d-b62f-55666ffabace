<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\SystemSetting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 添加 API 同步設定
        $apiSettings = [
            [
                'key' => SystemSetting::API_SYNC_EXAM_IDS,
                'value' => '1,2',
                'type' => SystemSetting::TYPE_STRING,
                'category' => SystemSetting::CATEGORY_GENERAL,
                'description' => 'API同步的考試代碼列表(逗號分隔)',
            ],
            [
                'key' => SystemSetting::API_SYNC_INTERVAL_HOURS,
                'value' => '24',
                'type' => SystemSetting::TYPE_INTEGER,
                'category' => SystemSetting::CATEGORY_TIMING,
                'description' => 'API同步間隔時間(小時)',
            ],
        ];

        foreach ($apiSettings as $setting) {
            SystemSetting::firstOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 移除 API 同步設定
        $settingKeys = [
            SystemSetting::API_SYNC_EXAM_IDS,
            SystemSetting::API_SYNC_INTERVAL_HOURS,
        ];

        SystemSetting::whereIn('key', $settingKeys)->delete();
    }
};
