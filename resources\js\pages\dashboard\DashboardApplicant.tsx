import ConfirmationDialog from '@/components/ConfirmationDialog';
import { NoPayState, NoRecommendationsEmptyState } from '@/components/EmptyState';
import { ToastContainer } from '@/components/Toast';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { useLanguage } from '@/hooks/useLanguage';
import { useToast } from '@/hooks/useToast';
import { SharedData } from '@/types';
import { Head, router, useForm, usePage } from '@inertiajs/react';
import { AlertCircle, Clock, Plus, ChevronDown, ChevronUp, Calendar, User, Mail, Phone, BarChart3, Lock } from 'lucide-react';
import React, { useEffect, useState } from 'react';

// 型別定義
interface Recommendation {
    id: number;
    department_name: string; // 推薦函大分組
    program_type: string; // 推薦函小分組
    status: string;
    recommender_email?: string;
    recommender_name?: string;
    recommender_title?: string;
    recommender_phone?: string;
    recommender_department: string; // 推薦人所屬公司或組織
    pdf_path?: string;
    submitted_at?: string;
    created_at?: string;
    updated_at?: string;
}

// 外部 API 回傳的報名資料結構
interface ExternalApplication {
    autono: number;
    dep_no: string;
    dep_name: string;
    exam_name: string;
    is_upload: boolean; // 是否已上傳書審資料
    stu_sure_pay_note: string; // 確認繳費備註
}

// 內部分組結構
interface Group {
    autono: number;
    name: string;
    dep_no: string;
    is_upload: boolean; // 是否已上傳書審資料
    stu_sure_pay_note: string; // 確認繳費備註
    data: Recommendation[]; // 推薦函資料(分類用)
    // 擴充欄位
    need_recs?: number; // 需要幾份推薦信 todo 調整所需數量預設值 null 則代表未限制
}

interface Registration {
    department: string;
    groups: Group[];
}

/**
 * 將登入時送往前端的資料根據 dep_name 進行分組
 */
function transformExternalApplications(applications: ExternalApplication[]): Registration[] {
    if (!applications || applications.length === 0) {
        return [];
    }

    // 根據 dep_name 分組
    const groupedByDepartment = applications.reduce(
        (acc, app) => {
            const deptName = app.dep_name;
            if (!acc[deptName]) {
                acc[deptName] = [];
            }
            acc[deptName].push(app);
            return acc;
        },
        {} as Record<string, ExternalApplication[]>,
    );

    // 轉換為前端格式
    return Object.entries(groupedByDepartment).map(([department, apps]) => ({
        department,
        groups: apps.map((app) => ({
            name: app.exam_name,
            dep_no: app.dep_no,
            autono: app.autono,
            is_upload: app.is_upload,
            stu_sure_pay_note: app.stu_sure_pay_note,
            need_recs: 0, // 初始需要推薦函數量為 0 (未使用)
            data: [], // 初始推薦函資料為空
        })),
    }));
}

// 工具函式將推薦函套入註冊表資料中
function transformRegistrationData(registrations: Registration[], recommendations: Recommendation[]): Registration[] {
    return registrations.map((reg) => {
        const updatedGroups = reg.groups.map((group) => {
            const matched = recommendations.filter((rec) => rec.department_name === reg.department && rec.program_type === group.name);

            return {
                ...group,
                data: matched, // 可為空，但明確表示這個群組目前沒有推薦函
            };
        });

        return {
            ...reg,
            groups: updatedGroups,
        };
    });
}

export default function DashboardApplicant() {
    const { recommendations, applications, applicant_info, system_info } = usePage<SharedData>().props;
    const { t } = useLanguage();
    const toast = useToast();

    // 簡單的錯誤處理函數
    const getErrorMessage = (errors: any): string => {
        if (typeof errors === 'string') return errors;
        if (errors && typeof errors === 'object') {
            // 取得第一個錯誤訊息
            const firstError = Object.values(errors)[0];
            if (Array.isArray(firstError)) {
                return firstError[0] as string;
            }
            return firstError as string;
        }
        return '發生未知錯誤，請稍後再試';
    };

    const [showFormFor, setShowFormFor] = useState<{
        department: string;
        group: string;
        extAutono: number;
    } | null>(null);
    const [registrations, setRegistrations] = useState<Registration[]>([]);
    const [expandedCards, setExpandedCards] = useState<Set<number>>(new Set()); // 追蹤展開的卡片

    // 檢查是否應該顯示狀態概覽（只有當有推薦函需求時才顯示）
    const shouldShowOverview = () => {
        return registrations.some((registration) => registration.groups.some((group) => group.need_recs > 0));
    };

    // 確認視窗狀態
    const [confirmDialog, setConfirmDialog] = useState<{
        isOpen: boolean;
        type: 'remind' | 'withdraw' | null;
        recommendation: Recommendation | null;
        isLoading: boolean;
    }>({
        isOpen: false,
        type: null,
        recommendation: null,
        isLoading: false,
    });

    // 初始化時添加 light 類別
    useEffect(() => {
        document.documentElement.classList.add('light');
        return () => {
            document.documentElement.classList.remove('light');
        };
    }, []);

    // 狀態顯示函數 - 包含超時檢查
    const getStatusBadge = (rec: Recommendation) => {
        const status = rec.status;
        const overdue = isOverdue(rec);

        if (status === 'pending' && overdue) {
            return (
                <Badge variant="secondary" className="border-amber-300 bg-amber-100 text-amber-700">
                    ⏰ 未回應 ({getDaysAgo(rec)}天)
                </Badge>
            );
        }

        switch (status) {
            case 'pending':
                return (
                    <Badge variant="secondary" className="border-slate-300 bg-slate-100 text-slate-700">
                        {t('通用.標籤.待處理')}
                    </Badge>
                );

            case 'submitted':
                return (
                    <Badge variant="secondary" className="border-emerald-300 bg-emerald-100 text-emerald-700">
                        ✓ {t('通用.標籤.已完成')}
                    </Badge>
                );

            case 'withdrawn':
                return (
                    <Badge variant="secondary" className="border-slate-300 bg-slate-200 text-slate-700">
                        {t('通用.標籤.已撤回')}
                    </Badge>
                );

            case 'no_response':
                return (
                    <Badge variant="secondary" className="border-amber-300 bg-amber-100 text-amber-700">
                        ⏰ {t('通用.標籤.無回應')}
                    </Badge>
                );

            case 'declined':
                return (
                    <Badge variant="secondary" className="border-rose-300 bg-rose-100 text-rose-700">
                        ✗ {t('通用.標籤.已婉拒')}
                    </Badge>
                );

            default:
                return (
                    <Badge variant="secondary" className="border-gray-300 bg-gray-100 text-gray-700">
                        {status}
                    </Badge>
                );
        }
    };

    // 進度顯示函數
    const getProgressInfo = (group: Group) => {
        const completed = group.data.filter((rec) => rec.status === 'submitted').length;
        const total = group.need_recs;
        const isComplete = completed >= total;
        const remainingNeeded = Math.max(0, total - completed); // 基於已完成數量計算還需要的

        return {
            completed,
            total,
            remaining: remainingNeeded,
            isComplete,
            progressText: `${completed}/${total}`,
            progressColor: isComplete ? 'text-emerald-600' : completed > 0 ? 'text-slate-600' : 'text-gray-500',
        };
    };

    // 卡片樣式函數 - 包含超時檢查
    const getCardStyle = (rec: Recommendation) => {
        const status = rec.status;
        const overdue = isOverdue(rec);

        // 未回應，強調但不刺眼
        if (status === 'pending' && overdue) {
            return 'border-yellow-300 bg-yellow-100/30 shadow-sm'; // 柔和黃
        }

        switch (status) {
            case 'submitted':
                return 'border-green-300 bg-green-100/30 shadow-sm'; // 柔和綠，表示完成
            case 'pending':
                return 'border-blue-200 bg-blue-50/30 shadow-sm'; // 柔和藍，表示等待
            case 'withdrawn':
                return 'border-gray-300 bg-gray-100/30 shadow-sm'; // 中性灰，表示取消
            case 'no_response':
                return 'border-yellow-200 bg-yellow-50/30 shadow-sm'; // 淺黃，表示未回應
            case 'declined':
                return 'border-rose-300 bg-rose-100/30 shadow-sm'; // 柔和紅，表示拒絕
            default:
                return 'border-gray-200 bg-white shadow-sm'; // 預設白底灰框
        }
    };

    const form = useForm({
        recommenderEmail: '',
        recommenderName: '',
        recommenderTitle: '',
        recommenderPhone: '',
        recommenderAffiliation: '',
        department: '',
        group: '',
        extAutono: 0, // 考生流水號
    });

    // 初始化報名資料並套入推薦函資料
    useEffect(() => {
        if (!applications || applications.length === 0) {
            setRegistrations([]);
            return;
        }

        // 將外部 API 資料轉換為前端格式
        const initialRegistrations = transformExternalApplications(applications as ExternalApplication[]);

        // 如果有推薦函資料，立即套入
        if (recommendations && recommendations.length > 0) {
            const updated = transformRegistrationData(initialRegistrations, recommendations);
            setRegistrations(updated);
        } else {
            setRegistrations(initialRegistrations);
        }
    }, [applications, recommendations]);

    // 切換卡片展開狀態
    const toggleCardExpansion = (recommendationId: number) => {
        setExpandedCards((prev) => {
            const newSet = new Set(prev);
            if (newSet.has(recommendationId)) {
                newSet.delete(recommendationId);
            } else {
                newSet.add(recommendationId);
            }
            return newSet;
        });
    };

    // 檢查推薦函是否超過7天未回應
    const isOverdue = (rec: Recommendation): boolean => {
        if (rec.status !== 'pending') return false;

        const createdDate = new Date(rec.created_at || '');
        const now = new Date();
        const daysDiff = Math.floor((now.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24));

        return daysDiff >= 7;
    };

    // 計算推薦函建立天數
    const getDaysAgo = (rec: Recommendation): number => {
        const createdDate = new Date(rec.created_at || '');
        const now = new Date();
        return Math.floor((now.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24));
    };

    // 開啟表單
    const openForm = (department: string, group: string, extAutono: number) => {
        // console.log('Opening form for:', department, group, extAutono);
        setShowFormFor({ department, group, extAutono });
        form.setData({
            recommenderEmail: '',
            recommenderName: '',
            recommenderTitle: '',
            recommenderPhone: '',
            recommenderAffiliation: '',
            department,
            group,
            extAutono,
        });
    };

    const closeForm = () => {
        setShowFormFor(null);
    };

    const submitForm = (e: React.FormEvent) => {
        e.preventDefault();

        // 防止重複提交
        if (form.processing) {
            return;
        }

        // 確保使用正確的 department 和 group 值
        const department = showFormFor?.department || form.data.department;
        const group = showFormFor?.group || form.data.group;
        const extAutono = showFormFor?.extAutono || form.data.extAutono;

        router.post(
            route('recommendations.applicant.create'),
            {
                recommender_email: form.data.recommenderEmail,
                recommender_name: form.data.recommenderName,
                recommender_title: form.data.recommenderTitle,
                recommender_phone: form.data.recommenderPhone,
                recommender_department: form.data.recommenderAffiliation,
                department_name: department,
                program_type: group,
                external_autono: extAutono, // 使用考生流水號
            },
            {
                onSuccess: (response: unknown) => {
                    // Handle enhanced response with recommender info
                    const data = (response as any)?.props?.flash || (response as any) || {};

                    if (data.message) {
                        toast.success(data.message);
                    } else {
                        toast.success('推薦函邀請已成功創建並發送');
                    }

                    // Optionally show recommender login link if created
                    if (data.recommender?.created && data.recommender?.login_url) {
                        toast.info(`推薦人帳號已建立，登入連結${data.recommender.login_url}`, {
                            duration: 10000,
                            action: {
                                label: '複製連結',
                                onClick: () => {
                                    navigator.clipboard.writeText(data.recommender.login_url);
                                    toast.success('登入連結已複製到剪貼簿');
                                },
                            },
                        });
                    }

                    closeForm();
                    // Refresh the page to show updated data
                    router.reload();
                },
                onError: (errors) => {
                    closeForm();
                    // console.error('Error creating recommendation:', errors);
                    const errorMessage = getErrorMessage(errors);
                    toast.error(errorMessage);
                },
            },
        );
    };

    // Open confirmation dialog
    const openConfirmDialog = (type: 'remind' | 'withdraw', recommendation: Recommendation) => {
        setConfirmDialog({
            isOpen: true,
            type,
            recommendation,
            isLoading: false,
        });
    };

    // Close confirmation dialog
    const closeConfirmDialog = () => {
        setConfirmDialog({
            isOpen: false,
            type: null,
            recommendation: null,
            isLoading: false,
        });
    };

    // Handle confirmed action
    const handleConfirmedAction = () => {
        if (!confirmDialog.recommendation || !confirmDialog.type) return;

        setConfirmDialog((prev) => ({ ...prev, isLoading: true }));

        const rec = confirmDialog.recommendation;

        if (confirmDialog.type === 'remind') {
            router.post(
                route('recommendations.applicant.remind', { id: rec.id }),
                {},
                {
                    onSuccess: () => {
                        toast.success(t('儀表板.考生.推薦函.reminderSent'));
                        closeConfirmDialog();
                        router.reload();
                    },
                    onError: (errors) => {
                        // console.error('Error sending reminder:', errors);
                        const errorMessage = getErrorMessage(errors);
                        toast.error(errorMessage || t('儀表板.考生.推薦函.reminderFailed'));
                        setConfirmDialog((prev) => ({ ...prev, isLoading: false, isOpen: false }));
                    },
                },
            );
        } else if (confirmDialog.type === 'withdraw') {
            router.post(
                route('recommendations.applicant.withdraw', { id: rec.id }),
                {},
                {
                    onSuccess: () => {
                        toast.success(t('儀表板.考生.推薦函.withdrawn'));
                        closeConfirmDialog();
                        router.reload();
                    },
                    onError: (errors) => {
                        // console.error('Error withdrawing recommendation:', errors);
                        const errorMessage = getErrorMessage(errors);
                        toast.error(errorMessage || t('儀表板.考生.推薦函.withdrawFailed'));
                        setConfirmDialog((prev) => ({ ...prev, isLoading: false, isOpen: false }));
                    },
                },
            );
        }
    };

    return (
        <div className="p-4">
            <Head title="推薦函列表"></Head>
            {/* Toast Container */}
            <ToastContainer toasts={toast.toasts} onRemove={toast.removeToast} position="top-right" />

            {/* 操作狀態禁止通知 is_user_allowed */}
            {!system_info.is_user_allowed && (
                <div className="mb-6 rounded-lg border-l-4 border-gray-400 bg-gray-50 p-4 shadow-sm">
                    <div className="flex items-start">
                        <div className="flex-shrink-0">
                            <Lock className="h-5 w-5 text-gray-500" />
                        </div>
                        <div className="ml-3 flex-1">
                            <h3 className="text-sm font-medium text-gray-800">目前無法進行此操作</h3>
                            <div className="mt-2 text-sm text-gray-700">
                                推薦函功能僅於考試期間內開放。您的申請階段尚未開始或已結束，請留意後續開放時間。
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* 提醒訊息 */}
            <div className="mb-6 rounded-lg border-l-4 border-blue-500 bg-blue-50 p-4 shadow-sm">
                <div className="flex items-start">
                    <div className="flex-shrink-0">
                        <AlertCircle className="h-5 w-5 text-blue-400" />
                    </div>
                    <div className="ml-3 flex-1">
                        <h3 className="text-sm font-medium text-blue-800">{t('儀表板.考生.重要通知.標題')}</h3>

                        <div className="mt-2 text-sm text-blue-700">
                            <ul className="list-disc space-y-1 pl-5">
                                <li>
                                    <strong>{t('儀表板.考生.重要通知.截止標題')}</strong>
                                    <span className="font-semibold text-red-600">{system_info.period_info?.app_date1_end}</span>（
                                    {t('儀表板.考生.重要通知.截止說明')}）
                                </li>
                                <li>
                                    <strong>{t('儀表板.考生.重要通知.建議提前日期')}</strong>
                                    {t('儀表板.考生.重要通知.建議提前說明')}
                                </li>

                                <li>
                                    <strong>{t('儀表板.考生.重要通知.提醒功能')}</strong>
                                    {t('儀表板.考生.重要通知.提醒說明', { hours: system_info.reminder_cooldown_hours })}
                                </li>
                            </ul>
                        </div>

                        {/* 聯絡方式 + 系統時間，左右排 */}
                        <div className="mt-4 flex flex-col gap-1 text-xs text-blue-600 sm:flex-row sm:items-center sm:justify-between sm:gap-3">
                            <div className="flex flex-wrap items-center gap-x-1 gap-y-1">
                                {t('儀表板.考生.重要通知.聯絡說明')}
                                <a href={`tel:${system_info.support_tel}`} className="font-medium text-blue-600 underline hover:text-blue-500">
                                    {system_info.support_tel}
                                </a>
                                <span>{t('通用.其他.或')}</span>
                                <a href={`mailto:${system_info.support_email}`} className="font-medium text-blue-600 underline hover:text-blue-500">
                                    {system_info.support_email}
                                </a>
                            </div>

                            <div className="mt-1 flex items-center text-blue-600 sm:mt-0">
                                <Clock className="mr-1 h-3 w-3" />
                                {t('儀表板.考生.重要通知.系統時間')}
                                {new Date().toLocaleString('zh-TW', {
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    hour12: false,
                                })}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* 使用者基本資訊 */}
            <div className="mb-6 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                <div className="mb-4 flex items-center gap-2">
                    <User className="h-5 w-5 text-gray-600" />
                    <h3 className="text-lg font-semibold text-gray-900">{t('儀表板.考生.個人資料.標題')}</h3>
                </div>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {/* 姓名 */}
                    <div className="flex items-center gap-3 rounded-lg bg-gray-50 p-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                            <User className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                            <p className="text-xs font-medium text-gray-500">{t('儀表板.考生.個人資料.姓名')}</p>
                            <p className="truncate text-sm font-medium text-gray-900">{applicant_info.user.name}</p>
                        </div>
                    </div>

                    {/* 電子郵件 */}
                    <div className="flex items-center gap-3 rounded-lg bg-gray-50 p-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                            <Mail className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                            <p className="text-xs font-medium text-gray-500">{t('儀表板.考生.個人資料.電子郵件')}</p>
                            <p className="truncate text-sm font-medium text-gray-900">{applicant_info.user.email}</p>
                        </div>
                    </div>

                    {/* 電話 */}
                    {applicant_info.user.phone && (
                        <div className="flex items-center gap-3 rounded-lg bg-gray-50 p-3">
                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                                <Phone className="h-4 w-4 text-blue-600" />
                            </div>
                            <div className="min-w-0 flex-1">
                                <p className="text-xs font-medium text-gray-500">{t('儀表板.考生.個人資料.聯絡電話')}</p>
                                <p className="truncate text-sm font-medium text-gray-900">{applicant_info.user.phone}</p>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* 狀態概覽 - 只有當有推薦函需求時才顯示 */}
            {shouldShowOverview() && (
                <div className="mb-6 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                    <div className="mb-4 flex items-center gap-2">
                        <BarChart3 className="h-5 w-5 text-gray-600" />
                        <h3 className="text-lg font-semibold text-gray-900">{t('儀表板.考生.推薦函.整體進度')}</h3>
                    </div>
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {registrations.map((registration) =>
                            registration.groups
                                .filter((group) => group.need_recs > 0) // 只顯示有推薦函需求的群組
                                .map((group) => {
                                    const progress = getProgressInfo(group);
                                    const isComplete = progress.isComplete;
                                    const hasIncomplete = progress.remaining > 0;

                                    return (
                                        <div
                                            key={`${registration.department}-${group.name}`}
                                            className={`rounded-lg border p-4 transition-all duration-200 ${
                                                isComplete
                                                    ? 'border-emerald-200 bg-emerald-50 shadow-sm'
                                                    : hasIncomplete
                                                      ? 'border-amber-200 bg-amber-50 shadow-sm'
                                                      : 'border-gray-200 bg-gray-50'
                                            }`}
                                        >
                                            <div className="flex items-start justify-between">
                                                <div className="min-w-0 flex-1">
                                                    <h4 className="truncate text-sm font-medium text-gray-900">{registration.department}</h4>
                                                </div>
                                                <Badge variant="outline" className={`${progress.progressColor} border-current text-xs`}>
                                                    {progress.progressText}
                                                </Badge>
                                            </div>
                                        </div>
                                    );
                                }),
                        )}
                    </div>
                </div>
            )}

            {/* 推薦函列表 */}
            <div className="space-y-8">
                {registrations.map((registration, regIndex) => (
                    <section
                        key={`${registration.department}-${registration.groups[0].name}`}
                        className={`${regIndex > 0 ? 'border-t border-gray-200 pt-8' : ''}`}
                    >
                        <div className="space-y-8">
                            {registrations.length > 0 ? (
                                registration.groups.map((group, groupIndex) => {
                                    return (
                                        <div key={group.name} className={`${groupIndex > 0 ? 'border-t border-gray-100 pt-6' : ''}`}>
                                            {/* 學程標題卡片 */}
                                            <div className="mb-4">
                                                <div className="flex justify-between">
                                                    <div className="flex items-center gap-3">
                                                        <div className="flex-1 border-l-4 border-gray-500 pl-3">
                                                            <h3 className="text-lg font-semibold text-gray-900">{registration.department}</h3>
                                                            <p className="text-sm text-gray-600">{group.name}</p>
                                                            {!group.is_upload && (
                                                                <span className="mt-2 inline-block rounded-full bg-orange-100 px-3 py-1 text-xs font-medium text-orange-700">
                                                                    {t('儀表板.考生.推薦函.尚未上傳書審資料')}
                                                                </span>
                                                            )}
                                                        </div>
                                                    </div>
                                                    {/* 添加推薦人按鈕 */}
                                                    <div className="flex-shrink-0">
                                                        {group.data.length !== 0 && (
                                                            <Button
                                                                size="sm"
                                                                className="bg-indigo-600 text-white transition hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-300"
                                                                onClick={() => openForm(registration.department, group.name, group.autono)}
                                                                title={t('儀表板.考生.推薦函.新增推薦人按鈕')}
                                                            >
                                                                <Plus className="h-4 w-4 sm:mr-2" />
                                                                <span className="hidden sm:inline">{t('儀表板.考生.推薦函.新增推薦人按鈕')}</span>
                                                            </Button>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="space-y-4">
                                                {/* 先檢查繳費狀態，再檢查推薦信狀態 */}
                                                {(group.stu_sure_pay_note === 'N' && <NoPayState />) ||
                                                    (group.data.length === 0 && (
                                                        <NoRecommendationsEmptyState
                                                            userRole="applicant"
                                                            onCreateNew={() => openForm(registration.department, group.name, group.autono)}
                                                        />
                                                    ))}
                                                {group.data.map((rec) => {
                                                    const isExpanded = expandedCards.has(rec.id);
                                                    return (
                                                        <Card key={rec.id} className={getCardStyle(rec)}>
                                                            <CardContent className="py-0">
                                                                {/* 卡片標題列 - 始終顯示 */}
                                                                <div
                                                                    className="-m-2 flex cursor-pointer items-center justify-between rounded-lg p-3 transition-all duration-200 hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50"
                                                                    onClick={() => toggleCardExpansion(rec.id)}
                                                                >
                                                                    <div className="flex min-w-0 flex-1 items-center gap-4">
                                                                        <div className="min-w-0 flex-1">
                                                                            <h4 className="truncate text-lg font-semibold text-gray-900">
                                                                                {rec.recommender_name}
                                                                            </h4>
                                                                            {(rec.recommender_title || rec.recommender_department) && (
                                                                                <p className="text-sm text-gray-500">
                                                                                    {rec.recommender_title}
                                                                                    {rec.recommender_title && rec.recommender_department && ' – '}
                                                                                    {rec.recommender_department}
                                                                                </p>
                                                                            )}
                                                                            <p className="truncate text-sm text-gray-600">
                                                                                {t('儀表板.考生.推薦函.邀請詳情.推薦人信箱')}
                                                                                {rec.recommender_email}
                                                                            </p>
                                                                        </div>
                                                                        {getStatusBadge(rec)}
                                                                    </div>
                                                                    <div className="flex flex-shrink-0 items-center gap-2">
                                                                        {isExpanded ? (
                                                                            <ChevronUp className="h-5 w-5 text-gray-400 transition-transform duration-200" />
                                                                        ) : (
                                                                            <ChevronDown className="h-5 w-5 text-gray-400 transition-transform duration-200" />
                                                                        )}
                                                                    </div>
                                                                </div>

                                                                {/* 詳細資訊 - 可收合 */}
                                                                {isExpanded && (
                                                                    <div className="mt-4 space-y-6 border-t border-gray-200 pt-5">
                                                                        {/* 推薦人詳細資訊僅在 md+ 顯示，避免小螢幕重複 */}
                                                                        <div className="grid-cols-1 gap-4 md:grid md:grid-cols-2">
                                                                            {rec.recommender_phone && (
                                                                                <div className="flex items-center gap-3 rounded-md border border-amber-100 bg-amber-50/60 p-3">
                                                                                    <Phone className="h-4 w-4 text-amber-600" />
                                                                                    <div>
                                                                                        <p className="text-xs font-medium text-gray-500">
                                                                                            {t('儀表板.考生.推薦函.邀請詳情.推薦人電話')}
                                                                                        </p>
                                                                                        <p className="text-sm font-medium text-gray-900">
                                                                                            {rec.recommender_phone}
                                                                                        </p>
                                                                                    </div>
                                                                                </div>
                                                                            )}
                                                                        </div>

                                                                        {/* 時程資訊 */}
                                                                        <div
                                                                            className={`rounded-lg border p-4 ${
                                                                                isOverdue(rec)
                                                                                    ? 'border-orange-200 bg-orange-50/40'
                                                                                    : 'border-slate-200 bg-slate-50/50'
                                                                            }`}
                                                                        >
                                                                            <h5 className="mb-3 flex items-center gap-2 text-sm font-semibold text-gray-800">
                                                                                <Calendar className="h-4 w-4 text-gray-500" />
                                                                                {t('儀表板.考生.推薦函.邀請詳情.標題')}
                                                                                {isOverdue(rec) && (
                                                                                    <span className="ml-2 text-xs font-medium text-orange-600">
                                                                                        ({t('儀表板.考生.推薦函.邀請詳情.超時')} {getDaysAgo(rec)}{' '}
                                                                                        {t('儀表板.考生.推薦函.邀請詳情.天')})
                                                                                    </span>
                                                                                )}
                                                                            </h5>

                                                                            <div className="space-y-2 text-sm text-gray-700">
                                                                                <div className="flex justify-between">
                                                                                    <span>{t('儀表板.考生.推薦函.邀請詳情.邀請時間')}</span>
                                                                                    <span className="font-medium text-gray-900">
                                                                                        {new Date(rec.created_at || '').toLocaleString('zh-TW')}
                                                                                    </span>
                                                                                </div>

                                                                                <div className="flex justify-between">
                                                                                    <span>{t('儀表板.考生.推薦函.邀請詳情.發信狀態')}</span>
                                                                                    <span className="font-medium text-emerald-600">
                                                                                        ✓ {t('儀表板.考生.推薦函.邀請詳情.成功')}
                                                                                    </span>
                                                                                </div>

                                                                                {rec.updated_at && rec.updated_at !== rec.created_at && (
                                                                                    <div className="flex justify-between">
                                                                                        <span>{t('儀表板.考生.推薦函.邀請詳情.最後更新時間')}</span>
                                                                                        <span className="font-medium text-gray-900">
                                                                                            {new Date(rec.updated_at).toLocaleString('zh-TW')}
                                                                                        </span>
                                                                                    </div>
                                                                                )}

                                                                                {rec.submitted_at && (
                                                                                    <div className="flex justify-between">
                                                                                        <span>{t('儀表板.考生.推薦函.邀請詳情.完成時間')}</span>
                                                                                        <span className="font-medium text-green-600">
                                                                                            {new Date(rec.submitted_at).toLocaleString('zh-TW')}
                                                                                        </span>
                                                                                    </div>
                                                                                )}
                                                                            </div>

                                                                            {/* 超時警告 */}
                                                                            {isOverdue(rec) && (
                                                                                <div className="mt-4 rounded-md border border-orange-300 bg-orange-100/60 p-4">
                                                                                    <div className="flex items-start gap-3">
                                                                                        <AlertCircle className="h-5 w-5 flex-shrink-0 text-orange-600" />
                                                                                        <div className="text-sm text-orange-800">
                                                                                            <p className="mb-1 font-semibold">
                                                                                                {t('儀表板.考生.推薦函.邀請詳情.超時警告')}
                                                                                            </p>
                                                                                            <ul className="ml-4 list-disc space-y-0.5 text-xs leading-relaxed">
                                                                                                <li>{t('儀表板.考生.推薦函.checkEmail')}</li>
                                                                                                <li>{t('儀表板.考生.推薦函.contactRecommender')}</li>
                                                                                                <li>{t('儀表板.考生.推薦函.useReminder')}</li>
                                                                                            </ul>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            )}
                                                                        </div>

                                                                        {/* 操作按鈕區域 */}
                                                                        {rec.status === 'pending' && (
                                                                            <div className="flex flex-col gap-2 sm:flex-row sm:justify-end sm:gap-3">
                                                                                <Button
                                                                                    size="sm"
                                                                                    variant="outline"
                                                                                    className="border-sky-300 bg-sky-50 text-sky-800 hover:border-sky-400 hover:bg-sky-100"
                                                                                    onClick={() => openConfirmDialog('remind', rec)}
                                                                                >
                                                                                    {t('儀表板.考生.推薦函.邀請詳情.提醒按鈕')}
                                                                                </Button>
                                                                                <Button
                                                                                    size="sm"
                                                                                    variant="outline"
                                                                                    className="border-rose-300 bg-rose-50 text-rose-700 hover:border-rose-400 hover:bg-rose-100"
                                                                                    onClick={() => openConfirmDialog('withdraw', rec)}
                                                                                >
                                                                                    {t('儀表板.考生.推薦函.邀請詳情.撤回按鈕')}
                                                                                </Button>
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                )}
                                                            </CardContent>
                                                        </Card>
                                                    );
                                                })}
                                            </div>
                                        </div>
                                    );
                                })
                            ) : (
                                <div className="p-4 text-center text-gray-500">{t('儀表板.考生.推薦函.邀請詳情.無資料')}</div>
                            )}
                        </div>
                    </section>
                ))}
            </div>
            {/* Recommendation Form Modal */}
            {showFormFor !== null && (
                <div
                    className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4"
                    role="dialog"
                    aria-modal="true"
                    aria-labelledby="modal-title"
                >
                    <div className="w-full max-w-lg scale-100 transform rounded-lg bg-white p-6 shadow-xl transition-transform">
                        <h2 id="modal-title" className="mb-6 text-xl font-bold text-gray-900">
                            {t('儀表板.考生.推薦函.表單.標題')}
                        </h2>
                        <form onSubmit={submitForm} className="space-y-4">
                            {/* 顯示當前選擇的群組信息 */}
                            <div className="rounded-lg border border-blue-200 bg-blue-50 p-3">
                                <p className="text-sm text-blue-800">
                                    {form.data.group} – {form.data.department}
                                </p>
                            </div>

                            <div>
                                <label className="mb-2 block font-semibold text-gray-700" htmlFor="recommenderName">
                                    {t('儀表板.考生.推薦函.表單.推薦人姓名')} <span className="text-red-500">*</span>
                                </label>
                                <input
                                    id="recommenderName"
                                    type="text"
                                    value={form.data.recommenderName}
                                    onChange={(e) => form.setData('recommenderName', e.target.value)}
                                    className="w-full rounded border border-gray-300 p-2 text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-400 focus:outline-none"
                                    required
                                />
                            </div>
                            <div>
                                <label className="mb-2 block font-semibold text-gray-700" htmlFor="recommenderEmail">
                                    {t('儀表板.考生.推薦函.表單.推薦人電子郵件')} <span className="text-red-500">*</span>
                                </label>
                                <input
                                    id="recommenderEmail"
                                    type="email"
                                    value={form.data.recommenderEmail}
                                    onChange={(e) => form.setData('recommenderEmail', e.target.value)}
                                    className="w-full rounded border border-gray-300 p-2 text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-400 focus:outline-none"
                                    required
                                />
                            </div>
                            <div>
                                <label className="mb-2 block font-semibold text-gray-700" htmlFor="recommenderTitle">
                                    {t('儀表板.考生.推薦函.表單.推薦人職稱')} <span className="text-red-500">*</span>
                                </label>
                                <input
                                    id="recommenderTitle"
                                    type="text"
                                    value={form.data.recommenderTitle}
                                    onChange={(e) => form.setData('recommenderTitle', e.target.value)}
                                    placeholder={t('儀表板.考生.推薦函.表單.推薦人職稱說明')}
                                    className="w-full rounded border border-gray-300 p-2 text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-400 focus:outline-none"
                                    required
                                />
                            </div>

                            <div>
                                <label className="mb-2 block font-semibold text-gray-700" htmlFor="recommenderAffiliation">
                                    {t('儀表板.考生.推薦函.表單.推薦人所屬單位')} <span className="text-red-500">*</span>
                                </label>
                                <input
                                    id="recommenderAffiliation"
                                    type="text"
                                    value={form.data.recommenderAffiliation || ''}
                                    onChange={(e) => form.setData('recommenderAffiliation', e.target.value)}
                                    placeholder={t('儀表板.考生.推薦函.表單.推薦人所屬單位說明')}
                                    className="w-full rounded border border-gray-300 p-2 text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-400 focus:outline-none"
                                    required
                                />
                            </div>
                            <div>
                                <label className="mb-2 block font-semibold text-gray-700" htmlFor="recommenderPhone">
                                    {t('儀表板.考生.推薦函.表單.推薦人聯絡電話')}
                                </label>
                                <input
                                    id="recommenderPhone"
                                    type="text"
                                    value={form.data.recommenderPhone}
                                    onChange={(e) => form.setData('recommenderPhone', e.target.value)}
                                    className="w-full rounded border border-gray-300 p-2 text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-400 focus:outline-none"
                                />
                            </div>

                            <div className="flex justify-end space-x-3">
                                <button
                                    type="submit"
                                    className="rounded bg-green-600 px-5 py-2 text-white shadow-sm transition-colors hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-1 focus:outline-none"
                                >
                                    {t('儀表板.考生.推薦函.表單.送出')}
                                </button>
                                <button
                                    type="button"
                                    className="rounded bg-gray-300 px-5 py-2 font-medium text-gray-700 shadow-sm transition-colors hover:bg-gray-400 focus:ring-2 focus:ring-gray-400 focus:ring-offset-1 focus:outline-none"
                                    onClick={closeForm}
                                >
                                    {t('儀表板.考生.推薦函.表單.取消')}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
            {/* Confirmation Dialog */}
            <ConfirmationDialog
                isOpen={confirmDialog.isOpen}
                onClose={closeConfirmDialog}
                onConfirm={handleConfirmedAction}
                title={confirmDialog.type === 'remind' ? t('儀表板.考生.推薦函.發送提醒') : t('儀表板.考生.推薦函.確認撤回')}
                message={confirmDialog.type === 'remind' ? `${t('儀表板.考生.推薦函.提醒確認訊息')}` : `${t('儀表板.考生.推薦函.撤回確認訊息')}`}
                subMessage={`${t('儀表板.考生.推薦函.邀請詳情.推薦人姓名')}${confirmDialog.recommendation?.recommender_name || ''}`}
                confirmText={confirmDialog.type === 'remind' ? t('儀表板.考生.推薦函.發送提醒') : t('儀表板.考生.推薦函.確認撤回')}
                type={confirmDialog.type === 'withdraw' ? 'danger' : 'warning'}
                isLoading={confirmDialog.isLoading}
            />
        </div>
    );
}
