<?php

namespace Database\Factories;

use App\Models\EmailLog;
use App\Models\RecommendationLetter;
use App\Enums\LogConstants;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EmailLog>
 */
class EmailLogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = EmailLog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $status = $this->faker->randomElement([
            LogConstants::EMAIL_STATUS_SENT,
            LogConstants::EMAIL_STATUS_FAILED,
            LogConstants::EMAIL_STATUS_PENDING,
            LogConstants::EMAIL_STATUS_BOUNCED
        ]);

        return [
            'recommendation_letter_id' => $this->faker->optional()->randomElement([
                null,
                RecommendationLetter::factory()
            ]),
            'recipient_email' => $this->faker->email(),
            'recipient_name' => $this->faker->name(),
            'email_type' => $this->faker->randomElement([
                LogConstants::EMAIL_TYPE_INVITATION,
                LogConstants::EMAIL_TYPE_REMINDER,
                LogConstants::EMAIL_TYPE_NOTIFICATION,
                LogConstants::EMAIL_TYPE_ALERT
            ]),
            'subject' => $this->faker->sentence(),
            'content' => $this->faker->paragraph(),
            'status' => $status,
            'retry_count' => $status === LogConstants::EMAIL_STATUS_FAILED 
                ? $this->faker->numberBetween(0, 3) 
                : 0,
            'error_message' => $status === LogConstants::EMAIL_STATUS_FAILED 
                ? $this->faker->sentence() 
                : null,
            'sent_at' => $status === LogConstants::EMAIL_STATUS_SENT 
                ? $this->faker->dateTimeBetween('-30 days', 'now') 
                : null,
            'metadata' => [
                'template' => $this->faker->randomElement(['invitation', 'reminder', 'notification']),
                'priority' => $this->faker->randomElement(['high', 'normal', 'low']),
                'additional_data' => $this->faker->words(3, true)
            ],
            'created_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the email was sent successfully.
     */
    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => LogConstants::EMAIL_STATUS_SENT,
            'sent_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'error_message' => null,
            'retry_count' => 0,
        ]);
    }

    /**
     * Indicate that the email failed to send.
     */
    public function failed(string $errorMessage = 'SMTP連接失敗'): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => LogConstants::EMAIL_STATUS_FAILED,
            'sent_at' => null,
            'error_message' => $errorMessage,
            'retry_count' => $this->faker->numberBetween(1, 3),
        ]);
    }

    /**
     * Indicate that the email is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => LogConstants::EMAIL_STATUS_PENDING,
            'sent_at' => null,
            'error_message' => null,
            'retry_count' => 0,
        ]);
    }

    /**
     * Indicate that the email bounced.
     */
    public function bounced(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => LogConstants::EMAIL_STATUS_BOUNCED,
            'sent_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'error_message' => '郵件退信',
            'retry_count' => 0,
        ]);
    }

    /**
     * Indicate that the email is of a specific type.
     */
    public function ofType(string $type): static
    {
        return $this->state(fn (array $attributes) => [
            'email_type' => $type,
        ]);
    }

    /**
     * Indicate that the email is an invitation.
     */
    public function invitation(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_type' => LogConstants::EMAIL_TYPE_INVITATION,
            'subject' => '推薦函邀請 - ' . $this->faker->company(),
            'content' => '推薦函邀請信內容',
        ]);
    }

    /**
     * Indicate that the email is a reminder.
     */
    public function reminder(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_type' => LogConstants::EMAIL_TYPE_REMINDER,
            'subject' => '推薦函提醒 - ' . $this->faker->company(),
            'content' => '推薦函提醒信內容',
        ]);
    }

    /**
     * Indicate that the email is retryable.
     */
    public function retryable(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => LogConstants::EMAIL_STATUS_FAILED,
            'retry_count' => $this->faker->numberBetween(0, 2), // 小於3次，可重試
            'error_message' => 'Temporary failure',
        ]);
    }

    /**
     * Indicate that the email is recent.
     */
    public function recent(int $hours = 24): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween("-{$hours} hours", 'now'),
        ]);
    }
}
