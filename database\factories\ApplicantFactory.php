<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Applicant>
 */
class ApplicantFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory()->applicant(),
            'external_uid' => fake()->optional()->uuid(),
            'exam_id' => 'EXAM' . fake()->numberBetween(1, 999),
            'exam_year' => fake()->numberBetween(2020, 2025),
            'phone' => fake()->optional()->phoneNumber(),
        ];
    }

    /**
     * Create an applicant with a specific user.
     */
    public function forUser(User $user): static
    {
        return $this->state(fn(array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Create an applicant with LINE external UID.
     */
    public function withLineUid(): static
    {
        return $this->state(fn(array $attributes) => [
            'external_uid' => 'line_' . fake()->uuid(),
        ]);
    }
}
