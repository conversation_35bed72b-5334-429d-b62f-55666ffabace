<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\QuestionnaireTemplate;
use App\Models\SystemSetting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Tests\TestCase;

class SubmissionControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // 初始化系統設定
        SystemSetting::initializeDefaults();
    }

    /**
     * 測試提交管理頁面是否正常載入
     */
    public function test_submission_management_page_loads()
    {
        // 創建管理員用戶
        $admin = User::factory()->create([
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        // 創建測試問卷模板
        QuestionnaireTemplate::create([
            'department_name' => '資訊工程學系',
            'program_type' => '碩士班',
            'template_name' => '測試模板',
            'questions' => [
                [
                    'id' => 'q1',
                    'question' => '請評估考生的學術能力',
                    'type' => 'textarea',
                    'required' => true,
                ]
            ],
            'is_active' => true,
        ]);

        // 以管理員身份訪問頁面
        $response = $this->actingAs($admin)->get('/admin/submission');

        $response->assertStatus(200);
        $response->assertInertia(
            fn($page) =>
            $page->component('admin/submission-management')
                ->has('templates')
                ->has('submission_settings')
        );
    }

    /**
     * 測試更新提交方式設定
     */
    public function test_update_submission_settings()
    {
        $admin = User::factory()->create([
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($admin)->post('/admin/submission/submission-settings', [
            'allow_pdf_upload' => true,
            'allow_questionnaire_submission' => false,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // 驗證設定已更新
        $this->assertTrue(SystemSetting::isAllowPdfUpload());
        $this->assertFalse(SystemSetting::isAllowQuestionnaireSubmission());
    }

    /**
     * 測試不允許兩種提交方式都關閉
     */
    public function test_cannot_disable_both_submission_methods()
    {
        $admin = User::factory()->create([
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($admin)->post('/admin/submission/submission-settings', [
            'allow_pdf_upload' => false,
            'allow_questionnaire_submission' => false,
        ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors('submission_settings');
    }

    /**
     * 測試CSV模板上傳
     */
    public function test_upload_csv_template()
    {
        $admin = User::factory()->create([
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        // 創建測試CSV文件
        $csvContent = "question,type,required\n";
        $csvContent .= "請評估考生的學術能力,textarea,true\n";
        $csvContent .= "請評估考生的研究潛力,textarea,true\n";

        $csvFile = UploadedFile::fake()->createWithContent('test_template.csv', $csvContent);

        $response = $this->actingAs($admin)->post('/admin/submission/upload-csv', [
            'csv_file' => $csvFile,
            'department_name' => '電機工程學系',
            'program_type' => '博士班',
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // 驗證模板已創建
        $this->assertDatabaseHas('questionnaire_templates', [
            'department_name' => '電機工程學系',
            'program_type' => '博士班',
            'is_active' => true,
        ]);
    }

    /**
     * 測試問卷模板切換狀態
     */
    public function test_toggle_template_status()
    {
        $admin = User::factory()->create([
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        $template = QuestionnaireTemplate::create([
            'department_name' => '機械工程學系',
            'program_type' => '碩士班',
            'template_name' => '測試模板',
            'questions' => [],
            'is_active' => true,
        ]);

        $response = $this->actingAs($admin)->post("/admin/submission/toggle/{$template->id}");

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // 驗證狀態已切換
        $template->refresh();
        $this->assertFalse($template->is_active);
    }

    /**
     * 測試刪除問卷模板
     */
    public function test_delete_template()
    {
        $admin = User::factory()->create([
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        $template = QuestionnaireTemplate::create([
            'department_name' => '化學工程學系',
            'program_type' => '碩士班',
            'template_name' => '測試模板',
            'questions' => [],
            'is_active' => true,
        ]);

        $response = $this->actingAs($admin)->delete("/admin/submission/templates/{$template->id}");

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // 驗證模板已標記為非活動
        $template->refresh();
        $this->assertFalse($template->is_active);
    }

    /**
     * 測試非管理員無法訪問
     */
    public function test_non_admin_cannot_access()
    {
        $user = User::factory()->create([
            'role' => 'recommender',
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($user)->get('/admin/submission');

        $response->assertStatus(403);
    }

    /**
     * 測試獲取設定資訊
     */
    public function test_get_settings()
    {
        $admin = User::factory()->create([
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($admin)->get('/admin/submission/settings');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'submission_settings',
                'upload',
                'pdf',
                'storage_info'
            ]
        ]);
    }
}
