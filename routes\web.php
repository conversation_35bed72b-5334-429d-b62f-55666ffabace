<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\UserAgreementController;
use App\Http\Controllers\WelcomeController;
use App\Http\Controllers\Admin\SubmissionController;
use App\Http\Controllers\Admin\AdminRecommendationController;
use App\Http\Controllers\Admin\SystemSettingController;
use App\Http\Controllers\Admin\RecruitmentPeriodController;
use App\Http\Controllers\Admin\AdvancedSettingsController;
use App\Http\Controllers\Admin\LogsController;
use App\Http\Controllers\Admin\PdfMergeTaskController;
use App\Http\Controllers\Api\PdfMergeApiController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 主要 Web 路由
|--------------------------------------------------------------------------
|
| 這裡定義應用程式的主要 Web 路由，包括首頁、儀表板和使用者協議等核心功能
|
*/

// 首頁 - 系統歡迎頁面
Route::get('/', [WelcomeController::class, 'index'])->name('home');

// 認證後路由 (需要登入但不需要同意使用者協議)
Route::middleware(['auth', 'check.system.access'])->group(function () {
    Route::get('/user-agreement', [UserAgreementController::class, 'show'])->name('user-agreement.show'); // 使用者協議頁面
    Route::post('/user-agreement', [UserAgreementController::class, 'store'])->name('user-agreement.store'); // 使用者同意協議

    // 已同意使用者協議
    Route::middleware(['check.user.agreement'])->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard'); // 主儀表板 - 考生與推薦人主要在此頁面下進行作業
    });
});

/**
 * 管理員專用路由
 *
 * 後台管理頁面與相關功能
 * - auth: 確保使用者已登入
 * - role:admin: 確保使用者為管理員角色
 * - check.system.access: 確保使用者有權限訪問系統
 */
Route::middleware(['auth',  'role:admin', 'check.system.access'])->prefix('admin')->name('admin.')->group(function () {
    /** 推薦函管理 - 查看所有推薦函 */
    Route::get('/recommendations', [AdminRecommendationController::class, 'index'])->name('recommendations.index');

    /** 系統設定管理 */
    Route::prefix('system-settings')->name('system-settings.')->group(function () {
        Route::get('/', [SystemSettingController::class, 'index'])->name('index'); // 系統設定首頁
        Route::put('/', [SystemSettingController::class, 'update'])->name('update'); // 更新系統設定
        Route::put('/{key}', [SystemSettingController::class, 'updateSingle'])->name('update-single');
        Route::post('/reset-defaults', [SystemSettingController::class, 'resetDefaults'])->name('reset-defaults'); // 重置系統設定為預設值
        Route::post('/toggle-maintenance-mode', [SystemSettingController::class, 'toggleMaintenanceMode'])->name('toggle-maintenance-mode'); // 維護模式開關
        Route::post('/sync-external-data', [SystemSettingController::class, 'syncExternalData'])->name('sync-external-data'); // 同步外部數據
        Route::get('/check-api-connection', [SystemSettingController::class, 'checkApiConnection'])->name('check-api-connection'); // 檢查API連線
        Route::get('/status', [SystemSettingController::class, 'getSystemStatus'])->name('status'); // 取得系統狀態
    });

    /** 進階系統設定管理 */
    Route::prefix('advanced-settings')->name('advanced-settings.')->group(function () {
        Route::get('/', [AdvancedSettingsController::class, 'index'])->name('index'); // 進階設定首頁
        Route::post('/log', [AdvancedSettingsController::class, 'updateLogSettings'])->name('log.update'); // 更新 Log 設定
        Route::post('/upload', [AdvancedSettingsController::class, 'updateUploadSettings'])->name('upload.update'); // 更新上傳設定
        Route::post('/recommendation', [AdvancedSettingsController::class, 'updateRecommendationSettings'])->name('recommendation.update'); // 更新推薦函設定
    });

    /**
     * 提交管理
     *
     * 統一管理推薦函提交方式，包括問卷模板管理與提交方式設定
     * 整合原本的PDF管理和問卷模板功能
     */
    Route::prefix('submission')->name('submission.')->group(function () {
        Route::get('/', [SubmissionController::class, 'index'])->name('index'); // 提交管理主頁面
        Route::post('/submission-settings', [SubmissionController::class, 'updateSubmissionSettings'])->name('submission-settings'); // 更新提交方式設定
        Route::get('/settings', [SubmissionController::class, 'getSettings'])->name('settings'); // 取得設定資訊

        // 問卷模板管理
        Route::post('/toggle/{id}', [SubmissionController::class, 'toggleTemplate'])->name('toggle'); // 啟用/停用問卷模板
        Route::post('/upload-csv', [SubmissionController::class, 'uploadCsvTemplate'])->name('upload-csv'); // 上傳CSV模板
        Route::post('/save-template', [SubmissionController::class, 'saveTemplate'])->name('save-template'); // 儲存問卷模板
        Route::delete('/templates/{id}', [SubmissionController::class, 'destroyTemplate'])->name('templates.destroy'); // 刪除問卷模板
        Route::get('/template/{recommendationId}', [SubmissionController::class, 'getTemplate'])->name('template'); // 取得問卷模板

        // 資料合併作業(開發中，未實作)
        // Route::post('/start-merge', [SubmissionController::class, 'startDataMerge'])->name('start-merge'); // 開始資料合併作業
        // Route::get('/download-merge/{filename}', [SubmissionController::class, 'downloadMergeResult'])->name('download-merge'); // 下載合併結果
    });

    /** 招生期間管理 */
    Route::prefix('recruitment-periods')->name('recruitment-periods.')->group(function () {
        Route::get('/', [RecruitmentPeriodController::class, 'index'])->name('index'); // 招生期間列表
        Route::post('/sync', [RecruitmentPeriodController::class, 'sync'])->name('sync'); // 同步招生期間
        Route::post('/api-settings', [RecruitmentPeriodController::class, 'updateApiSettings'])->name('api-settings'); // 更新 API 設定
        Route::get('/{examId}', [RecruitmentPeriodController::class, 'show'])->name('show'); // 查看招生期間
        Route::get('/status/check', [RecruitmentPeriodController::class, 'checkStatus'])->name('check-status'); // 檢查招生期間狀態
        Route::post('/clear-cache', [RecruitmentPeriodController::class, 'clearCache'])->name('clear-cache'); // 清除招生期間快取
        Route::get('/export/data', [RecruitmentPeriodController::class, 'export'])->name('export'); // 匯出招生期間資料
    });

    /** 統一日誌管理 */
    Route::prefix('logs')->name('logs.')->group(function () {
        Route::get('/', [LogsController::class, 'index'])->name('index'); // 日誌管理首頁
        Route::get('/{id}', [LogsController::class, 'show'])->name('show'); // 查看日誌詳情
        Route::post('/cleanup', [LogsController::class, 'cleanup'])->name('cleanup'); // 清理日誌
        Route::post('/export', [LogsController::class, 'export'])->name('export'); // 匯出日誌
    });

    /** PDF壓縮任務管理 */
    Route::prefix('tasks')->name('tasks.')->group(function () {
        Route::get('/', [PdfMergeTaskController::class, 'index'])->name('index'); // 任務列表
        Route::get('/{task}', [PdfMergeTaskController::class, 'show'])->name('show'); // 查看任務詳情
        Route::get('/{task}/download', [PdfMergeTaskController::class, 'download'])->name('download'); // 下載任務結果
        Route::get('/{task}/logs', [PdfMergeTaskController::class, 'logs'])->name('logs'); // 查看任務日誌
        Route::post('/{task}/retry', [PdfMergeTaskController::class, 'retry'])->name('retry'); // 重試任務
        Route::post('/{task}/cancel', [PdfMergeTaskController::class, 'cancel'])->name('cancel'); // 取消任務
        Route::delete('/{task}', [PdfMergeTaskController::class, 'destroy'])->name('destroy'); // 刪除任務
        Route::get('/available-exams', [PdfMergeTaskController::class, 'getAvailableExams'])->name('available-exams'); // 獲取可用考試
        Route::post('/cleanup', [PdfMergeTaskController::class, 'cleanup'])->name('cleanup'); // 清理過期任務

        Route::post('/', [PdfMergeApiController::class, 'startMerge'])->name('start'); // 主動創建任務(測試、備用方案)
    });
});


/** 認證相關路由 (登入、登出) */
require __DIR__ . '/auth.php';

/** 推薦函功能相關路由 */
require __DIR__ . '/recommendation.php';
