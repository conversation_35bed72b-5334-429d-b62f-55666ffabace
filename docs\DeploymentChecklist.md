# 推薦函系統 - 部署檢查清單

## 🚀 部署前檢查

### 環境準備

- [ ] 確認 PHP 版本 >= 8.1
- [ ] 確認 MySQL 版本 >= 8.0
- [ ] 確認 Composer 已安裝
- [ ] 確認 Node.js >= 18 已安裝
- [ ] 確認 Web 伺服器 (Nginx/Apache) 已配置

### 代碼準備

- [ ] 檢查 `.env` 檔案配置
- [ ] 執行 `composer install --no-dev --optimize-autoloader`
- [ ] 執行 `npm ci --only=production`
- [ ] 執行 `npm run build`

### 資料庫準備

- [ ] 建立生產環境資料庫
- [ ] 設置資料庫用戶權限
- [ ] 執行 `php artisan migrate`
- [ ] 執行 `php artisan db:seed` (如需要)
- [ ] 備份現有資料 (如為更新部署)

## 🔧 部署步驟

### 1. 依賴安裝

```bash
# PHP 依賴
composer install --no-dev --optimize-autoloader

# 前端依賴與編譯
npm ci --only=production
npm run build
```

### 3. 環境配置

```bash
# 複製環境檔案
cp .env.production .env

# 生成應用金鑰
php artisan key:generate

# 建立儲存連結
php artisan storage:link
```

### 4. 資料庫設置

```bash
# 執行遷移
php artisan migrate --force

# 清除並重建快取
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache
```

### 5. 權限設置

```bash
# 設置檔案權限
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache
```

### 6. 排程設置

```bash
# 編輯 crontab
crontab -e

# 添加 Laravel 排程
* * * * * cd /path/to/project && php artisan schedule:run >> /dev/null 2>&1
```

### 7. 啟動任務佇列 or 需要任務時才啟動

```bash
# 啟動佇列工作
php artisan queue:work --daemon
```

## ✅ 部署後檢查

### 基本功能測試

- [ ] 網站首頁可正常訪問
- [ ] 用戶登入功能正常
- [ ] 資料庫連接正常
- [ ] 郵件發送功能正常
- [ ] 檔案上傳功能正常

### 系統檢查

```bash
# 檢查 Laravel 狀態
php artisan about

# 檢查資料庫連接
php artisan migrate:status

# 檢查排程任務
php artisan schedule:list

# 測試郵件功能
php artisan test:email-log

# 檢查 Log 系統
php artisan log:stats --days=1
```

### 效能檢查

- [ ] 頁面載入時間 < 3 秒
- [ ] 資料庫查詢效能正常
- [ ] 快取功能運作正常
- [ ] 靜態資源載入正常

### 安全檢查

- [ ] HTTPS 憑證有效
- [ ] 敏感檔案無法直接訪問
- [ ] 錯誤頁面不洩露敏感資訊
- [ ] 資料庫連接使用安全憑證
- [ ] 檔案上傳限制正常

## 🔄 更新部署流程

### 準備階段

- [ ] 備份現有資料庫
- [ ] 備份現有檔案
- [ ] 通知用戶系統維護
- [ ] 啟用維護模式 `php artisan down`

### 完成階段

- [ ] 關閉維護模式 `php artisan up`
- [ ] 執行部署後檢查
- [ ] 監控系統運行狀況
- [ ] 通知用戶系統恢復
