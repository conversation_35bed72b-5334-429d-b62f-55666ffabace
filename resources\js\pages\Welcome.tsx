import { AppFooter } from '@/components/AppFooter';
import AppLogo from '@/components/AppLogo';
import { LanguageSwitcher } from '@/components/LanguageSwitcher';
import { useLanguage } from '@/hooks/useLanguage';
import { Head } from '@inertiajs/react';

// 推薦函系統首頁 - 現代化設計，專為推薦函系統定制
export default function Welcome() {
    const { t } = useLanguage();
    const exam_url = import.meta.env.VITE_EXAM_BASE_URL;
    return (
        <>
            <Head title={t('通用.系統名稱')}>
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700" rel="stylesheet" />
            </Head>

            <div className="flex min-h-screen flex-col">
                {/* 導航欄 */}
                <header className="relative z-10 border-b border-gray-200/50 bg-white/80 backdrop-blur-sm">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex h-16 items-center justify-between">
                            {/* Logo 區域 */}
                            <div className="flex items-center space-x-3">
                                <AppLogo />
                            </div>
                            {/* 右側導航 */}
                            <div className="flex items-center space-x-4">
                                <LanguageSwitcher />
                            </div>
                        </div>
                    </div>
                </header>

                {/* 主內容區域，撐開空間 */}
                <main className="flex-1 bg-gradient-to-r from-blue-50 to-green-50">
                    <div className="relative h-full overflow-hidden">
                        <div className="mx-auto max-w-7xl px-4 py-24 sm:px-6 lg:px-8">
                            <div className="text-center">
                                <h1 className="mb-6 text-4xl font-bold text-gray-900 md:text-6xl">
                                    <span className="bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent">
                                        {t('通用.系統名稱')}
                                    </span>
                                </h1>
                                <p className="mx-auto mb-8 max-w-3xl text-xl leading-relaxed text-gray-600">{t('歡迎頁面.通用.說明')}</p>

                                {/* 入口卡片 */}
                                <div className="mx-auto grid max-w-4xl gap-8 md:grid-cols-2">
                                    {/* 考生入口 */}
                                    <div className="transform rounded-2xl border border-gray-200 bg-white p-8 shadow-md transition-all duration-300 hover:-translate-y-1 hover:shadow-xl">
                                        <div className="text-center">
                                            <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-emerald-400 to-emerald-600 shadow-md">
                                                <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth={2}
                                                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                                                    />
                                                </svg>
                                            </div>
                                            <h3 className="mb-2 text-2xl font-semibold text-gray-900">{t('歡迎頁面.考生.標題')}</h3>
                                            <p className="mb-6 text-sm text-gray-600">{t('歡迎頁面.考生.說明')}</p>
                                            <a
                                                href={exam_url}
                                                target="_blank"
                                                className="inline-flex items-center rounded-md bg-emerald-600 px-4 py-2 text-sm font-medium text-white transition duration-200 hover:bg-emerald-700 focus:ring-2 focus:ring-emerald-400 focus:outline-none"
                                            >
                                                {t('歡迎頁面.通用.前往報名系統')}
                                            </a>
                                        </div>
                                    </div>

                                    {/* 推薦人入口 */}
                                    <div className="transform rounded-2xl border border-gray-200 bg-white p-8 shadow-md transition-all duration-300 hover:-translate-y-1 hover:shadow-xl">
                                        <div className="text-center">
                                            <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-sky-500 to-sky-700 shadow-md">
                                                <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth={2}
                                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                                    />
                                                </svg>
                                            </div>
                                            <h3 className="mb-2 text-2xl font-semibold text-gray-900">{t('歡迎頁面.推薦人.標題')}</h3>
                                            <p className="mb-6 text-sm text-gray-600"> {t('歡迎頁面.推薦人.說明')}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>

                {/* 頁尾 */}
                <AppFooter />
            </div>
        </>
    );
}
