<?php

namespace App\Console\Commands;

use App\Models\RecommendationLetter;
use App\Models\Applicant;
use App\Services\LogService;
use App\Enums\LogConstants;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class RecommendationReportCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'recommendations:generate-report 
                            {--days=30 : 報告期間天數}
                            {--format=table : 輸出格式 (table|csv|json)}
                            {--output= : 輸出檔案路徑}
                            {--type=summary : 報告類型 (summary|detailed|stats)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '生成推薦函系統報告';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        $format = $this->option('format');
        $output = $this->option('output');
        $type = $this->option('type');

        $this->info("📊 生成最近 {$days} 天的推薦函報告...");

        $data = $this->generateReportData($days, $type);

        if ($format === 'table') {
            $this->displayTableReport($data, $type);
        } else {
            $this->exportReport($data, $format, $output, $type, $days);
        }

        // 記錄操作
        LogService::operationSuccess(
            '生成推薦函報告',
            [
                'days' => $days,
                'format' => $format,
                'type' => $type,
                'output' => $output
            ],
            LogConstants::ACTION_EXPORT
        );

        return 0;
    }

    /**
     * 生成報告資料
     */
    private function generateReportData(int $days, string $type): array
    {
        $startDate = now()->subDays($days);

        $data = [
            'period' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => now()->format('Y-m-d'),
                'days' => $days
            ]
        ];

        switch ($type) {
            case 'summary':
                $data['summary'] = $this->getSummaryData($startDate);
                break;
            case 'detailed':
                $data['detailed'] = $this->getDetailedData($startDate);
                break;
            case 'stats':
                $data['statistics'] = $this->getStatisticsData($startDate);
                break;
        }

        return $data;
    }

    /**
     * 取得摘要資料
     */
    private function getSummaryData($startDate): array
    {
        return [
            'total_recommendations' => RecommendationLetter::where('created_at', '>=', $startDate)->count(),
            'completed_recommendations' => RecommendationLetter::where('created_at', '>=', $startDate)
                ->where('status', 'completed')->count(),
            'pending_recommendations' => RecommendationLetter::where('created_at', '>=', $startDate)
                ->where('status', 'pending')->count(),
            'overdue_recommendations' => RecommendationLetter::where('created_at', '>=', $startDate)
                ->where('status', 'overdue')->count(),
            'new_applicants' => Applicant::where('created_at', '>=', $startDate)->count(),
            'active_recommenders' => RecommendationLetter::where('created_at', '>=', $startDate)
                ->distinct('recommender_email')->count('recommender_email')
        ];
    }

    /**
     * 取得詳細資料
     */
    private function getDetailedData($startDate): array
    {
        $recommendations = RecommendationLetter::with(['applicant'])
            ->where('created_at', '>=', $startDate)
            ->orderBy('created_at', 'desc')
            ->get();

        return $recommendations->map(function ($rec) {
            return [
                'id' => $rec->id,
                'applicant_name' => $rec->applicant->name ?? 'N/A',
                'recommender_email' => $rec->recommender_email,
                'status' => $rec->status,
                'created_at' => $rec->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $rec->updated_at->format('Y-m-d H:i:s'),
                'days_since_created' => $rec->created_at->diffInDays(now())
            ];
        })->toArray();
    }

    /**
     * 取得統計資料
     */
    private function getStatisticsData($startDate): array
    {
        $recommendations = RecommendationLetter::where('created_at', '>=', $startDate);

        return [
            'status_distribution' => $recommendations->clone()
                ->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray(),
            'daily_creation' => $recommendations->clone()
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->orderBy('date')
                ->pluck('count', 'date')
                ->toArray(),
            'completion_rate' => $this->calculateCompletionRate($startDate),
            'average_completion_time' => $this->calculateAverageCompletionTime($startDate)
        ];
    }

    /**
     * 計算完成率
     */
    private function calculateCompletionRate($startDate): float
    {
        $total = RecommendationLetter::where('created_at', '>=', $startDate)->count();
        $completed = RecommendationLetter::where('created_at', '>=', $startDate)
            ->where('status', 'completed')->count();

        return $total > 0 ? round(($completed / $total) * 100, 2) : 0;
    }

    /**
     * 計算平均完成時間
     */
    private function calculateAverageCompletionTime($startDate): ?float
    {
        $completedRecs = RecommendationLetter::where('created_at', '>=', $startDate)
            ->where('status', 'completed')
            ->whereNotNull('completed_at')
            ->get();

        if ($completedRecs->isEmpty()) {
            return null;
        }

        $totalDays = $completedRecs->sum(function ($rec) {
            return $rec->created_at->diffInDays($rec->completed_at);
        });

        return round($totalDays / $completedRecs->count(), 2);
    }

    /**
     * 顯示表格報告
     */
    private function displayTableReport(array $data, string $type): void
    {
        $this->info("📋 推薦函系統報告");
        $this->line("期間: {$data['period']['start_date']} 至 {$data['period']['end_date']}");
        $this->line("");

        switch ($type) {
            case 'summary':
                $this->displaySummaryTable($data['summary']);
                break;
            case 'detailed':
                $this->displayDetailedTable($data['detailed']);
                break;
            case 'stats':
                $this->displayStatisticsTable($data['statistics']);
                break;
        }
    }

    /**
     * 顯示摘要表格
     */
    private function displaySummaryTable(array $summary): void
    {
        $this->table(
            ['項目', '數量'],
            [
                ['總推薦函數', $summary['total_recommendations']],
                ['已完成', $summary['completed_recommendations']],
                ['待處理', $summary['pending_recommendations']],
                ['逾期', $summary['overdue_recommendations']],
                ['新申請人', $summary['new_applicants']],
                ['活躍推薦人', $summary['active_recommenders']]
            ]
        );
    }

    /**
     * 顯示詳細表格
     */
    private function displayDetailedTable(array $detailed): void
    {
        if (empty($detailed)) {
            $this->info("沒有找到推薦函記錄");
            return;
        }

        $headers = ['ID', '申請人', '推薦人信箱', '狀態', '建立時間', '天數'];
        $rows = array_map(function ($item) {
            return [
                $item['id'],
                $item['applicant_name'],
                $item['recommender_email'],
                $item['status'],
                $item['created_at'],
                $item['days_since_created']
            ];
        }, array_slice($detailed, 0, 20)); // 限制顯示 20 筆

        $this->table($headers, $rows);

        if (count($detailed) > 20) {
            $this->info("... 還有 " . (count($detailed) - 20) . " 筆記錄");
        }
    }

    /**
     * 顯示統計表格
     */
    private function displayStatisticsTable(array $stats): void
    {
        $this->info("📊 狀態分布:");
        foreach ($stats['status_distribution'] as $status => $count) {
            $this->line("  {$status}: {$count}");
        }

        $this->info("\n📈 完成率: {$stats['completion_rate']}%");

        if ($stats['average_completion_time']) {
            $this->info("⏱️  平均完成時間: {$stats['average_completion_time']} 天");
        }
    }

    /**
     * 匯出報告
     */
    private function exportReport(array $data, string $format, ?string $output, string $type, int $days): void
    {
        if (!$output) {
            $timestamp = now()->format('Y-m-d_H-i-s');
            $output = "reports/recommendation_report_{$type}_{$timestamp}.{$format}";
        }

        $content = $format === 'json'
            ? json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
            : $this->convertToCsv($data, $type);

        Storage::put($output, $content);

        $this->info("✅ 報告已匯出到: " . Storage::path($output));
    }

    /**
     * 轉換為 CSV 格式
     */
    private function convertToCsv(array $data, string $type): string
    {
        // 簡化的 CSV 轉換，實際應用中可能需要更複雜的邏輯
        $csv = "推薦函系統報告\n";
        $csv .= "期間,{$data['period']['start_date']} 至 {$data['period']['end_date']}\n\n";

        if ($type === 'summary' && isset($data['summary'])) {
            $csv .= "項目,數量\n";
            foreach ($data['summary'] as $key => $value) {
                $csv .= "{$key},{$value}\n";
            }
        }

        return $csv;
    }
}
