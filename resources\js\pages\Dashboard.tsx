import AppLayout from '@/layouts/AppLayout';
import { SharedData } from '@/types';
import { usePage } from '@inertiajs/react';
import DashboardAdmin from './dashboard/DashboardAdmin';
import DashboardApplicant from './dashboard/DashboardApplicant';
import DashboardRecommender from './dashboard/DashboardRecommender';

const breadcrumbs = [
    {
        title: 'common.dashboard',
        href: '/dashboard',
    },
];

export default function Dashboard() {
    const { auth, recommendations } = usePage<SharedData>().props;

    const role = auth?.user?.role;

    let content;
    switch (role) {
        case 'admin':
            content = <DashboardAdmin recommendations={recommendations} />;
            break;
        case 'applicant':
            content = <DashboardApplicant />;
            break;
        case 'recommender':
            content = <DashboardRecommender />;
            break;
        default:
            content = <p>Unknown user role. Cannot display dashboard.</p>;
    }

    return <AppLayout breadcrumbs={breadcrumbs}>{content}</AppLayout>;
}



