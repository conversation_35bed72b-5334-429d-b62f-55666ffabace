import { Button } from '@/components/ui/Button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/DropdownMenu';
import { useLanguage, type Language } from '@/hooks/useLanguage';
import { Languages } from 'lucide-react';

const languageOptions: { value: Language; label: string; flag: string }[] = [
    { value: 'zh-TW', label: '繁體中文', flag: '🇹🇼' },
    { value: 'en', label: 'English', flag: '🇺🇸' },
];

/**
 * 語言切換器組件(中英文切換)
 */
export function LanguageSwitcher() {
    const { language, setLanguage } = useLanguage();

    const handleLanguageChange = (newLanguage: Language) => {
        if (newLanguage !== language) {
            setLanguage(newLanguage);
            // 刷新頁面以確保狀態完全更新
            setTimeout(() => {
                window.location.reload();
            }, 100);
        }
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 rounded-full p-0">
                    <Languages className="h-4 w-4" />
                    <span className="sr-only">Switch language</span>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-40">
                {languageOptions.map((option) => (
                    <DropdownMenuItem
                        key={option.value}
                        onClick={() => handleLanguageChange(option.value)}
                        className={`m-2 flex items-center ${language === option.value ? 'bg-accent' : ''}`}
                    >
                        <span className="text-base">{option.flag}</span>
                        <span className="text-sm">{option.label}</span>
                        {language === option.value && <span className="ml-auto text-xs text-muted-foreground">✓</span>}
                    </DropdownMenuItem>
                ))}
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
