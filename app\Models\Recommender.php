<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

/**
 * 推薦人模型
 *
 * 管理推薦人的詳細資訊、登入憑證和推薦函關聯
 */
class Recommender extends Model
{
    use HasFactory;

    /**
     * 可批量賦值的屬性
     *
     * @var array<string>
     */
    protected $fillable = [
        'user_id',          // 關聯的使用者 ID
        'email',            // 電子郵件
        'name',             // 姓名
        'title',            // 職稱
        'department',       // 所屬部門/機構
        'phone',            // 聯絡電話
        'login_token',      // 登入 Token
        'token_expires_at', // Token 過期時間
        'exam_year',        // 推薦年度
        'exam_id',          // 考試ID
        'last_login_at',    // 最後登入時間
        'is_active',        // 是否啟用
    ];

    /**
     * 屬性類型轉換
     *
     * @var array<string, string>
     */
    protected $casts = [
        'last_login_at' => 'datetime',
        'token_expires_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * 取得推薦人關聯的使用者
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 取得推薦人的所有推薦函
     *
     * @return HasMany
     */
    public function recommendationLetters(): HasMany
    {
        return $this->hasMany(RecommendationLetter::class);
    }

    /**
     * 產生新的唯一登入 Token
     *
     * @param int $expirationHours Token 有效期限（小時）
     * @return string
     */
    public function generateLoginToken(int $expirationHours = 168): string // 預設 7 天
    {
        $token = Str::random(60);

        $this->update([
            'login_token' => $token,
            'token_expires_at' => Carbon::now()->addHours($expirationHours),
        ]);

        return $token;
    }

    /**
     * 檢查 Token 是否有效
     *
     * @return bool
     */
    public function isTokenValid(): bool
    {
        // 若為 null 則為不失效的 token
        return $this->login_token &&
            (!$this->token_expires_at || $this->token_expires_at->isFuture());
    }

    /**
     * 清除登入 Token
     *
     * @return void
     */
    public function clearToken(): void
    {
        $this->update([
            'login_token' => null,
            'token_expires_at' => null,
        ]);
    }

    /**
     * 更新最後登入時間
     *
     * @return void
     */
    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }

    /**
     * 取得推薦人的顯示名稱
     *
     * @return string
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name ?: $this->email;
    }

    /**
     * 取得推薦人的完整職稱
     *
     * @return string
     */
    public function getFullTitleAttribute(): string
    {
        $parts = array_filter([$this->department, $this->title]);
        return implode(' ', $parts) ?: '推薦人';
    }

    /**
     * 檢查推薦人是否啟用
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * 靜態方法：產生唯一的登入 Token
     *
     * @return string
     */
    public static function generateUniqueToken(): string
    {
        do {
            $token = Str::random(60);
        } while (static::where('login_token', $token)->exists());

        return $token;
    }
}
