<?php

namespace App\Console\Commands;

use App\Models\EmailStatus;
use App\Models\RecommendationLetter;
use App\Services\EmailService;
use App\Services\ExternalMailService;
use Illuminate\Console\Command;

class CheckEmailSystem extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'email:check-system 
                            {--test-api : 測試外部 API 連線}
                            {--stats : 顯示郵件統計}
                            {--duplicates : 檢查重複記錄}';

    /**
     * The console command description.
     */
    protected $description = '檢查郵件系統的完整性和狀態';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 檢查郵件系統狀態...');

        if ($this->option('test-api')) {
            $this->testApiConnection();
        }

        if ($this->option('stats')) {
            $this->showEmailStats();
        }

        if ($this->option('duplicates')) {
            $this->checkDuplicates();
        }

        if (!$this->option('test-api') && !$this->option('stats') && !$this->option('duplicates')) {
            $this->showEmailStats();
            $this->checkDuplicates();
        }

        $this->info('✅ 檢查完成');
    }

    /**
     * 測試外部 API 連線
     */
    private function testApiConnection(): void
    {
        $this->info('🌐 測試外部郵件 API 連線...');
        
        $externalMailService = new ExternalMailService();
        $result = $externalMailService->testConnection();

        if ($result['success']) {
            $this->info("✅ API 連線成功");
            $this->line("   回應時間: {$result['response_time_ms']}ms");
            $this->line("   API 回應: " . substr($result['api_response'], 0, 100) . '...');
        } else {
            $this->error("❌ API 連線失敗");
            $this->line("   錯誤訊息: {$result['message']}");
        }
    }

    /**
     * 顯示郵件統計
     */
    private function showEmailStats(): void
    {
        $this->info('📊 郵件系統統計:');

        // 總體統計
        $totalEmails = EmailStatus::count();
        $sentEmails = EmailStatus::where('status', EmailStatus::STATUS_SENT)->count();
        $failedEmails = EmailStatus::where('status', EmailStatus::STATUS_FAILED)->count();
        $pendingEmails = EmailStatus::where('status', EmailStatus::STATUS_PENDING)->count();

        $this->table(['狀態', '數量', '百分比'], [
            ['總計', $totalEmails, '100%'],
            ['已發送', $sentEmails, $totalEmails > 0 ? round(($sentEmails / $totalEmails) * 100, 1) . '%' : '0%'],
            ['發送失敗', $failedEmails, $totalEmails > 0 ? round(($failedEmails / $totalEmails) * 100, 1) . '%' : '0%'],
            ['待發送', $pendingEmails, $totalEmails > 0 ? round(($pendingEmails / $totalEmails) * 100, 1) . '%' : '0%'],
        ]);

        // 按類型統計
        $this->line('');
        $this->info('📧 按郵件類型統計:');
        
        $typeStats = EmailStatus::selectRaw('email_type, status, COUNT(*) as count')
            ->groupBy('email_type', 'status')
            ->get()
            ->groupBy('email_type');

        foreach ($typeStats as $type => $statuses) {
            $this->line("  {$type}:");
            foreach ($statuses as $status) {
                $this->line("    {$status->status}: {$status->count}");
            }
        }

        // 最近發送統計
        $this->line('');
        $this->info('⏰ 最近24小時發送統計:');
        $recent24h = EmailStatus::where('last_sent_at', '>=', now()->subDay())->count();
        $recentFailed24h = EmailStatus::where('last_sent_at', '>=', now()->subDay())
            ->where('status', EmailStatus::STATUS_FAILED)->count();
        
        $this->line("  發送總數: {$recent24h}");
        $this->line("  失敗數量: {$recentFailed24h}");
    }

    /**
     * 檢查重複記錄
     */
    private function checkDuplicates(): void
    {
        $this->info('🔍 檢查重複記錄...');

        // 檢查是否有違反唯一約束的記錄
        $duplicates = EmailStatus::selectRaw('recommendation_letter_id, email_type, COUNT(*) as count')
            ->groupBy('recommendation_letter_id', 'email_type')
            ->having('count', '>', 1)
            ->get();

        if ($duplicates->isEmpty()) {
            $this->info('✅ 沒有發現重複記錄');
        } else {
            $this->warn("⚠️  發現 {$duplicates->count()} 組重複記錄:");
            foreach ($duplicates as $duplicate) {
                $this->line("  推薦函 ID: {$duplicate->recommendation_letter_id}, 類型: {$duplicate->email_type}, 重複數: {$duplicate->count}");
            }
        }

        // 檢查孤立記錄（推薦函已被刪除）
        $orphaned = EmailStatus::leftJoin('recommendation_letters', 'email_status.recommendation_letter_id', '=', 'recommendation_letters.id')
            ->whereNull('recommendation_letters.id')
            ->count();

        if ($orphaned > 0) {
            $this->warn("⚠️  發現 {$orphaned} 筆孤立記錄（推薦函已被刪除）");
        } else {
            $this->info('✅ 沒有發現孤立記錄');
        }
    }
}
