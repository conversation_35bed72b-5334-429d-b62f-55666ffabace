<?php

namespace App\Console\Commands;

use App\Services\LogService;
use App\Enums\LogConstants;
use Illuminate\Console\Command;

class TestEmailLog extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:email-log';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '測試郵件日誌功能';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 開始測試郵件日誌功能...');

        try {
            // 測試1: 郵件發送成功記錄
            $this->info('📧 測試郵件發送成功記錄...');
            $log = LogService::emailSent(
                '<EMAIL>',
                '推薦函邀請測試',
                LogConstants::EMAIL_TYPE_INVITATION,
                ['test' => true],
                '這是一個測試郵件內容'
            );

            if ($log) {
                $this->info("✅ 郵件成功記錄已創建！");
                $this->line("   ID: {$log->id}");
                $this->line("   收件人: {$log->recipient_email}");
                $this->line("   主旨: {$log->subject}");
                $this->line("   類型: {$log->email_type}");
                $this->line("   狀態: {$log->status}");
                $this->line("   內容: {$log->content}");
            } else {
                $this->error("❌ 郵件成功記錄創建失敗");
                return 1;
            }

            // 測試2: 郵件發送失敗記錄
            $this->info('📧 測試郵件發送失敗記錄...');
            $failedLog = LogService::emailFailed(
                '<EMAIL>',
                '推薦函提醒測試',
                'SMTP連接超時',
                LogConstants::EMAIL_TYPE_REMINDER,
                ['retry_count' => 1]
            );

            if ($failedLog) {
                $this->info("✅ 郵件失敗記錄已創建！");
                $this->line("   ID: {$failedLog->id}");
                $this->line("   收件人: {$failedLog->recipient_email}");
                $this->line("   主旨: {$failedLog->subject}");
                $this->line("   類型: {$failedLog->email_type}");
                $this->line("   狀態: {$failedLog->status}");
                $this->line("   錯誤: {$failedLog->error_message}");
                $this->line("   內容: {$failedLog->content}");
            } else {
                $this->error("❌ 郵件失敗記錄創建失敗");
                return 1;
            }

            // 測試3: 預設內容生成
            $this->info('📧 測試預設內容生成...');
            $defaultLog = LogService::emailSent(
                '<EMAIL>',
                '系統通知測試',
                LogConstants::EMAIL_TYPE_NOTIFICATION
            );

            if ($defaultLog) {
                $this->info("✅ 預設內容記錄已創建！");
                $this->line("   ID: {$defaultLog->id}");
                $this->line("   自動生成的內容: {$defaultLog->content}");
            } else {
                $this->error("❌ 預設內容記錄創建失敗");
                return 1;
            }

            // 測試4: 統計資訊
            $this->info('📊 測試統計功能...');
            $stats = LogService::getLogStatistics(\App\Models\EmailLog::class, 1);
            $this->info("✅ 統計資訊:");
            $this->line("   今日郵件總數: {$stats['total_logs']}");

            if (isset($stats['by_status'])) {
                foreach ($stats['by_status'] as $status => $count) {
                    $this->line("   {$status}: {$count}");
                }
            }

            $this->info('🎉 所有測試完成！郵件日誌功能正常運作。');
            return 0;

        } catch (\Exception $e) {
            $this->error("❌ 測試過程中發生錯誤: " . $e->getMessage());
            $this->line("錯誤詳情: " . $e->getTraceAsString());
            return 1;
        }
    }
}
