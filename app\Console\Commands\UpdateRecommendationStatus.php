<?php

namespace App\Console\Commands;

use App\Models\RecommendationLetter;
use App\Models\SystemSetting;
use App\Services\EmailService;
use App\Services\LogService;
use App\Enums\LogConstants;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

/**
 * 自動更新推薦函狀態命令
 *
 * 處理超時推薦函的狀態更新和通知
 */
class UpdateRecommendationStatus extends Command
{
    /**
     * 命令名稱和簽名
     *
     * @var string
     */
    protected $signature = 'recommendations:update-status 
                            {--dry-run : 只顯示將要更新的記錄，不實際執行}
                            {--timeout-days= : 覆蓋系統設定的超時天數}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '自動更新超時推薦函的狀態';

    /**
     * 執行命令
     *
     * @return int
     */
    public function handle(): int
    {
        $this->info('開始檢查推薦函狀態...');

        try {
            $isDryRun = $this->option('dry-run');
            $timeoutDays = $this->option('timeout-days') ?? SystemSetting::getAutoTimeoutDays();

            $this->info("使用超時天數: {$timeoutDays} 天");

            if ($isDryRun) {
                $this->warn('*** 這是預覽模式，不會實際更新資料 ***');
            }

            // 處理超時的推薦函
            $timeoutResults = $this->processTimeoutRecommendations($timeoutDays, $isDryRun);

            // 處理需要發送提醒的推薦函
            $reminderResults = $this->processReminderRecommendations($isDryRun);

            // 顯示結果
            $this->displayResults($timeoutResults, $reminderResults, $isDryRun);

            // 記錄系統日誌
            if (!$isDryRun) {
                LogService::system(
                    [
                    'command' => 'auto_status_update',
                    'timeout_days' => $timeoutDays,
          ],
                    [
                    'timeout_updated' => $timeoutResults['updated'],
                    'reminders_sent' => $reminderResults['sent'],
          ],
                    200
                );
            }

            $this->info('推薦函狀態檢查完成');
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('執行過程中發生錯誤: ' . $e->getMessage());

            LogService::system(
                [
                'command' => 'auto_status_update',
                'error' => $e->getMessage(),
                'timeout_days' => $timeoutDays ?? null,
                'dry_run' => $isDryRun ?? false,
        ],
                [],
                500
            );

            return Command::FAILURE;
        }
    }

    /**
     * 處理超時的推薦函
     *
     * @param int $timeoutDays
     * @param bool $isDryRun
     * @return array
     */
    private function processTimeoutRecommendations(int $timeoutDays, bool $isDryRun): array
    {
        $cutoffDate = now()->subDays($timeoutDays);

        $query = RecommendationLetter::where('status', RecommendationLetter::STATUS_PENDING)
          ->where('created_at', '<', $cutoffDate);

        $timeoutRecommendations = $query->get();
        $updated = 0;

        if ($timeoutRecommendations->isEmpty()) {
            $this->info('沒有找到需要標記為超時的推薦函');
            return ['found' => 0, 'updated' => 0];
        }

        $this->info("找到 {$timeoutRecommendations->count()} 筆超時的推薦函:");

        foreach ($timeoutRecommendations as $recommendation) {
            $daysOverdue = now()->diffInDays($recommendation->created_at);

            $this->line("  - ID: {$recommendation->id}, 考生: {$recommendation->applicant_id}, " .
              "推薦人: {$recommendation->recommender_email}, " .
              "建立時間: {$recommendation->created_at->format('Y-m-d H:i:s')}, " .
              "超時: {$daysOverdue} 天");

            if (!$isDryRun) {
                try {
                    DB::beginTransaction();

                    // 更新狀態為超時
                    $recommendation->update([
                      'status' => RecommendationLetter::STATUS_TIMEOUT,
                    ]);

                    // 記錄操作日誌
                    LogService::operationSuccess(
                        "推薦函自動標記為超時 (超過 {$timeoutDays} 天未處理)",
                        [
                        'recommendation_id' => $recommendation->id,
                        'days_overdue' => $daysOverdue,
                        'timeout_days' => $timeoutDays,
                        'previous_status' => RecommendationLetter::STATUS_PENDING,
            ],
                        LogConstants::ACTION_UPDATE
                    );

                    DB::commit();
                    $updated++;
                } catch (\Exception $e) {
                    DB::rollBack();
                    $this->error("  更新推薦函 ID {$recommendation->id} 失敗: " . $e->getMessage());

                    LogService::operationFailure(
                        "自動標記推薦函超時失敗 ID: {$recommendation->id}",
                        [
                        'recommendation_id' => $recommendation->id,
                        'error' => $e->getMessage()
            ],
                        LogConstants::LEVEL_ERROR,
                        LogConstants::ACTION_UPDATE
                    );
                }
            }
        }

        return [
          'found' => $timeoutRecommendations->count(),
          'updated' => $updated
        ];
    }

    /**
     * 處理需要發送提醒的推薦函
     *
     * @param bool $isDryRun
     * @return array
     */
    private function processReminderRecommendations(bool $isDryRun): array
    {
        $reminderCooldownHours = SystemSetting::getReminderCooldownHours();
        $reminderThresholdDays = 3; // 3天後開始自動提醒

        $cutoffDate = now()->subDays($reminderThresholdDays);
        $cooldownCutoff = now()->subHours($reminderCooldownHours);

        $query = RecommendationLetter::where('status', RecommendationLetter::STATUS_PENDING)
          ->where('created_at', '<', $cutoffDate)
          ->where(function ($q) use ($cooldownCutoff) {
              $q->whereNull('last_reminded_at')
                ->orWhere('last_reminded_at', '<', $cooldownCutoff);
          });

        $reminderRecommendations = $query->get();
        $sent = 0;

        if ($reminderRecommendations->isEmpty()) {
            $this->info('沒有找到需要發送提醒的推薦函');
            return ['found' => 0, 'sent' => 0];
        }

        $this->info("找到 {$reminderRecommendations->count()} 筆需要發送提醒的推薦函:");

        foreach ($reminderRecommendations as $recommendation) {
            $daysSinceCreated = now()->diffInDays($recommendation->created_at);
            $lastReminded = $recommendation->last_reminded_at
              ? $recommendation->last_reminded_at->format('Y-m-d H:i:s')
              : '從未提醒';

            $this->line("  - ID: {$recommendation->id}, 推薦人: {$recommendation->recommender_email}, " .
              "建立: {$daysSinceCreated} 天前, 上次提醒: {$lastReminded}");

            if (!$isDryRun) {
                try {
                    $emailService = new EmailService();
                    $success = $emailService->sendReminderEmail($recommendation);

                    if ($success) {
                        $recommendation->update([
                          'last_reminded_at' => now(),
                        ]);

                        LogService::operationSuccess(
                            "自動發送提醒郵件給推薦人",
                            [
                            'recommendation_id' => $recommendation->id,
                            'days_since_created' => $daysSinceCreated,
                            'previous_reminder' => $recommendation->last_reminded_at?->toDateTimeString(),
              ],
                            LogConstants::ACTION_UPDATE
                        );

                        $sent++;
                    } else {
                        $this->error("  發送提醒郵件失敗: {$recommendation->recommender_email}");
                    }
                } catch (\Exception $e) {
                    $this->error("  發送提醒郵件異常 ID {$recommendation->id}: " . $e->getMessage());

                    LogService::operationFailure(
                        "自動發送提醒郵件失敗 ID: {$recommendation->id}",
                        [
                        'recommendation_id' => $recommendation->id,
                        'error' => $e->getMessage()
            ],
                        LogConstants::LEVEL_ERROR,
                        LogConstants::ACTION_UPDATE
                    );
                }
            }
        }

        return [
          'found' => $reminderRecommendations->count(),
          'sent' => $sent
        ];
    }

    /**
     * 顯示執行結果
     *
     * @param array $timeoutResults
     * @param array $reminderResults
     * @param bool $isDryRun
     * @return void
     */
    private function displayResults(array $timeoutResults, array $reminderResults, bool $isDryRun): void
    {
        $this->newLine();
        $this->info('=== 執行結果 ===');

        if ($isDryRun) {
            $this->warn('預覽模式結果:');
            $this->line("  - 將標記為超時: {$timeoutResults['found']} 筆");
            $this->line("  - 將發送提醒: {$reminderResults['found']} 筆");
        } else {
            $this->info('實際執行結果:');
            $this->line("  - 已標記為超時: {$timeoutResults['updated']} / {$timeoutResults['found']} 筆");
            $this->line("  - 已發送提醒: {$reminderResults['sent']} / {$reminderResults['found']} 筆");
        }
    }
}
