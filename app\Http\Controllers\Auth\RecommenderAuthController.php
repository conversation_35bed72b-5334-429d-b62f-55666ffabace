<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Services\AuthenticationService;
use App\Services\LogService;
use App\Models\Recommender;
use App\Models\UserAgreement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 推薦人認證控制器
 *
 * 使用統一的 AuthenticationService 進行認證處理
 */
class RecommenderAuthController extends Controller
{
    protected AuthenticationService $authService;

    public function __construct(AuthenticationService $authService)
    {
        $this->authService = $authService;
    }

    /**
     * 透過 Token 進行跳轉登入
     *
     * 處理推薦人透過信件中的連結登入
     * 使用統一的 AuthenticationService 進行認證處理
     */
    public function authenticateWithToken(string $token, Request $request)
    {
        Log::debug('【RecommenderAuthController】authenticateWithToken', ['token' => substr($token, 0, 6) . '***', 'ip' => $request->ip(), 'user_agent' => $request->userAgent()]);

        // 強制登出當前使用者，確保乾淨的登入狀態
        $this->authService->logout();

        try {
            // [步驟 1] 依照 token 找出推薦人（不檢查過期 / 停用）
            $recommender = Recommender::where('login_token', $token)->first();

            if (!$recommender) {
                LogService::loginFailure('Token 無效');
                return $this->showAuthFailure('Token 無效');
            }

            if (!$recommender->is_active) {
                LogService::loginFailure('帳號停用');
                return $this->showAuthFailure('帳號已停用，請聯絡系統管理員');
            }

            if ($recommender->token_expires_at && $recommender->token_expires_at->isPast()) {
                LogService::loginFailure('Token 已過期');
                return $this->showAuthFailure('Token 已過期');
            }

            // [步驟 2] 呼叫統一認證服務，執行 Token 登入
            $result = $this->authService->loginRecommenderByToken(
                $token,
                $request->ip()
            );

            if (!$result['success']) {
                return $this->showAuthFailure($result['message']);
            }

            return redirect()->route('dashboard')->with('success', '歡迎使用推薦函系統！');
        } catch (\Exception $e) {
            // 系統級錯誤 → laravel.log
            Log::error('推薦人 Token 登入控制器異常', [
                'token' => substr($token, 0, 6) . '***',
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
            ]);

            // 系統級錯誤 → DB log
            LogService::system(
                ['token' => substr($token, 0, 6) . '***'],
                ['error' => $e->getMessage()],
                500
            );

            return $this->showAuthFailure('登入失敗，請稍後再試或聯繫系統管理員');
        }
    }

    /**
     * 顯示認證失敗頁面
     */
    private function showAuthFailure(string $message)
    {
        return inertia('auth/AuthFailure', [
            'message' => $message,
            'type' => 'recommender'
        ]);
    }

    /**
     * 更新推薦人個人資料
     */
    public function updateProfile(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return redirect()->route('login')->withErrors(['error' => '請先登入']);
            }

            // 驗證輸入資料
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'title' => 'nullable|string|max:255',
                'department' => 'nullable|string|max:255',
                'phone' => 'nullable|string|max:20',
            ]);

            // 更新用戶基本資料
            DB::table('users')
                ->where('id', $user->id)
                ->update([
                    'updated_at' => now(),
                ]);

            // 查找並更新推薦人資料
            $recommender = Recommender::where('user_id', $user->id)->first();
            if ($recommender) {
                $recommender->update([
                    'name' => $validated['name'],
                    'title' => $validated['title'],
                    'department' => $validated['department'],
                    'phone' => $validated['phone'],
                ]);
            }

            LogService::operationSuccess(
                '推薦人個人資料更新成功',
                ['input' => $validated]
            );

            return redirect()->route('dashboard')->with('success', '個人資料更新成功');
        } catch (\Exception $e) {
            LogService::operationFailure(
                '推薦人個人資料更新失敗',
                [
                    'input' => $request->all(),
                    'error' => $e->getMessage(),
                ]
            );

            return redirect()->route('dashboard')->withErrors(['error' => '個人資料更新失敗，請稍後再試或聯繫系統管理員']);
        }
    }
}
