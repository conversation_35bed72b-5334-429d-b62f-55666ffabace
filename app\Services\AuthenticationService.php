<?php

namespace App\Services;

use App\Models\User;
use App\Models\Applicant;
use App\Models\Recommender;
use App\Enums\LogConstants;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Http;

/**
 * 統一認證服務類
 */
class AuthenticationService
{
    /**
     * 查找或建立使用者帳號
     *
     * 根據使用者角色和 email 查找或建立使用者帳號
     * - 對於推薦人，使用 email 作為唯一識別
     * - 對於考生，使用 email 和姓名作為唯一識別
     *
     * @param array $userData 使用者資料
     * @return User
     */
    private function findOrCreateUser(array $userData): User
    {
        if ($userData['role'] === 'recommender') {
            $user = User::where('email', $userData['email'])->first();
        } elseif ($userData['role'] === 'applicant') {
            $user = User::where('email', $userData['email'])->where('name', $userData['name'])->first();
        }

        if (!$user) {
            $user = User::create([
                'name' => $userData['name'],
                'email' => $userData['email'],
                'role' => $userData['role'],
            ]);

            LogService::system(
                [
                    '建立新使用者',
                    [
                        'email' => $userData['email'],
                        'role' => $userData['role'],
                    ]
                ]
            );
        }

        return $user;
    }

    /**
     * 考生eapapi登入處理
     *
     * 將收到的 token 送回 API 系統交換所需的考生資料後，代理考生登入系統
     *
     * @param string $token eapapi Token
     * @param string $clientIp 客戶端 IP
     * @return array 登入結果
     */
    public function verifyTokenWithApi(string $token, string $clientIp): array
    {
        try {
            $userData = $this->fetchUserDataFromApiSystem($token);

            if (!$userData) {
                Log::error('無法從原系統取得使用者資訊', ['token' => $token, 'ip' => $clientIp]);
                throw new \Exception('無法從原系統取得使用者資訊');
            }

            // 解析必要的資料
            $exam_id = $userData['exam_id'] ?? null;
            $exam_year = $userData['exam_year'] ?? null;
            $external_uid = $userData['stu_idno'] ?? null;
            $applications = $userData['applications'] ?? []; // 取出考生報名資料

            // 先查找是否已存在考生記錄
            $applicant = $this->findOrCreateApplicant($external_uid, $exam_year, $exam_id, $userData);
            $user = $applicant->user;

            $this->performLogin($user, [
                'applicant_id' => $applicant->id,
                'exam_year' => $exam_year,
                'exam_id' => $exam_id,
                'applications' => $applications, // 考生報名資料
            ]);

            return [
                'success' => true,
                'message' => '登入成功',
                'user' => $user,
                'applicant' => $applicant,
            ];
        } catch (\Exception $e) {
            Log::error('考生eapapi登入失敗', [
                'error' => $e->getMessage(),
                'ip' => $clientIp,
            ]);

            // 記錄登入失敗
            LogService::loginFailure($e->getMessage());

            return [
                'success' => false,
                'message' => '登入失敗：' . $e->getMessage(),
            ];
        }
    }


    /**
     * 從 eapapi 系統獲取使用者資料
     */
    private function fetchUserDataFromApiSystem(string $token): array
    {
        $apiBaseUrl = config('api.eapapi_base_url');
        $apiSecret = config('api.eapapi_secret');
        $apiUrl = $apiBaseUrl . config('api.endpoints.get_applicant_data');

        try {
            // 發送 POST 請求到 API 系統
            $response = Http::asForm()->post($apiUrl, [
                'token' => $token,
                'nuu_api_key' => $apiSecret,
                'nuu_api_id' => 'get_applicant_data',
            ]);

            if (!$response->successful()) {
                Log::error('API 請求失敗', [
                    'status_code' => $response->status(),
                    'token' => substr($token, 0, 20) . '...',
                ]);
                throw new \Exception('API 請求失敗，狀態碼：' . $response->status());
            }

            // 解析 JSON 回應
            $data = $response->json();

            if (!isset($data['status']) || $data['status'] !== 'success') {
                Log::error('API 回傳非 success', [
                    'response' => $data,
                    'token' => substr($token, 0, 20) . '...',
                ]);
                throw new \Exception('API 回傳非 success：' . json_encode($data));
            }

            return $data['data']; // 學生資料陣列
        } catch (\Exception $e) {
            Log::error('API 請求錯誤', [
                'error' => $e->getMessage(),
                'token' => substr($token, 0, 20) . '...',
            ]);
            LogService::system(
                [
                    'API 請求錯誤',
                    [
                        'token' => substr($token, 0, 20) . '...',
                        'error' => $e->getMessage(),
                    ]
                ],
                [
                    'token' => substr($token, 0, 20) . '...',
                ],
                $response->status()
            );
            throw new \Exception('無法從 API 系統取得使用者資料');
        }
    }

    /**
     * 推薦人 Token 登入處理
     *
     * @param string $token 登入 Token
     * @param string $clientIp 客戶端 IP
     * @return array 登入結果
     */
    public function loginRecommenderByToken(string $token, string $clientIp): array
    {
        try {
            // 查找推薦人
            $recommender = Recommender::where('login_token', $token)
                ->where(function ($query) {
                    $query->whereNull('token_expires_at')
                        ->orWhere('token_expires_at', '>', now());
                })
                ->first();

            // 2. 查找或建立使用者帳號
            $user = $this->findOrCreateUser([
                'email' => $recommender->email,
                'name' => $recommender->name,
                'role' => 'recommender',
            ]);

            // 3. 確保推薦人與使用者關聯
            if (!$recommender->user_id) {
                $recommender->update(['user_id' => $user->id]);
            }

            // 4. 更新最後登入時間
            $recommender->updateLastLogin();

            // 5. 執行登入並設定 session
            $this->performLogin($user, [
                'login_token' => $token,
                'recommender_id' => $recommender->id,
                'exam_year' => $recommender->exam_year,
                'exam_id' => $recommender->exam_id,
            ]);

            return [
                'success' => true,
                'user' => $user,
                'recommender' => $recommender,
                'message' => '登入成功',
            ];
        } catch (\Exception $e) {
            Log::error('推薦人登入異常', [
                'token' => substr($token, 0, 20) . '...',
                'ip' => $clientIp,
                'error' => $e->getMessage(),
            ]);

            LogService::loginFailure($e->getMessage());

            return [
                'success' => false,
                'message' => '登入失敗：' . $e->getMessage(),
            ];
        }
    }

    /**
     * 查找或建立考生記錄
     *
     * @param string $external_uid 加密的考生ID
     * @param string $exam_year 學年度
     * @param string $exam_id 招生類別ID
     * @param array $userData 使用者資料
     * @return Applicant 考生記錄
     */
    private function findOrCreateApplicant(string $external_uid, string $exam_year, string $exam_id, array $userData): Applicant
    {
        // 建立或取得使用者帳號
        $user = $this->findOrCreateUser([
            'name' => $userData['stu_name'],
            'email' => $userData['stu_e_mail'],
            'role' => 'applicant',
        ]);

        // 用 user_id + exam_id + exam_year 查找 applicant
        $applicant = Applicant::where([
            'exam_id'   => $exam_id,
            'exam_year' => $exam_year,
            'user_id'   => $user->id,
        ])->first();

        // 如果已存在，直接回傳
        if ($applicant) {
            return $applicant;
        }

        // 若無，建立 applicant
        $applicant = Applicant::create([
            'user_id'      => $user->id,
            'external_uid' => $external_uid,
            'exam_year'    => $exam_year,
            'exam_id'      => $exam_id,
            'phone'        => $userData['stu_cell_phone'] ?? null,
        ]);

        return $applicant;
    }

    /**
     * 執行登入並設定 session
     *
     * @param User $user 使用者物件
     * @param array $sessionData 額外的 session 資料
     * @return void
     */
    private function performLogin(User $user, array $sessionData = []): void
    {
        // 執行 Laravel 認證登入，並記錄
        Auth::login($user);

        LogService::loginSuccess(Auth::id());

        // 重新生成 session ID 以防止 session fixation 攻擊
        if (request()->hasSession()) {
            request()->session()->regenerate();
        }

        // 設定額外的 session 資料
        foreach ($sessionData as $key => $value) {
            Session::put($key, $value);
        }
    }

    /**
     * 統一登出處理
     *
     * @param string $userType 使用者類型 (applicant|recommender|admin)
     * @return void
     */
    public function logout()
    {
        // 不管身分，統一清除所有相關 session 資料
        Session::forget([
            'applicant_id',
            'external_uid',
            'recommender_id',
            'login_token',
        ]);

        // 標準登出流程
        Auth::logout();
        request()->session()->invalidate();
        request()->session()->regenerateToken();

        LogService::operationSuccess(
            '用戶登出',
            [
                'user_id' => Auth::id(),
                'user_role' => Auth::user()?->role,
                'session_data_cleared' => ['applicant_id', 'external_uid', 'recommender_id', 'login_token']
            ],
            LogConstants::ACTION_LOGOUT
        );
        return redirect()->route('home')->with('success', '您已成功登出。');
    }
}
