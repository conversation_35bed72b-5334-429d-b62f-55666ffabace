import React from 'react';
import { Head } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { ArrowLeft, FileText, Clock, AlertCircle, Info, XCircle } from 'lucide-react';

interface Task {
    id: number;
    task_id: string;
    status: string;
    progress: number;
    total_files: number;
    processed_files: number;
    created_at: string;
    updated_at: string;
    expires_at?: string;
    error_message?: string;
    parameters?: {
        exam_id?: string;
        exam_year?: number;
    };
}

interface PdfMergeTaskLogsProps {
    task: Task;
    logs: string[];
    breadcrumbs: Array<{ title: string; href: string }>;
}

export default function PdfMergeTaskLogs({ task, logs, breadcrumbs }: PdfMergeTaskLogsProps) {
    // 解析日誌等級
    const getLogLevel = (logLine: string) => {
        if (logLine.includes('.ERROR:')) return 'error';
        if (logLine.includes('.WARNING:')) return 'warning';
        if (logLine.includes('.INFO:')) return 'info';
        if (logLine.includes('.DEBUG:')) return 'debug';
        return 'info';
    };

    // 獲取日誌等級圖標
    const getLogIcon = (level: string) => {
        switch (level) {
            case 'error':
                return <XCircle className="h-4 w-4 text-red-500" />;
            case 'warning':
                return <AlertCircle className="h-4 w-4 text-yellow-500" />;
            case 'info':
                return <Info className="h-4 w-4 text-blue-500" />;
            case 'debug':
                return <Clock className="h-4 w-4 text-gray-500" />;
            default:
                return <Info className="h-4 w-4 text-blue-500" />;
        }
    };

    // 獲取日誌等級樣式
    const getLogLevelClass = (level: string) => {
        switch (level) {
            case 'error':
                return 'border-l-red-500 bg-red-50';
            case 'warning':
                return 'border-l-yellow-500 bg-yellow-50';
            case 'info':
                return 'border-l-blue-500 bg-blue-50';
            case 'debug':
                return 'border-l-gray-500 bg-gray-50';
            default:
                return 'border-l-blue-500 bg-blue-50';
        }
    };

    // 格式化日誌內容
    const formatLogContent = (logLine: string) => {
        // 移除時間戳記和等級前綴，只保留主要內容
        const match = logLine.match(/\[(.*?)\] .*?\.(ERROR|WARNING|INFO|DEBUG): (.*)/);
        if (match) {
            return {
                timestamp: match[1],
                level: match[2].toLowerCase(),
                content: match[3]
            };
        }
        return {
            timestamp: '',
            level: 'info',
            content: logLine
        };
    };

    const goBack = () => {
        window.history.back();
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="任務處理日誌" description="查看PDF合併任務的詳細處理日誌">
            <Head title={`任務日誌 - ${task.task_id}`} />

            <div className="space-y-6 p-6">
                {/* 返回按鈕 */}
                <div className="flex items-center gap-4">
                    <Button variant="outline" size="sm" onClick={goBack}>
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        返回任務詳情
                    </Button>
                </div>

                {/* 任務基本資訊 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <FileText className="h-5 w-5" />
                            任務資訊
                        </CardTitle>
                        <CardDescription>任務ID: {task.task_id}</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">考試資訊</label>
                                <div className="text-sm text-gray-900">
                                    {task.parameters?.exam_id || 'N/A'} - {task.parameters?.exam_year || 'N/A'}年
                                </div>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">任務狀態</label>
                                <div className="text-sm text-gray-900">{task.status}</div>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">處理進度</label>
                                <div className="text-sm text-gray-900">{task.progress}%</div>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">檔案數量</label>
                                <div className="text-sm text-gray-900">
                                    {task.processed_files || 0} / {task.total_files || 0}
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* 日誌內容 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <FileText className="h-5 w-5" />
                            處理日誌
                        </CardTitle>
                        <CardDescription>
                            共找到 {logs.length} 條相關日誌記錄
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {logs.length === 0 ? (
                            <div className="py-8 text-center text-gray-500">
                                <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                                <p>沒有找到相關的日誌記錄</p>
                            </div>
                        ) : (
                            <div className="space-y-2 max-h-96 overflow-y-auto">
                                {logs.map((log, index) => {
                                    const { timestamp, level, content } = formatLogContent(log);
                                    return (
                                        <div
                                            key={index}
                                            className={`border-l-4 p-3 rounded-r-md ${getLogLevelClass(level)}`}
                                        >
                                            <div className="flex items-start gap-2">
                                                {getLogIcon(level)}
                                                <div className="flex-1 min-w-0">
                                                    <div className="flex items-center gap-2 mb-1">
                                                        <span className="text-xs font-medium text-gray-600">
                                                            {timestamp}
                                                        </span>
                                                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                                                            level === 'error' ? 'bg-red-100 text-red-800' :
                                                            level === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                                                            level === 'info' ? 'bg-blue-100 text-blue-800' :
                                                            'bg-gray-100 text-gray-800'
                                                        }`}>
                                                            {level.toUpperCase()}
                                                        </span>
                                                    </div>
                                                    <div className="text-sm text-gray-900 font-mono break-all">
                                                        {content}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}



