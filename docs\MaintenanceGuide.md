# 推薦函系統 - 維護手冊

## 🔧 日常維護

### 每日檢查清單

- [ ] 檢查系統日誌錯誤
- [ ] 確認郵件發送正常
- [ ] 檢查磁碟空間使用
- [ ] 監控系統效能
- [ ] 確認備份任務執行

### 每週維護

- [ ] 清理舊日誌檔案
- [ ] 檢查資料庫效能
- [ ] 更新系統套件
- [ ] 檢查安全更新
- [ ] 驗證備份完整性

### 每月維護

- [ ] 深度系統清理
- [ ] 資料庫優化
- [ ] 安全掃描
- [ ] 效能分析報告
- [ ] 容量規劃檢查

## 📊 監控指令

### 系統狀態檢查

```bash
# Laravel 系統資訊
php artisan about

# 檢查資料庫連接
php artisan migrate:status

# 檢查排程任務
php artisan schedule:list

# 檢查佇列狀態
php artisan queue:diagnostics

# 檢查快取狀態
php artisan cache:table
```

### Log 系統監控

```bash
# 檢查 Log 統計
php artisan log:stats --days=7

# 檢查最近錯誤
php artisan log:errors --hours=24

# 測試 Log 功能
php artisan test:email-log

# 清理舊 Log
php artisan log:cleanup --days=30
```

### 效能監控

```bash
# 檢查系統資源
htop
free -m
df -h

# 檢查 PHP 進程
ps aux | grep php

# 檢查 Nginx/Apache 狀態
systemctl status nginx
systemctl status php8.1-fpm

# 檢查資料庫狀態
systemctl status mysql
```

## 🗄️ 資料庫維護

### 日常資料庫檢查

```sql
-- 檢查資料庫大小
SELECT
    table_schema AS 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables
WHERE table_schema = 'recletter_test'
GROUP BY table_schema;

-- 檢查表大小
SELECT
    table_name AS 'Table',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.TABLES
WHERE table_schema = 'recletter_test'
ORDER BY (data_length + index_length) DESC;

-- 檢查慢查詢
SHOW PROCESSLIST;
```

### 資料庫優化

```bash
# Laravel 快取優化
php artisan optimize

# MySQL 優化
mysqlcheck -u username -p --optimize --all-databases

# 重建快取
php artisan optimize
```

### 資料庫備份

```bash
# 完整備份
mysqldump -u username -p recletter_test > backup_$(date +%Y%m%d_%H%M%S).sql

# 壓縮備份
mysqldump -u username -p recletter_test | gzip > backup_$(date +%Y%m%d_%H%M%S).sql.gz

# 自動備份腳本
#!/bin/bash
BACKUP_DIR="/path/to/backups"
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u username -p recletter_test | gzip > $BACKUP_DIR/backup_$DATE.sql.gz

# 保留最近 30 天的備份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +30 -delete
```

## 🧹 清理維護

### 清理舊檔案

```bash
# 清理 Laravel 日誌 (保留 30 天)
find storage/logs -name "*.log" -mtime +30 -delete

# 清理暫存檔案
php artisan cache:clear
php artisan view:clear
php artisan route:clear
php artisan config:clear

# 清理 session 檔案
php artisan session:gc

# 清理上傳的暫存檔案
find storage/app/temp -type f -mtime +1 -delete
```

### 清理資料庫記錄

```bash
# 清理舊的 Log 記錄
php artisan log:cleanup --days=90

# 清理過期的 Session 記錄
php artisan session:gc

# 清理失敗的 Job 記錄
php artisan queue:prune-failed --hours=168
```

## 🔄 更新維護

### 系統更新流程

```bash
# 1. 備份系統
./backup.sh

# 2. 啟用維護模式
php artisan down --message="系統維護中，請稍後再試"

# 3. 更新代碼
git pull origin main

# 4. 更新依賴
composer install --no-dev --optimize-autoloader

# 5. 執行遷移
php artisan migrate --force

# 6. 清除快取
php artisan optimize:clear
php artisan optimize

# 7. 重啟服務
sudo systemctl reload nginx
sudo systemctl restart php8.1-fpm

# 8. 關閉維護模式
php artisan up

# 9. 驗證更新
php artisan test:email-log
```

### 套件更新

```bash
# 檢查可更新的套件
composer outdated

# 更新所有套件
composer update

# 更新特定套件
composer update vendor/package

# 檢查安全漏洞
composer audit
```

## 🚨 故障處理

### 常見問題診斷

#### 1. 網站無法訪問

```bash
# 檢查 Web 伺服器
systemctl status nginx
tail -f /var/log/nginx/error.log

# 檢查 PHP-FPM
systemctl status php8.1-fpm
tail -f /var/log/php8.1-fpm.log

# 檢查磁碟空間
df -h

# 檢查記憶體使用
free -m
```

#### 2. 資料庫連接問題

```bash
# 檢查 MySQL 服務
systemctl status mysql
tail -f /var/log/mysql/error.log

# 測試資料庫連接
mysql -u username -p -e "SELECT 1"

# 檢查連接數
mysql -u username -p -e "SHOW PROCESSLIST"
```

#### 3. 郵件發送失敗

```bash
# 檢查郵件配置
php artisan config:show mail

# 測試郵件發送
php artisan test:email-log

# 檢查郵件日誌
php artisan log:stats --type=email --days=1
```

#### 4. 效能問題

```bash
# 檢查慢查詢
mysql -u username -p -e "SHOW FULL PROCESSLIST"

# 檢查 PHP 錯誤
tail -f storage/logs/laravel.log

# 檢查系統負載
top
iostat -x 1
```

### 緊急恢復程序

```bash
# 1. 啟用維護模式
php artisan down

# 2. 恢復資料庫備份
mysql -u username -p recletter_test < latest_backup.sql

# 3. 恢復檔案備份
rsync -avz backup/ ./

# 4. 清除快取
php artisan optimize:clear

# 5. 關閉維護模式
php artisan up
```

## 📈 效能優化

### Laravel 優化

```bash
# 配置快取
php artisan config:cache

# 路由快取
php artisan route:cache

# 視圖快取
php artisan view:cache

# 事件快取
php artisan event:cache

# OPcache 優化
# 在 php.ini 中設置
opcache.enable=1
opcache.memory_consumption=256
opcache.max_accelerated_files=20000
```

### 資料庫優化

```sql
-- 分析表
ANALYZE TABLE table_name;

-- 優化表
OPTIMIZE TABLE table_name;

-- 檢查索引使用
EXPLAIN SELECT * FROM table_name WHERE condition;
```

### Web 伺服器優化

```nginx
# Nginx 配置優化
gzip on;
gzip_types text/plain text/css application/json application/javascript;

# 靜態檔案快取
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 📋 維護記錄

### 維護日誌範本

```
日期: YYYY-MM-DD
維護人員: 姓名
維護類型: [日常/緊急/計劃性]
維護內容:
- 執行的操作
- 發現的問題
- 解決方案
- 預防措施

系統狀態:
- 維護前: 正常/異常
- 維護後: 正常/異常
- 影響時間: X 分鐘

備註:
- 其他需要記錄的資訊
```

### 定期報告

- 每週系統健康報告
- 每月效能分析報告
- 季度容量規劃報告
- 年度系統回顧報告

---

📝 **注意**:

- 所有維護操作都應該有備份
- 重要維護應在低峰時段進行
- 維護前後都要進行系統檢查
- 詳細記錄所有維護活動
