<?php

use App\Models\User;
use App\Models\Applicant;
use App\Services\AuthenticationService;
use Illuminate\Support\Facades\Http;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

/**
 * 考生登入測試
 * 
 * 測試修正後的考生登入流程，確保同一位考生不會重複建立帳號
 */
describe('Applicant Login', function () {

    beforeEach(function () {
        // Mock 外部 API 回應
        Http::fake([
            'http://localhost:18001/index.php/api/v1/recommendation_system/get_applicant_data' => Http::response([
                'status' => 'success',
                'data' => [
                    'stu_idno' => 'encrypted_id_123',
                    'stu_name' => '測試學生',
                    'stu_e_mail' => '<EMAIL>',
                    'stu_year' => '2025',
                    'exam_id' => 'EXAM001',
                    'stu_cell_phone' => '0912345678',
                ]
            ], 200)
        ]);
    });

    test('新考生首次登入會建立新帳號', function () {
        $authService = new AuthenticationService();

        // 確認資料庫中沒有這個考生
        expect(User::where('email', '<EMAIL>')->exists())->toBeFalse();
        expect(Applicant::count())->toBe(0);

        // 執行登入
        $result = $authService->verifyTokenWithApi('test_token', '127.0.0.1');

        // 如果失敗，顯示錯誤訊息
        if (!$result['success']) {
            dump('Login failed:', $result);
        }

        // 驗證結果
        expect($result['success'])->toBeTrue();
        expect($result['user'])->toBeInstanceOf(User::class);
        expect($result['applicant'])->toBeInstanceOf(Applicant::class);

        // 驗證使用者資料
        $user = $result['user'];
        expect($user->email)->toBe('<EMAIL>');
        expect($user->name)->toBe('測試學生');
        expect($user->role)->toBe('applicant');

        // 驗證考生資料
        $applicant = $result['applicant'];
        expect($applicant->external_uid)->toBe('encrypted_id_123');
        expect($applicant->exam_year)->toBe('2025');
        expect($applicant->exam_id)->toBe('EXAM001');
        expect($applicant->phone)->toBe('0912345678');

        // 確認只建立了一個使用者和一個考生
        expect(User::count())->toBe(1);
        expect(Applicant::count())->toBe(1);
    });

    test('同一考生再次登入不會重複建立帳號', function () {
        // 使用Http::sequence()來確保按順序返回不同的回應
        Http::fake([
            'http://localhost:18001/index.php/api/v1/recommendation_system/get_applicant_data' => Http::sequence()
                ->push([
                    'status' => 'success',
                    'data' => [
                        'stu_idno' => 'encrypted_id_123',
                        'stu_name' => '測試學生',
                        'stu_e_mail' => '<EMAIL>',
                        'stu_year' => '2025',
                        'exam_id' => 'EXAM001',
                        'stu_cell_phone' => '0912345678',
                    ]
                ], 200)
                ->push([
                    'status' => 'success',
                    'data' => [
                        'stu_idno' => 'encrypted_id_456', // 不同的加密ID
                        'stu_name' => '測試學生',
                        'stu_e_mail' => '<EMAIL>', // 相同的email
                        'stu_year' => '2025',
                        'exam_id' => 'EXAM001',
                        'stu_cell_phone' => '0912345678',
                    ]
                ], 200)
        ]);

        $authService = new AuthenticationService();

        // 第一次登入
        $result1 = $authService->verifyTokenWithApi('test_token', '127.0.0.1');
        expect($result1['success'])->toBeTrue();

        $firstUserId = $result1['user']->id;
        $firstApplicantId = $result1['applicant']->id;

        // 確認建立了一個使用者和一個考生
        expect(User::count())->toBe(1);
        expect(Applicant::count())->toBe(1);

        // 第二次登入
        $result2 = $authService->verifyTokenWithApi('test_token', '127.0.0.1');
        expect($result2['success'])->toBeTrue();

        // 驗證使用相同的使用者和考生記錄
        expect($result2['user']->id)->toBe($firstUserId);
        expect($result2['applicant']->id)->toBe($firstApplicantId);

        // 確認沒有重複建立
        expect(User::count())->toBe(1);
        expect(Applicant::count())->toBe(1);

        // 驗證external_uid已更新為新的加密ID
        $applicant = Applicant::find($result2['applicant']->id); // 從資料庫重新載入
        expect($applicant->external_uid)->toBe('encrypted_id_456');
    });

    test('不同考生會建立不同帳號', function () {
        // 第一個考生的HTTP mock
        Http::fake([
            'http://localhost:18001/index.php/api/v1/recommendation_system/get_applicant_data' => Http::response([
                'status' => 'success',
                'data' => [
                    'stu_idno' => 'encrypted_id_123',
                    'stu_name' => '測試學生',
                    'stu_e_mail' => '<EMAIL>',
                    'stu_year' => '2025',
                    'exam_id' => 'EXAM001',
                    'stu_cell_phone' => '0912345678',
                ]
            ], 200)
        ]);

        $authService = new AuthenticationService();

        // 第一個考生登入
        $result1 = $authService->verifyTokenWithApi('test_token', '127.0.0.1');
        expect($result1['success'])->toBeTrue();

        // 模擬第二個考生的資料
        Http::fake([
            'http://localhost:18001/index.php/api/v1/recommendation_system/get_applicant_data' => Http::response([
                'status' => 'success',
                'data' => [
                    'stu_idno' => 'encrypted_id_789',
                    'stu_name' => '另一個學生',
                    'stu_e_mail' => '<EMAIL>', // 不同的email
                    'stu_year' => '2025',
                    'exam_id' => 'EXAM001',
                    'stu_cell_phone' => '0987654321',
                ]
            ], 200)
        ]);

        // 第二個考生登入
        $result2 = $authService->verifyTokenWithApi('test_token', '127.0.0.1');
        expect($result2['success'])->toBeTrue();

        // 驗證建立了不同的使用者和考生
        expect($result2['user']->id)->not->toBe($result1['user']->id);
        expect($result2['applicant']->id)->not->toBe($result1['applicant']->id);

        // 確認建立了兩個使用者和兩個考生
        expect(User::count())->toBe(2);
        expect(Applicant::count())->toBe(2);
    });

    test('API錯誤時登入失敗', function () {
        // 清除之前的HTTP mock並設定錯誤回應
        Http::fake([
            'http://localhost:18001/index.php/api/v1/recommendation_system/get_applicant_data' => Http::response([
                'status' => 'error',
                'message' => 'Token invalid'
            ], 400)
        ]);

        $authService = new AuthenticationService();
        $result = $authService->verifyTokenWithApi('invalid_token', '127.0.0.1');

        expect($result['success'])->toBeFalse();
        expect($result['message'])->toContain('登入失敗');

        // 確認沒有建立任何記錄
        expect(User::count())->toBe(0);
        expect(Applicant::count())->toBe(0);
    });
});
