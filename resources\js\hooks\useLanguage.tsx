import { useEffect, useState } from 'react';
import en from '../lang/en.json';
import zhTW from '../lang/zh-TW.json';

export type Language = 'zh-TW' | 'en';

interface LanguageContextType {
    language: Language;
    setLanguage: (lang: Language) => void;
    t: (key: string, params?: Record<string, string | number>, fallback?: string) => string;
}

// 檢測瀏覽器語言
const detectBrowserLanguage = (): Language => {
    const browserLang = navigator.language || navigator.languages[0];

    // 檢查是否為中文相關語言
    if (browserLang.startsWith('zh')) {
        return 'zh-TW';
    }

    return 'en';
};

// 翻譯字典 - 使用外部 JSON 文件
const translations: Record<Language, Record<string, unknown>> = {
    'zh-TW': zhTW,
    en: en,
};

export const useLanguage = (): LanguageContextType => {
    const [language, setLanguageState] = useState<Language>(() => {
        // 從 localStorage 讀取，如果沒有則使用瀏覽器語言
        const saved = localStorage.getItem('language') as Language;
        return saved || detectBrowserLanguage();
    });

    const setLanguage = (lang: Language) => {
        setLanguageState(lang);
        localStorage.setItem('language', lang);
    };

    const t = (key: string, params?: Record<string, string | number>, fallback?: string): string => {
        const keys = key.split('.');
        let value: unknown = translations[language];

        for (const k of keys) {
            if (value && typeof value === 'object' && value !== null && k in value) {
                value = (value as Record<string, unknown>)[k];
            } else {
                return fallback || key;
            }
        }

        if (typeof value === 'string') {
            if (params) {
                // 用正則找出 {param} 並取代成 params[param]
                return value.replace(/\{(\w+)\}/g, (_, p1) => {
                    return params[p1] !== undefined ? String(params[p1]) : `{${p1}}`;
                });
            }
            return value;
        }

        return fallback || key;
    };

    useEffect(() => {
        // 設定 HTML lang 屬性
        document.documentElement.lang = language;
    }, [language]);

    return {
        language,
        setLanguage,
        t,
    };
};
