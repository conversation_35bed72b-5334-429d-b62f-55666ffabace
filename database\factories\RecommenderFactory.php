<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Recommender>
 */
class RecommenderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $departments = [
            'Computer Science',
            'Electrical Engineering',
            'Mathematics',
            'Physics',
            'Chemistry',
            'Biology',
            'Economics',
            'Business Administration'
        ];

        $titles = [
            'Professor',
            'Associate Professor',
            'Assistant Professor',
            'Lecturer',
            'Research Fellow',
            'Department Head'
        ];

        return [
            'user_id' => User::factory()->recommender(),
            'email' => fake()->unique()->safeEmail(),
            'name' => fake()->name(),
            'title' => fake()->randomElement($titles),
            'department' => fake()->randomElement($departments),
            'phone' => fake()->optional()->phoneNumber(),
            'login_token' => Str::random(32),
            'year' => fake()->year(),
            'last_login_at' => fake()->optional()->dateTimeBetween('-1 month', 'now'),
        ];
    }

    /**
     * Create a recommender with a specific user.
     */
    public function forUser(User $user): static
    {
        return $this->state(fn(array $attributes) => [
            'user_id' => $user->id,
            'email' => $user->email,
            'name' => $user->name,
        ]);
    }

    /**
     * Create a recommender for a specific department.
     */
    public function forDepartment(string $department): static
    {
        return $this->state(fn(array $attributes) => [
            'department' => $department,
        ]);
    }

    /**
     * Create a recommender for a specific year.
     */
    public function forYear(string $year): static
    {
        return $this->state(fn(array $attributes) => [
            'year' => $year,
        ]);
    }

    /**
     * Create a recommender with a specific login token.
     */
    public function withToken(string $token): static
    {
        return $this->state(fn(array $attributes) => [
            'login_token' => $token,
        ]);
    }
}
