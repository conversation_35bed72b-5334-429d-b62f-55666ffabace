<?php

namespace App\Http\Middleware;

use App\Services\LogService;
use App\Enums\LogConstants;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Closure;

/**
 * 使用者角色檢查中間件
 *
 * 檢查使用者是否具有指定的角色權限
 */
class CheckUserRole
{
    /**
     * 處理傳入的請求
     *
     * @param Request $request
     * @param Closure $next
     * @param string ...$roles 允許的角色列表
     * @return Response
     */
    public function handle(Request $request, Closure $next, string ...$roles): Response
    {
        $user = Auth::user();

        // 檢查使用者是否已登入
        if (!$user) {
            LogService::securityOperation(
                '未登入用戶嘗試訪問需要角色權限的頁面',
                [
                    'middleware' => 'CheckUserRole',
                    'required_roles' => $roles,
                    'route' => $request->route()?->getName(),
                    'ip_address' => $request->ip()
                ],
                LogConstants::LEVEL_WARNING
            );
            return $this->handleUnauthorized($request, '請先登入系統');
        }

        // 檢查使用者角色是否在允許的角色列表中
        if (!in_array($user->role, $roles)) {
            LogService::securityOperation(
                '用戶角色權限不足',
                [
                    'middleware' => 'CheckUserRole',
                    'user_id' => $user->id,
                    'user_role' => $user->role,
                    'required_roles' => $roles,
                    'route' => $request->route()?->getName(),
                    'ip_address' => $request->ip()
                ],
                LogConstants::LEVEL_WARNING
            );
            return $this->handleForbidden($request, '您沒有權限訪問此頁面');
        }

        Log::debug('【Middleware】角色檢查通過', [
            'user_id' => $user->id,
            'user_role' => $user->role,
            'required_roles' => $roles,
            'route' => $request->route()?->getName(),
            'ip_address' => $request->ip()
        ]);

        return $next($request);
    }

    /**
     * 處理未授權的請求
     *
     * @param Request $request
     * @param string $message
     * @return Response
     */
    private function handleUnauthorized(Request $request, string $message): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => $message,
                'error' => 'unauthorized'
            ], 401);
        }

        return redirect()->route('home')->with('error', $message);
    }

    /**
     * 處理禁止訪問的請求
     *
     * @param Request $request
     * @param string $message
     * @return Response
     */
    private function handleForbidden(Request $request, string $message): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => $message,
                'error' => 'forbidden'
            ], 403);
        }

        return redirect()->route('dashboard')->with('error', $message);
    }
}
