import AdminLayout from '@/layouts/AdminLayout';
import { Head } from '@inertiajs/react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Switch } from '@/components/ui/Switch';
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs';
import { Badge } from '@/components/ui/Badge';
import { router, useForm } from '@inertiajs/react';
import { Save, AlertTriangle, Settings, Clock, RotateCcw, CheckCircle } from 'lucide-react';
import { useState } from 'react';

interface SystemSetting {
    id: number;
    key: string;
    value: string;
    type: string;
    category: string;
    description: string;
    is_active: boolean;
}

interface SystemSettingsProps {
    settings: {
        timing: SystemSetting[];
        general: SystemSetting[];
        security: SystemSetting[];
        email: SystemSetting[];
    };
    system_status: {
        is_maintenance_mode: boolean;
        reminder_cooldown_hours: number;
        auto_timeout_days: number;
        allow_pdf_upload: boolean;
        allow_questionnaire_submission: boolean;
        support_tel: string;
        support_email: string;
        admin_email: string;
    };
}

export default function SystemSettings({ settings, system_status }: SystemSettingsProps) {
    const [isLoading, setIsLoading] = useState(false);
    const [activeTab, setActiveTab] = useState('general');

    const { data, setData, processing, errors } = useForm({
        // 時序設定
        reminder_cooldown: settings?.timing.find((setting) => setting.key === 'reminder.cooldown_hours')?.value || '24',
        auto_timeout: settings?.timing.find((setting) => setting.key === 'auto.timeout_days')?.value || '7',

        // 一般設定
        support_tel: settings?.general.find((setting) => setting.key === 'system.support_tel')?.value || '',
        support_email: settings?.general.find((setting) => setting.key === 'system.support_email')?.value || '',
        admin_email: settings?.general.find((setting) => setting.key === 'system.admin_email')?.value || '',
        allow_pdf_upload: settings?.general.find((setting) => setting.key === 'submission.allow_pdf_upload')?.value === '1',
        allow_questionnaire_submission:
            settings?.general.find((setting) => setting.key === 'submission.allow_questionnaire_submission')?.value === '1',
    });

    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: '系統設定', href: '/admin/system-settings' },
    ];

    const handleSaveSettings = () => {
        // 使用 PUT 方法更新設定
        router.put('/admin/system-settings', data, {
            onSuccess: () => {
                alert('設定已儲存');
            },
            onError: (errors) => {
                console.error('儲存失敗:', errors);
                alert('儲存失敗，請檢查輸入資料');
            },
        });
    };

    // 重置為預設值
    const handleResetDefaults = () => {
        if (confirm('確定要重置所有設定為預設值嗎？此操作無法復原。')) {
            router.post(
                '/admin/system-settings/reset-defaults',
                {},
                {
                    onSuccess: () => {
                        alert('設定已重置為預設值');
                        router.reload();
                    },
                    onError: () => {
                        alert('重置失敗，請稍後再試');
                    },
                },
            );
        }
    };

    // 切換維護模式
    const toggleMaintenanceMode = (enabled: boolean) => {
        setIsLoading(true);

        router.post(
            '/admin/system-settings/toggle-maintenance-mode',
            { enabled },
            {
                onFinish: () => setIsLoading(false),
                onSuccess: () => {
                    // 重新載入頁面以更新狀態
                    router.reload();
                },
                onError: () => {
                    alert('切換維護模式失敗，請重試');
                },
            },
        );
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="基礎設定" description="管理系統的各項參數和配置">
            <Head title="基礎設定" />

            <div className="space-y-6 p-6">
                {/* 頂部操作區 */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div className="flex items-center gap-2">
                        <Settings className="h-6 w-6" />
                        <h1 className="text-2xl font-bold">系統基礎設定管理</h1>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="destructive" size="sm" onClick={handleResetDefaults}>
                            <RotateCcw className="mr-1 h-3 w-3" />
                            重置預設值
                        </Button>
                    </div>
                </div>
                {/* 維護模式狀態卡片 */}
                <Card className={system_status.is_maintenance_mode ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'}>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            {system_status.is_maintenance_mode ? (
                                <AlertTriangle className="h-5 w-5 text-red-500" />
                            ) : (
                                <CheckCircle className="h-5 w-5 text-green-500" />
                            )}
                            系統狀態
                            <Badge variant={system_status.is_maintenance_mode ? 'destructive' : 'default'}>
                                {system_status.is_maintenance_mode ? '維護中' : '正常運行'}
                            </Badge>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center justify-between rounded-lg border p-4">
                            <div className="space-y-1">
                                <p className="font-medium">維護模式</p>
                                <p className="text-sm text-muted-foreground">
                                    {system_status.is_maintenance_mode
                                        ? '系統目前處於維護模式，除管理員外其他用戶無法訪問'
                                        : '系統正常運行中，所有用戶都可以正常訪問'}
                                </p>
                            </div>
                            <Switch checked={system_status.is_maintenance_mode} onCheckedChange={toggleMaintenanceMode} disabled={isLoading} />
                        </div>
                    </CardContent>
                </Card>

                {/* 分頁式設定管理 */}
                <Card>
                    <CardHeader className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
                        <div>
                            <CardTitle className="flex items-center gap-2">
                                <Settings className="h-5 w-5" />
                                系統設定管理
                            </CardTitle>
                            <CardDescription>管理系統的各項參數和配置</CardDescription>
                        </div>
                        <Button size="sm" onClick={handleSaveSettings} disabled={processing}>
                            <Save className="mr-1 h-3 w-3" />
                            儲存設定
                        </Button>
                    </CardHeader>

                    <CardContent>
                        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                            <TabsList className="grid w-full grid-cols-2">
                                <TabsTrigger value="general" className="flex items-center gap-1">
                                    <Settings className="h-3 w-3" />
                                    一般設定
                                </TabsTrigger>
                                <TabsTrigger value="timing" className="flex items-center gap-1">
                                    <Clock className="h-3 w-3" />
                                    時序控制
                                </TabsTrigger>
                            </TabsList>

                            {/* 一般設定分頁 */}
                            <TabsContent value="general" className="space-y-6">
                                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="support_tel">技術支援電話</Label>
                                        <Input
                                            id="support_tel"
                                            type="tel"
                                            value={data.support_tel}
                                            onChange={(e) => setData('support_tel', e.target.value)}
                                            placeholder="例如: 0800-123-456"
                                        />
                                        <p className="text-xs text-muted-foreground">顯示在前台的技術支援電話</p>
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="support_email">技術支援信箱</Label>
                                        <Input
                                            id="support_email"
                                            type="email"
                                            value={data.support_email}
                                            onChange={(e) => setData('support_email', e.target.value)}
                                            placeholder="例如: <EMAIL>"
                                        />
                                        <p className="text-xs text-muted-foreground">顯示在前台的技術支援信箱</p>
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="admin_email">系統管理員信箱</Label>
                                        <Input
                                            id="admin_email"
                                            type="email"
                                            value={data.admin_email}
                                            onChange={(e) => setData('admin_email', e.target.value)}
                                            placeholder="例如: <EMAIL>"
                                        />
                                        <p className="text-xs text-muted-foreground">系統管理員信箱，用於接收重要通知</p>
                                    </div>
                                </div>
                            </TabsContent>

                            {/* 時序控制分頁 */}
                            <TabsContent value="timing" className="space-y-6">
                                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="reminder_cooldown">提醒冷卻時間 (小時)</Label>
                                        <Input
                                            id="reminder_cooldown"
                                            type="number"
                                            min="1"
                                            max="168"
                                            value={data.reminder_cooldown}
                                            onChange={(e) => setData('reminder_cooldown', e.target.value)}
                                        />
                                        <p className="text-xs text-muted-foreground">發送提醒後需等待的時間間隔，避免過於頻繁的提醒</p>
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="auto_timeout">自動超時天數</Label>
                                        <Input
                                            id="auto_timeout"
                                            type="number"
                                            min="1"
                                            max="30"
                                            value={data.auto_timeout}
                                            onChange={(e) => setData('auto_timeout', e.target.value)}
                                        />
                                        <p className="text-xs text-muted-foreground">推薦函自動標記為超時的天數，超過此期間將發送通知</p>
                                    </div>
                                </div>
                            </TabsContent>
                        </Tabs>
                    </CardContent>
                </Card>

                {/* 警告訊息 */}
                <Card className="border-orange-200 bg-orange-50">
                    <CardContent>
                        <div className="flex items-start gap-3">
                            <AlertTriangle className="mt-0.5 h-5 w-5 text-orange-600" />
                            <div className="space-y-1">
                                <p className="text-sm font-medium text-orange-800">注意事項</p>
                                <p className="text-sm text-orange-700">
                                    修改系統設定可能會影響正在進行的申請流程，請謹慎操作。 建議在非招生期間進行重要設定的變更。
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
