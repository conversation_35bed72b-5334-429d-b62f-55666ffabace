# 推薦函系統 - 專案使用手冊

## 📋 目錄

- [系統概述](#系統概述)
- [環境要求](#環境要求)
- [安裝與部署](#安裝與部署)
- [測試指南](#測試指南)
- [Artisan 指令](#artisan-指令)
- [排程任務](#排程任務)
- [Log 系統](#log-系統)
- [維護與監控](#維護與監控)
- [故障排除](#故障排除)

## 🎯 系統概述

推薦函系統是一個基於 Laravel 的 Web 應用程式，用於管理學術推薦函的申請、審核和發送流程。

### 主要功能

- 考生推薦函申請管理
- 推薦人邀請與推薦函撰寫
- 管理員審核與系統管理
- 完整的日誌記錄與統計
- 郵件自動發送與提醒

## 🔧 環境要求

### 基本要求

- PHP 8.1+
- MySQL 8.0+
- Composer 2.0+
- Node.js 18+
- Docker & Docker Compose (推薦)

### PHP 擴展

- BCMath
- Ctype
- Fileinfo
- JSON
- Mbstring
- OpenSSL
- PDO
- Tokenizer
- XML

### 單元測試

```bash
# 執行所有測試
php artisan test

# 執行特定測試套件
php artisan test tests/Unit/
php artisan test tests/Feature/

# 執行特定測試類別
php artisan test tests/Unit/LogServiceTest.php
php artisan test tests/Unit/LogModelsTest.php
php artisan test tests/Unit/LogConstantsTest.php

# 執行特定測試方法
php artisan test --filter=it_can_record_email_sent

# 並行執行測試 (加速)
php artisan test --parallel

# 生成測試覆蓋率報告
php artisan test --coverage --min=80

# 詳細輸出模式
php artisan test --verbose
```

### 測試資料庫管理

```bash
# 重置測試資料庫
php artisan migrate:fresh --env=testing

# 填充測試資料
php artisan db:seed --env=testing

# 執行特定 Seeder
php artisan db:seed --class=TestDataSeeder --env=testing
```

### Log 系統測試

```bash
# 測試郵件日誌功能
php artisan test:email-log

# 測試 Log 服務完整性
php artisan test tests/Unit/LogServiceTest.php

# 測試 Log 模型功能
php artisan test tests/Unit/LogModelsTest.php

# 測試 Log 常數定義
php artisan test tests/Unit/LogConstantsTest.php

# 測試 Log 整合功能
php artisan test tests/Feature/LogIntegrationTest.php
```

### 功能測試

```bash
# 測試推薦函流程
php artisan test tests/Feature/RecommendationLetterTest.php

# 測試用戶認證
php artisan test tests/Feature/AuthenticationTest.php

# 測試郵件發送
php artisan test tests/Feature/EmailTest.php
```

## ⚡ Artisan 指令

### 系統管理指令

```bash
# 清除快取
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# 重新載入配置
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 資料庫操作
php artisan migrate
php artisan migrate:rollback
php artisan migrate:fresh --seed
php artisan db:seed
```

### Log 系統指令

```bash
# 測試郵件日誌
php artisan test:email-log

# 清理舊日誌 (保留 30 天)
php artisan log:cleanup --days=30

# 生成日誌統計報告
php artisan log:stats --days=7

# 匯出日誌資料
php artisan log:export --type=operation --days=30
```

### 郵件系統指令

```bash
# 發送待發郵件
php artisan email:send-pending

# 重試失敗郵件
php artisan email:retry-failed

# 清理舊郵件記錄
php artisan email:cleanup --days=90
```

### 推薦函系統指令

```bash
# 更新推薦函狀態
php artisan recommendations:update-status

# 發送提醒郵件
php artisan recommendations:send-reminders

# 生成推薦函報告
php artisan recommendations:generate-report
```

## ⏰ 排程任務

### 設置 Cron Job

```bash
# 編輯 crontab
crontab -e

# 添加 Laravel 排程
* * * * * cd /path/to/project && php artisan schedule:run >> /dev/null 2>&1
```

### 排程任務列表

```bash
# 查看所有排程任務
php artisan schedule:list

# 執行排程任務 (測試用)
php artisan schedule:run
```

### 預設排程任務

#### 每日任務 (凌晨 2:00)

- 清理舊日誌記錄
- 清理過期 Session
- 備份重要資料
- 生成日報統計

#### 每小時任務

- 發送待發郵件
- 更新推薦函狀態
- 檢查系統健康狀態

#### 每週任務 (週日凌晨 3:00)

- 深度清理暫存檔案
- 優化資料庫
- 生成週報統計

## 📊 Log 系統

### Log 類型

1. **操作日誌** (`operation_logs`) - 用戶操作記錄
2. **系統日誌** (`system_logs`) - 系統事件記錄
3. **登入日誌** (`login_logs`) - 認證活動記錄
4. **郵件日誌** (`email_logs`) - 郵件發送記錄

### Log 使用範例

```php
use App\Services\LogService;
use App\Enums\LogConstants;

// 記錄操作成功
LogService::operationSuccess('推薦函創建成功', ['id' => 123]);

// 記錄操作失敗
LogService::operationFailure('推薦函創建失敗', ['error' => '驗證失敗']);

// 記錄郵件發送
LogService::emailSent('<EMAIL>', '邀請信');

// 記錄登入
LogService::loginSuccess();
```

### Log 查詢與統計

```php
// 取得最近錯誤
$errors = LogService::getRecentErrors(24, 10);

// 取得用戶活動
$activity = LogService::getUserRecentActivity($userId, 7);

// 取得綜合統計
$stats = LogService::getComprehensiveStats(30);
```

## 🔧 維護與監控

### 日常維護檢查清單

- [ ] 檢查系統日誌是否有錯誤
- [ ] 確認郵件發送正常
- [ ] 檢查資料庫連接狀態
- [ ] 監控磁碟空間使用
- [ ] 確認備份任務執行

### 效能監控

```bash
# 檢查 Laravel 日誌
tail -f storage/logs/laravel.log

# 檢查 Web 伺服器日誌
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# 檢查系統資源
htop
df -h
free -m
```

### 資料庫維護

```bash
# 檢查資料庫狀態
php artisan db:monitor

# 優化資料庫
php artisan db:optimize

# 備份資料庫
mysqldump -u username -p database_name > backup.sql
```

## 🚨 故障排除

### 常見問題

#### 1. 郵件發送失敗

```bash
# 檢查郵件配置
php artisan config:show mail

# 測試郵件發送
php artisan test:email-log

# 檢查郵件日誌
php artisan log:stats --type=email
```

#### 2. 資料庫連接問題

```bash
# 檢查資料庫配置
php artisan config:show database

# 測試資料庫連接
php artisan migrate:status

# 重新建立連接
php artisan config:clear
```

#### 3. 權限問題

```bash
# 修復儲存權限
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache

# 重新建立符號連結
php artisan storage:link
```

#### 4. 快取問題

```bash
# 清除所有快取
php artisan optimize:clear

# 重新建立快取
php artisan optimize
```

### 除錯模式

```bash
# 啟用除錯模式 (僅開發環境)
APP_DEBUG=true

# 檢視詳細錯誤資訊
tail -f storage/logs/laravel.log
```

### 聯絡支援

如遇到無法解決的問題，請提供：

1. 錯誤訊息截圖
2. 相關日誌檔案
3. 系統環境資訊
4. 重現步驟

## 📚 相關文檔

- [指令參考手冊](CommandReference.md) - 所有可用指令的完整說明 ⭐
- [部署檢查清單](DeploymentChecklist.md) - 完整的部署流程和檢查項目
- [測試執行指南](TestingGuide.md) - 詳細的測試執行說明
- [維護手冊](MaintenanceGuide.md) - 系統維護和故障排除
- [Log 系統文檔](LogSystem.md) - Log 系統完整說明
- [Log 快速參考](LogQuickReference.md) - Log 使用快速參考

## 🆘 支援與聯絡

### 技術支援

- **系統管理員**: <EMAIL>
- **開發團隊**: <EMAIL>
- **緊急聯絡**: <EMAIL>

### 問題回報

請在回報問題時提供：

1. 問題描述和重現步驟
2. 錯誤訊息截圖
3. 系統日誌相關片段
4. 發生時間和影響範圍

---

📝 **注意**: 本手冊會隨系統更新而調整，請定期查看最新版本。
