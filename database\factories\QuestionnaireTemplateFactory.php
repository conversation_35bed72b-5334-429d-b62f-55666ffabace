<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\QuestionnaireTemplate>
 */
class QuestionnaireTemplateFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $departments = [
            'Computer Science & Engineering',
            'Electrical Engineering',
            'Mathematics Department',
            'Physics Department',
            'Chemistry Department',
        ];

        $programTypes = ['master', 'phd'];

        return [
            'department_name' => fake()->randomElement($departments),
            'program_type' => fake()->randomElement($programTypes),
            'template_name' => fake()->words(3, true) . ' Template',
            'questions' => $this->generateQuestions(),
            'is_active' => fake()->boolean(80), // 80% chance of being active
        ];
    }

    /**
     * Generate sample questions for the template.
     */
    private function generateQuestions(): array
    {
        return [
            [
                'id' => 'academic_performance',
                'type' => 'select',
                'label' => 'Academic Performance',
                'options' => ['Excellent', 'Very Good', 'Good', 'Average', 'Below Average'],
                'required' => true,
            ],
            [
                'id' => 'research_potential',
                'type' => 'select',
                'label' => 'Research Potential',
                'options' => ['Outstanding', 'Very High', 'High', 'Moderate', 'Limited'],
                'required' => true,
            ],
            [
                'id' => 'communication_skills',
                'type' => 'select',
                'label' => 'Communication Skills',
                'options' => ['Excellent', 'Very Good', 'Good', 'Fair', 'Poor'],
                'required' => true,
            ],
            [
                'id' => 'leadership_qualities',
                'type' => 'select',
                'label' => 'Leadership Qualities',
                'options' => ['Strong', 'Moderate', 'Developing', 'Limited', 'Not Observed'],
                'required' => false,
            ],
            [
                'id' => 'overall_recommendation',
                'type' => 'select',
                'label' => 'Overall Recommendation',
                'options' => ['Highly Recommend', 'Recommend', 'Recommend with Reservations', 'Do Not Recommend'],
                'required' => true,
            ],
            [
                'id' => 'additional_comments',
                'type' => 'textarea',
                'label' => 'Additional Comments',
                'required' => false,
            ],
        ];
    }

    /**
     * Create an active template.
     */
    public function active(): static
    {
        return $this->state(fn(array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Create an inactive template.
     */
    public function inactive(): static
    {
        return $this->state(fn(array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a template for a specific department and program.
     */
    public function forDepartmentAndProgram(string $department, string $program): static
    {
        return $this->state(fn(array $attributes) => [
            'department_name' => $department,
            'program_type' => $program,
            'template_name' => "{$department} {$program} Template",
        ]);
    }
}
