<?php

namespace Tests\Unit;

use App\Enums\LogConstants;
use Tests\TestCase;

class LogConstantsTest extends TestCase
{
    /** @test */
    public function it_has_correct_operation_types()
    {
        $this->assertEquals('operation', LogConstants::OPERATION_TYPE_OPERATION);
        $this->assertEquals('system', LogConstants::OPERATION_TYPE_SYSTEM);
        $this->assertEquals('error', LogConstants::OPERATION_TYPE_ERROR);
        $this->assertEquals('security', LogConstants::OPERATION_TYPE_SECURITY);
    }

    /** @test */
    public function it_has_correct_log_levels()
    {
        $this->assertEquals('debug', LogConstants::LEVEL_DEBUG);
        $this->assertEquals('info', LogConstants::LEVEL_INFO);
        $this->assertEquals('warning', LogConstants::LEVEL_WARNING);
        $this->assertEquals('error', LogConstants::LEVEL_ERROR);
        $this->assertEquals('critical', LogConstants::LEVEL_CRITICAL);
    }

    /** @test */
    public function it_has_correct_actions()
    {
        $this->assertEquals('view', LogConstants::ACTION_VIEW);
        $this->assertEquals('create', LogConstants::ACTION_CREATE);
        $this->assertEquals('update', LogConstants::ACTION_UPDATE);
        $this->assertEquals('delete', LogConstants::ACTION_DELETE);
        $this->assertEquals('upload', LogConstants::ACTION_UPLOAD);
        $this->assertEquals('download', LogConstants::ACTION_DOWNLOAD);
        $this->assertEquals('login', LogConstants::ACTION_LOGIN);
        $this->assertEquals('logout', LogConstants::ACTION_LOGOUT);
        $this->assertEquals('export', LogConstants::ACTION_EXPORT);
        $this->assertEquals('import', LogConstants::ACTION_IMPORT);
    }

    /** @test */
    public function it_has_correct_email_types()
    {
        $this->assertEquals('invitation', LogConstants::EMAIL_TYPE_INVITATION);
        $this->assertEquals('reminder', LogConstants::EMAIL_TYPE_REMINDER);
        $this->assertEquals('notification', LogConstants::EMAIL_TYPE_NOTIFICATION);
        $this->assertEquals('alert', LogConstants::EMAIL_TYPE_ALERT);
        $this->assertEquals('welcome', LogConstants::EMAIL_TYPE_WELCOME);
        $this->assertEquals('reset_password', LogConstants::EMAIL_TYPE_RESET_PASSWORD);
    }

    /** @test */
    public function it_has_correct_email_statuses()
    {
        $this->assertEquals('pending', LogConstants::EMAIL_STATUS_PENDING);
        $this->assertEquals('sent', LogConstants::EMAIL_STATUS_SENT);
        $this->assertEquals('failed', LogConstants::EMAIL_STATUS_FAILED);
        $this->assertEquals('bounced', LogConstants::EMAIL_STATUS_BOUNCED);
    }

    /** @test */
    public function it_has_correct_system_types()
    {
        $this->assertEquals('api', LogConstants::SYSTEM_TYPE_API);
        $this->assertEquals('middleware', LogConstants::SYSTEM_TYPE_MIDDLEWARE);
        $this->assertEquals('command', LogConstants::SYSTEM_TYPE_COMMAND);
        $this->assertEquals('schedule', LogConstants::SYSTEM_TYPE_SCHEDULE);
        $this->assertEquals('queue', LogConstants::SYSTEM_TYPE_QUEUE);
    }

    /** @test */
    public function it_has_correct_default_values()
    {
        $this->assertEquals(730, LogConstants::DEFAULT_LOG_RETENTION_DAYS);
        $this->assertEquals(3, LogConstants::DEFAULT_EMAIL_RETRY_LIMIT);
        $this->assertEquals(100, LogConstants::DEFAULT_BATCH_SIZE);
    }

    /** @test */
    public function it_can_get_operation_types_array()
    {
        $types = LogConstants::getOperationTypes();
        
        $this->assertIsArray($types);
        $this->assertArrayHasKey(LogConstants::OPERATION_TYPE_OPERATION, $types);
        $this->assertArrayHasKey(LogConstants::OPERATION_TYPE_SYSTEM, $types);
        $this->assertArrayHasKey(LogConstants::OPERATION_TYPE_ERROR, $types);
        $this->assertArrayHasKey(LogConstants::OPERATION_TYPE_SECURITY, $types);
        
        $this->assertEquals('正常業務流程', $types[LogConstants::OPERATION_TYPE_OPERATION]);
        $this->assertEquals('系統相關操作', $types[LogConstants::OPERATION_TYPE_SYSTEM]);
        $this->assertEquals('業務流程錯誤', $types[LogConstants::OPERATION_TYPE_ERROR]);
        $this->assertEquals('安全相關操作', $types[LogConstants::OPERATION_TYPE_SECURITY]);
    }

    /** @test */
    public function it_can_get_log_levels_array()
    {
        $levels = LogConstants::getLogLevels();
        
        $this->assertIsArray($levels);
        $this->assertArrayHasKey(LogConstants::LEVEL_DEBUG, $levels);
        $this->assertArrayHasKey(LogConstants::LEVEL_INFO, $levels);
        $this->assertArrayHasKey(LogConstants::LEVEL_WARNING, $levels);
        $this->assertArrayHasKey(LogConstants::LEVEL_ERROR, $levels);
        $this->assertArrayHasKey(LogConstants::LEVEL_CRITICAL, $levels);
        
        $this->assertEquals('除錯', $levels[LogConstants::LEVEL_DEBUG]);
        $this->assertEquals('資訊', $levels[LogConstants::LEVEL_INFO]);
        $this->assertEquals('警告', $levels[LogConstants::LEVEL_WARNING]);
        $this->assertEquals('錯誤', $levels[LogConstants::LEVEL_ERROR]);
        $this->assertEquals('嚴重', $levels[LogConstants::LEVEL_CRITICAL]);
    }

    /** @test */
    public function it_can_get_actions_array()
    {
        $actions = LogConstants::getActions();
        
        $this->assertIsArray($actions);
        $this->assertArrayHasKey(LogConstants::ACTION_VIEW, $actions);
        $this->assertArrayHasKey(LogConstants::ACTION_CREATE, $actions);
        $this->assertArrayHasKey(LogConstants::ACTION_UPDATE, $actions);
        $this->assertArrayHasKey(LogConstants::ACTION_DELETE, $actions);
        
        $this->assertEquals('查看', $actions[LogConstants::ACTION_VIEW]);
        $this->assertEquals('建立', $actions[LogConstants::ACTION_CREATE]);
        $this->assertEquals('更新', $actions[LogConstants::ACTION_UPDATE]);
        $this->assertEquals('刪除', $actions[LogConstants::ACTION_DELETE]);
    }

    /** @test */
    public function it_can_get_email_types_array()
    {
        $types = LogConstants::getEmailTypes();
        
        $this->assertIsArray($types);
        $this->assertArrayHasKey(LogConstants::EMAIL_TYPE_INVITATION, $types);
        $this->assertArrayHasKey(LogConstants::EMAIL_TYPE_REMINDER, $types);
        $this->assertArrayHasKey(LogConstants::EMAIL_TYPE_NOTIFICATION, $types);
        $this->assertArrayHasKey(LogConstants::EMAIL_TYPE_ALERT, $types);
        
        $this->assertEquals('邀請信', $types[LogConstants::EMAIL_TYPE_INVITATION]);
        $this->assertEquals('提醒信', $types[LogConstants::EMAIL_TYPE_REMINDER]);
        $this->assertEquals('通知信', $types[LogConstants::EMAIL_TYPE_NOTIFICATION]);
        $this->assertEquals('系統異常通知', $types[LogConstants::EMAIL_TYPE_ALERT]);
    }

    /** @test */
    public function it_can_get_email_statuses_array()
    {
        $statuses = LogConstants::getEmailStatuses();
        
        $this->assertIsArray($statuses);
        $this->assertArrayHasKey(LogConstants::EMAIL_STATUS_PENDING, $statuses);
        $this->assertArrayHasKey(LogConstants::EMAIL_STATUS_SENT, $statuses);
        $this->assertArrayHasKey(LogConstants::EMAIL_STATUS_FAILED, $statuses);
        $this->assertArrayHasKey(LogConstants::EMAIL_STATUS_BOUNCED, $statuses);
        
        $this->assertEquals('待發送', $statuses[LogConstants::EMAIL_STATUS_PENDING]);
        $this->assertEquals('已發送', $statuses[LogConstants::EMAIL_STATUS_SENT]);
        $this->assertEquals('發送失敗', $statuses[LogConstants::EMAIL_STATUS_FAILED]);
        $this->assertEquals('退信', $statuses[LogConstants::EMAIL_STATUS_BOUNCED]);
    }

    /** @test */
    public function it_can_get_system_types_array()
    {
        $types = LogConstants::getSystemTypes();
        
        $this->assertIsArray($types);
        $this->assertArrayHasKey(LogConstants::SYSTEM_TYPE_API, $types);
        $this->assertArrayHasKey(LogConstants::SYSTEM_TYPE_MIDDLEWARE, $types);
        $this->assertArrayHasKey(LogConstants::SYSTEM_TYPE_COMMAND, $types);
        $this->assertArrayHasKey(LogConstants::SYSTEM_TYPE_SCHEDULE, $types);
        $this->assertArrayHasKey(LogConstants::SYSTEM_TYPE_QUEUE, $types);
        
        $this->assertEquals('API', $types[LogConstants::SYSTEM_TYPE_API]);
        $this->assertEquals('中間件', $types[LogConstants::SYSTEM_TYPE_MIDDLEWARE]);
        $this->assertEquals('指令', $types[LogConstants::SYSTEM_TYPE_COMMAND]);
        $this->assertEquals('排程', $types[LogConstants::SYSTEM_TYPE_SCHEDULE]);
        $this->assertEquals('佇列', $types[LogConstants::SYSTEM_TYPE_QUEUE]);
    }

    /** @test */
    public function it_can_check_http_status_codes()
    {
        // 測試成功狀態碼
        $this->assertTrue(LogConstants::isSuccessHttpCode(200));
        $this->assertTrue(LogConstants::isSuccessHttpCode(201));
        $this->assertTrue(LogConstants::isSuccessHttpCode(202));
        $this->assertTrue(LogConstants::isSuccessHttpCode(204));
        $this->assertFalse(LogConstants::isSuccessHttpCode(404));
        $this->assertFalse(LogConstants::isSuccessHttpCode(500));

        // 測試客戶端錯誤狀態碼
        $this->assertTrue(LogConstants::isClientErrorHttpCode(400));
        $this->assertTrue(LogConstants::isClientErrorHttpCode(401));
        $this->assertTrue(LogConstants::isClientErrorHttpCode(403));
        $this->assertTrue(LogConstants::isClientErrorHttpCode(404));
        $this->assertTrue(LogConstants::isClientErrorHttpCode(422));
        $this->assertFalse(LogConstants::isClientErrorHttpCode(200));
        $this->assertFalse(LogConstants::isClientErrorHttpCode(500));

        // 測試伺服器錯誤狀態碼
        $this->assertTrue(LogConstants::isServerErrorHttpCode(500));
        $this->assertTrue(LogConstants::isServerErrorHttpCode(502));
        $this->assertTrue(LogConstants::isServerErrorHttpCode(503));
        $this->assertTrue(LogConstants::isServerErrorHttpCode(504));
        $this->assertFalse(LogConstants::isServerErrorHttpCode(200));
        $this->assertFalse(LogConstants::isServerErrorHttpCode(404));
    }

    /** @test */
    public function it_has_correct_http_status_code_arrays()
    {
        $successCodes = LogConstants::HTTP_SUCCESS_CODES;
        $this->assertIsArray($successCodes);
        $this->assertContains(200, $successCodes);
        $this->assertContains(201, $successCodes);
        $this->assertContains(202, $successCodes);
        $this->assertContains(204, $successCodes);

        $clientErrorCodes = LogConstants::HTTP_CLIENT_ERROR_CODES;
        $this->assertIsArray($clientErrorCodes);
        $this->assertContains(400, $clientErrorCodes);
        $this->assertContains(401, $clientErrorCodes);
        $this->assertContains(403, $clientErrorCodes);
        $this->assertContains(404, $clientErrorCodes);
        $this->assertContains(422, $clientErrorCodes);

        $serverErrorCodes = LogConstants::HTTP_SERVER_ERROR_CODES;
        $this->assertIsArray($serverErrorCodes);
        $this->assertContains(500, $serverErrorCodes);
        $this->assertContains(502, $serverErrorCodes);
        $this->assertContains(503, $serverErrorCodes);
        $this->assertContains(504, $serverErrorCodes);
    }
}
