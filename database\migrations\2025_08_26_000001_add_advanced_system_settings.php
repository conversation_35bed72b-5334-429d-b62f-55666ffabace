<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\SystemSetting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 添加進階系統設定
        $advancedSettings = [
            // Log 系統設定
            [
                'key' => SystemSetting::LOG_RETENTION_DAYS,
                'value' => '730',
                'type' => SystemSetting::TYPE_INTEGER,
                'category' => SystemSetting::CATEGORY_GENERAL,
                'description' => '日誌保留天數，超過此天數的日誌將被自動清理',
            ],
            [
                'key' => SystemSetting::LOG_CLEANUP_ENABLED,
                'value' => '1',
                'type' => SystemSetting::TYPE_BOOLEAN,
                'category' => SystemSetting::CATEGORY_GENERAL,
                'description' => '是否啟用自動清理舊日誌',
            ],

            // 上傳設定
            [
                'key' => SystemSetting::UPLOAD_MAX_SIZE,
                'value' => '10240',
                'type' => SystemSetting::TYPE_INTEGER,
                'category' => SystemSetting::CATEGORY_GENERAL,
                'description' => '上傳檔案最大大小(KB)',
            ],
            [
                'key' => SystemSetting::UPLOAD_ALLOWED_TYPES,
                'value' => 'pdf',
                'type' => SystemSetting::TYPE_STRING,
                'category' => SystemSetting::CATEGORY_GENERAL,
                'description' => '允許的上傳檔案類型(逗號分隔)',
            ],

            // 推薦函業務設定
            [
                'key' => SystemSetting::RECOMMENDATION_MAX_LETTERS_PER_APPLICANT,
                'value' => '5',
                'type' => SystemSetting::TYPE_INTEGER,
                'category' => SystemSetting::CATEGORY_GENERAL,
                'description' => '每位申請人最多推薦函數量(硬限制)',
            ],
        ];

        foreach ($advancedSettings as $setting) {
            SystemSetting::firstOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 移除進階系統設定
        $settingKeys = [
            SystemSetting::LOG_RETENTION_DAYS,
            SystemSetting::LOG_CLEANUP_ENABLED,
            SystemSetting::UPLOAD_MAX_SIZE,
            SystemSetting::UPLOAD_ALLOWED_TYPES,
            SystemSetting::RECOMMENDATION_MAX_LETTERS_PER_APPLICANT,
        ];

        SystemSetting::whereIn('key', $settingKeys)->delete();
    }
};
