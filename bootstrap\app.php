<?php

use App\Http\Middleware\CheckRecommenderAccess;
use App\Http\Middleware\CheckApplicantAccess;
use App\Http\Middleware\CheckSystemAccess;
use App\Http\Middleware\CheckExamTiming;
use App\Http\Middleware\CheckUserAgreement;
use App\Http\Middleware\CheckUserRole;
use App\Http\Middleware\HandleAppearance;
use App\Http\Middleware\HandleInertiaRequests;
use App\Http\Middleware\ApiWhitelistMiddleware;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->encryptCookies(except: ['appearance', 'sidebar_state']);

        $middleware->web(append: [
            HandleAppearance::class,
            HandleInertiaRequests::class,
            AddLinkHeadersForPreloadedAssets::class,
        ]);

        $middleware->alias([
            'check.user.agreement' => CheckUserAgreement::class, // 檢查使用者是否同意使用者協議
            'check.system.access' => CheckSystemAccess::class, // 檢查系統存取權限
            'check.exam.timing' => CheckExamTiming::class, // 檢查報名時間限制
            'role' => CheckUserRole::class, // 檢查使用者角色
            'api.whitelist' => ApiWhitelistMiddleware::class, // API白名單檢查
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
