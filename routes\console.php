<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

/**
 * 排程任務
 */

// 排程自動狀態更新任務 - 用於更新推薦函"超時未處理"狀態
Schedule::command('recommendations:update-status')
    ->hourly() // 每小時執行一次
    ->withoutOverlapping() // 防止重複執行
    ->runInBackground(); // 在背景執行，避免阻塞其他任務

// 排程自動提醒信發送任務
Schedule::command('recommendations:send-reminders')
    ->dailyAt('09:00') // 每天上午9點執行
    ->withoutOverlapping()
    ->runInBackground();

// 排程重試失敗郵件任務
Schedule::command('emails:retry-failed')
    ->everyFourHours() // 每四小時執行一次
    ->withoutOverlapping()
    ->runInBackground();

// 排程清理PDF檔案任務
Schedule::command('pdf:cleanup --days=7')
    ->dailyAt('02:00') // 每天凌晨2點執行
    ->withoutOverlapping()
    ->runInBackground();

// 排程同步招生期間資料
// 手動觸發同步:php artisan system:sync-recruitment-periods
// 排程同步招生期間資料
Schedule::command('system:sync-recruitment-periods')
    ->dailyAt('03:00') // 每天凌晨3點執行
    ->withoutOverlapping()
    ->runInBackground();
