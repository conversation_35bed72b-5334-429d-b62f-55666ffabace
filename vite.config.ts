import tailwindcss from '@tailwindcss/vite';
import react from '@vitejs/plugin-react';
import laravel from 'laravel-vite-plugin';
import { resolve } from 'node:path';
import { defineConfig } from 'vite';

export default defineConfig({
    // 此配置為提供給 Docker 容器內的服務使用，以便模擬一個乾淨的 ubuntu 環境
    server: {
        host: true,
        port: 5173,
        strictPort: true,
        hmr: { host: 'localhost', port: 5173 },
        watch: {
            ignored: [
                '**/node_modules/**',
                '**/vendor/**',       // Laravel composer 套件
                '**/storage/**',
                '**/public/**'
            ]
        }
    },
    plugins: [
        laravel({
            input: ['resources/css/app.css', 'resources/js/app.tsx'],
            ssr: 'resources/js/ssr.tsx',
            refresh: true,
        }),
        react(),
        tailwindcss(),
    ],
    esbuild: {
        jsx: 'automatic',
    },
    resolve: {
        alias: {
            'ziggy-js': resolve(__dirname, 'vendor/tightenco/ziggy'),
        },
    },
});
