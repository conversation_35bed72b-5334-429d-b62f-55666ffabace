<?php

namespace App\Models;

use App\Enums\LogConstants;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

/**
 * 系統日誌模型
 *
 * 記錄系統日誌(同步資料、自動更新資訊等)
 */
class SystemLog extends Model
{
    use HasFactory;

    /**
     * 資料表名稱
     */
    protected $table = 'system_logs';

    /**
     * 可批量賦值的屬性
     */
    protected $fillable = [
        'user_id',
        'route',
        'method',
        'request_payload',
        'response_payload',
        'status_code',
    ];

    /**
     * 屬性類型轉換
     */
    protected $casts = [
        'request_payload'  => 'array',
        'response_payload' => 'array',
    ];

    public const ACTION_VIEW = LogConstants::ACTION_VIEW;
    public const ACTION_CREATE = LogConstants::ACTION_CREATE;
    public const ACTION_UPDATE = LogConstants::ACTION_UPDATE;
    public const ACTION_DELETE = LogConstants::ACTION_DELETE;
    public const ACTION_SYSTEM_SETTING = LogConstants::ACTION_SYSTEM_SETTING;

    /**
     * 取得關聯的用戶
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    #region: 查詢範圍 (Scopes)



    /**
     * 範圍查詢：按HTTP方法
     */
    public function scopeOfMethod(Builder $query, string $method): Builder
    {
        return $query->where('method', $method);
    }

    /**
     * 範圍查詢：成功的請求
     */
    public function scopeSuccessful(Builder $query): Builder
    {
        return $query->whereBetween('status_code', [200, 299]);
    }

    /**
     * 範圍查詢：客戶端錯誤
     */
    public function scopeClientErrors(Builder $query): Builder
    {
        return $query->whereBetween('status_code', [400, 499]);
    }

    /**
     * 範圍查詢：伺服器錯誤
     */
    public function scopeServerErrors(Builder $query): Builder
    {
        return $query->whereBetween('status_code', [500, 599]);
    }

    /**
     * 範圍查詢：按路由
     */
    public function scopeForRoute(Builder $query, string $route): Builder
    {
        return $query->where('route', 'like', "%{$route}%");
    }

    /**
     * 範圍查詢：最近的記錄
     */
    public function scopeRecent(Builder $query, int $hours = 24): Builder
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }



    #endregion

    #region: 統計方法

    /**
     * 取得狀態碼統計
     */
    public static function getStatusCodeStats(int $hours = 24): array
    {
        return self::where('created_at', '>=', now()->subHours($hours))
            ->whereNotNull('status_code')
            ->selectRaw('status_code, COUNT(*) as count')
            ->groupBy('status_code')
            ->orderBy('status_code')
            ->pluck('count', 'status_code')
            ->toArray();
    }

    /**
     * 取得HTTP方法統計
     */
    public static function getMethodStats(int $hours = 24): array
    {
        return self::where('created_at', '>=', now()->subHours($hours))
            ->selectRaw('method, COUNT(*) as count')
            ->groupBy('method')
            ->pluck('count', 'method')
            ->toArray();
    }

    /**
     * 取得路由統計
     */
    public static function getRouteStats(int $hours = 24, int $limit = 20): array
    {
        return self::where('created_at', '>=', now()->subHours($hours))
            ->selectRaw('route, COUNT(*) as count')
            ->groupBy('route')
            ->orderByDesc('count')
            ->limit($limit)
            ->pluck('count', 'route')
            ->toArray();
    }



    /**
     * 取得每小時統計
     */
    public static function getHourlyStats(int $hours = 24): array
    {
        return self::where('created_at', '>=', now()->subHours($hours))
            ->selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour')
            ->toArray();
    }

    #endregion

    #region: 輔助方法

    /**
     * 檢查是否為成功請求
     */
    public function isSuccessful(): bool
    {
        return $this->status_code >= 200 && $this->status_code < 300;
    }

    /**
     * 檢查是否為錯誤請求
     */
    public function isError(): bool
    {
        return $this->status_code >= 400;
    }



    /**
     * 取得狀態碼類型
     */
    public function getStatusTypeAttribute(): string
    {
        if ($this->status_code >= 200 && $this->status_code < 300) {
            return 'success';
        } elseif ($this->status_code >= 300 && $this->status_code < 400) {
            return 'redirect';
        } elseif ($this->status_code >= 400 && $this->status_code < 500) {
            return 'client_error';
        } elseif ($this->status_code >= 500) {
            return 'server_error';
        }
        return 'unknown';
    }

    #endregion
}
