<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SystemSetting;
use App\Services\RecruitmentPeriodService;
use App\Services\ExternalApiSyncService;
use Illuminate\Support\Facades\Validator;
use App\Services\LogService;
use App\Enums\LogConstants;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

/**
 * 招生期間管理控制器
 */
class RecruitmentPeriodController extends Controller
{
    protected RecruitmentPeriodService $recruitmentService;
    protected ExternalApiSyncService $syncService;

    public function __construct(
        RecruitmentPeriodService $recruitmentService,
        ExternalApiSyncService $syncService
    ) {
        $this->recruitmentService = $recruitmentService;
        $this->syncService = $syncService;
    }

    /**
     * 顯示招生期間管理頁面
     */
    public function index()
    {
        $periods = $this->recruitmentService->getAllPeriods();
        $lastSyncTime = $this->recruitmentService->getLastSyncTime();
        $needsResync = $this->recruitmentService->needsResync();

        // 處理期間資料，添加狀態資訊
        $processedPeriods = array_map(function ($period) {
            return $this->recruitmentService->getPeriodInfo($period['exam_id']);
        }, $periods);

        $breadcrumbs = [
            ['title' => '儀表板', 'href' => '/dashboard'],
            ['title' => '招生期間管理', 'href' => '/admin/recruitment-periods'],
        ];

        // 取得 API 設定
        $apiSettings = [
            'sync_exam_ids' => SystemSetting::get(SystemSetting::API_SYNC_EXAM_IDS, '1,2,D1,D2'),
            'sync_interval_hours' => (int) SystemSetting::get(SystemSetting::API_SYNC_INTERVAL_HOURS, 24),
        ];

        // 取得同步的招生期間資料
        $recruitmentData = SystemSetting::get('recruitment_periods_data');
        $recruitmentPeriods = $recruitmentData ? json_decode($recruitmentData, true) : [];

        return Inertia::render('admin/RecruitmentPeriods', [
            'periods' => $processedPeriods,
            'last_sync_time' => $lastSyncTime?->toISOString(),
            'needs_resync' => $needsResync,
            'active_periods_count' => count($this->recruitmentService->getActiveRecruitmentPeriods()),
            'breadcrumbs' => $breadcrumbs,
            'settings' => $apiSettings,
            'recruitment_periods' => $recruitmentPeriods,
        ]);
    }

    /**
     * 手動同步招生期間資料
     */
    public function sync()
    {
        try {
            $result = $this->syncService->syncExamPeriods();

            $user = Auth::user();
            LogService::securityOperation(
                "管理員 {$user->name} 手動同步招生期間資料",
                [
                    'sync_result' => $result,
                    'admin_id' => $user->id
                ]
            );

            // 清除快取
            $this->recruitmentService->clearCache();

            return back()->with('success', '招生期間資料同步成功');
        } catch (\Exception $e) {
            LogService::operationFailure(
                '手動同步招生期間資料失敗',
                ['error' => $e->getMessage()],
                LogConstants::LEVEL_ERROR,
                LogConstants::ACTION_SYSTEM_SETTING
            );

            return back()->withErrors([
                'sync' => '同步失敗：' . $e->getMessage()
            ]);
        }
    }

    public function updateApiSettings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'sync_exam_ids' => 'required|string|max:500',
            'sync_enabled' => 'required|boolean',
            'sync_interval_hours' => 'required|integer|min:1|max:168',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $user = Auth::user();

            SystemSetting::set(SystemSetting::API_SYNC_EXAM_IDS, $request->sync_exam_ids);
            SystemSetting::set(SystemSetting::API_SYNC_INTERVAL_HOURS, $request->sync_interval_hours);

            LogService::securityOperation(
                "管理員 {$user->name} 更新了 API 設定",
                [
                    'settings' => $request->all(),
                    'admin_id' => $user->id,
                ]
            );

            return back()->with('success', 'API 設定已更新');
        } catch (\Exception $e) {
            LogService::operationFailure(
                'API 設定更新失敗',
                [
                    'error' => $e->getMessage(),
                    'request_data' => $request->all()
                ],
                LogConstants::LEVEL_ERROR,
                LogConstants::ACTION_UPDATE
            );

            return back()->withErrors([
                'api_settings' => '設定更新失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 獲取特定招生期間的詳細資訊
     */
    public function show($examId)
    {
        try {
            $periodInfo = $this->recruitmentService->getPeriodInfo($examId);

            return response()->json([
                'success' => true,
                'data' => $periodInfo
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '獲取招生期間資訊失敗：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 檢查招生期間狀態
     */
    public function checkStatus()
    {
        try {
            $periods = $this->recruitmentService->getAllPeriods();
            $activePeriods = $this->recruitmentService->getActiveRecruitmentPeriods();
            $lastSyncTime = $this->recruitmentService->getLastSyncTime();

            $status = [
                'total_periods' => count($periods),
                'active_periods' => count($activePeriods),
                'last_sync_time' => $lastSyncTime?->toISOString(),
                'needs_resync' => $this->recruitmentService->needsResync(),
                'active_period_details' => $activePeriods,
                'current_time' => now()->toISOString()
            ];

            return response()->json([
                'success' => true,
                'data' => $status
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '檢查狀態失敗：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 清除招生期間快取
     */
    public function clearCache()
    {
        try {
            $this->recruitmentService->clearCache();

            $user = Auth::user();
            LogService::securityOperation(
                "管理員 {$user->name} 清除招生期間快取",
                ['admin_id' => $user->id]
            );

            return back()->with('success', '快取已清除');
        } catch (\Exception $e) {
            LogService::operationFailure(
                '清除招生期間快取失敗',
                ['error' => $e->getMessage()],
                LogConstants::LEVEL_ERROR,
                LogConstants::ACTION_SYSTEM_SETTING
            );

            return back()->withErrors([
                'cache' => '清除快取失敗：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 匯出招生期間資料
     */
    public function export()
    {
        try {
            $periods = $this->recruitmentService->getAllPeriods();
            $lastSyncTime = $this->recruitmentService->getLastSyncTime();

            $exportData = [
                'export_time' => now()->toISOString(),
                'last_sync_time' => $lastSyncTime?->toISOString(),
                'periods_count' => count($periods),
                'periods' => $periods
            ];

            $fileName = 'recruitment_periods_' . now()->format('Y-m-d_H-i-s') . '.json';

            return response()->json($exportData)
                ->header('Content-Type', 'application/json')
                ->header('Content-Disposition', "attachment; filename=\"{$fileName}\"");
        } catch (\Exception $e) {
            return back()->withErrors([
                'export' => '匯出失敗：' . $e->getMessage()
            ]);
        }
    }
}
