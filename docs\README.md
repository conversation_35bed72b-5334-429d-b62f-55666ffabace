# 推薦函系統 - 文檔中心

歡迎來到推薦函系統的文檔中心！這裡包含了系統的完整使用說明、部署指南、測試文檔和維護手冊。

## 📚 文檔目錄

### 🚀 快速開始

- **[專案使用手冊](ProjectManual.md)** - 系統概述、安裝部署、基本操作
- **[環境配置指南](EnvironmentConfiguration.md)** - 環境變數完整配置說明 ⭐
- **[指令參考手冊](CommandReference.md)** - 所有可用指令的完整說明 ⭐
- **[部署檢查清單](DeploymentChecklist.md)** - 完整的部署流程和檢查項目

### 🧪 測試相關

- **[測試執行指南](TestingGuide.md)** - 詳細的測試執行說明和最佳實踐
- **[Log 系統測試](../tests/)** - 完整的測試套件

### 🔧 系統維護

- **[維護手冊](MaintenanceGuide.md)** - 日常維護、故障排除、效能優化
- **[系統設定管理指南](SystemSettingsGuide.md)** - 系統設定管理介面使用說明 ⭐
- **[Log 系統文檔](LogSystem.md)** - Log 系統完整說明和 API 參考
- **[Log 快速參考](LogQuickReference.md)** - Log 使用快速參考

## 🎯 依角色分類

### 👨‍💻 開發人員

必讀文檔：

1. [專案使用手冊](ProjectManual.md) - 了解系統架構
2. [Log 系統文檔](LogSystem.md) - 學習 Log 系統使用
3. [測試執行指南](TestingGuide.md) - 掌握測試流程

常用指令：

```bash
# 執行測試
php artisan test

# 測試 Log 功能
php artisan test:email-log

# 檢查 Log 統計
php artisan log:stats --days=7
```

### 🚀 部署人員

必讀文檔：

1. [部署檢查清單](DeploymentChecklist.md) - 部署流程
2. [專案使用手冊](ProjectManual.md) - 環境要求和配置
3. [維護手冊](MaintenanceGuide.md) - 部署後維護

關鍵步驟：

```bash
# 部署前檢查
php artisan about
php artisan migrate:status

# 部署
composer install --no-dev --optimize-autoloader
php artisan migrate --force
php artisan optimize

# 部署後驗證
php artisan test:email-log
```

### 🔧 系統管理員

必讀文檔：

1. [維護手冊](MaintenanceGuide.md) - 日常維護和故障排除
2. [專案使用手冊](ProjectManual.md) - 系統監控和管理
3. [Log 系統文檔](LogSystem.md) - Log 分析和統計

日常檢查：

```bash
# 系統狀態
php artisan about
php artisan schedule:list

# Log 監控
php artisan log:stats --days=1
php artisan log:errors --hours=24

# 清理維護
php artisan log:cleanup --days=30
```

## 🔍 快速查找

### 常見任務

#### 🧪 執行測試

```bash
# 完整測試套件
php artisan test

# Log 系統測試
php artisan test tests/Unit/LogServiceTest.php
php artisan test tests/Feature/LogIntegrationTest.php

# 實際功能測試
php artisan test:email-log
```

#### 📊 Log 系統操作

```php
// 記錄操作
LogService::operationSuccess('操作描述', $metadata);

// 記錄郵件
LogService::emailSent($email, $subject, $type);

// 記錄登入
LogService::loginSuccess($userId);
```

#### 🔧 系統維護

```bash
# 清除快取
php artisan optimize:clear

# 重建快取
php artisan optimize

# 檢查系統狀態
php artisan about
```

#### 🚀 部署更新

```bash
# 維護模式
php artisan down

# 更新代碼
git pull origin main
composer install --no-dev --optimize-autoloader

# 資料庫遷移
php artisan migrate --force

# 恢復服務
php artisan up
```

## 📋 檢查清單

### 新開發人員入門

- [ ] 閱讀[專案使用手冊](ProjectManual.md)
- [ ] 設置開發環境
- [ ] 執行完整測試套件
- [ ] 熟悉 Log 系統使用
- [ ] 了解部署流程

### 部署前檢查

- [ ] 執行所有測試
- [ ] 檢查環境配置
- [ ] 備份現有資料
- [ ] 準備回滾計畫
- [ ] 通知相關人員

### 日常維護

- [ ] 檢查系統日誌
- [ ] 監控系統效能
- [ ] 確認備份正常
- [ ] 清理舊檔案
- [ ] 更新安全補丁

## 🚨 緊急情況

### 系統故障

1. 立即啟用維護模式：`php artisan down`
2. 檢查[維護手冊](MaintenanceGuide.md)的故障排除章節
3. 查看系統日誌：`tail -f storage/logs/laravel.log`
4. 如需回滾，參考[部署檢查清單](DeploymentChecklist.md)

### 聯絡資訊

- **緊急技術支援**: <EMAIL>
- **系統管理員**: <EMAIL>
- **開發團隊**: <EMAIL>

## 📈 文檔更新

### 版本記錄

- **v1.0** (2025-08-26) - 初始版本，包含完整的 Log 系統文檔
- **v1.1** (待定) - 新增功能文檔更新

### 貢獻指南

如需更新文檔：

1. 確保內容準確性
2. 遵循現有格式
3. 更新相關索引
4. 測試所有指令和範例

## 🔗 外部資源

### Laravel 官方文檔

- [Laravel 文檔](https://laravel.com/docs)
- [Laravel 測試](https://laravel.com/docs/testing)
- [Laravel 部署](https://laravel.com/docs/deployment)

### 工具和服務

- [Composer](https://getcomposer.org/doc/)
- [Docker](https://docs.docker.com/)
- [MySQL](https://dev.mysql.com/doc/)

---

📝 **文檔維護**: 本文檔中心會隨系統更新而持續維護，建議定期查看最新版本。

🆘 **需要幫助**: 如果在文檔中找不到所需資訊，請聯絡技術支援團隊。

php artisan migrate:fresh --seed
