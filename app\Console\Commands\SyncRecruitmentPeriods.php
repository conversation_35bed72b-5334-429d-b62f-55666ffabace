<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ExternalApiSyncService;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * 從exam系統同步招生期間資料
 *
 * 每五分鐘laravel系統自動執行指令，同步時間
 */
class SyncRecruitmentPeriods extends Command
{
    protected $signature = 'system:sync-recruitment-periods';
    protected $description = '從exam系統同步招生期間資料';

    public function handle()
    {
        $this->info(Carbon::now()->toDateTimeString() . ' 正在同步 exam 系統招生期間資料...');

        try {
            $syncService = new ExternalApiSyncService();
            $result = $syncService->syncSystemSettings();

            if ($result['success']) {
                $updatedCount = $result['results']['exam_periods']['data']['updated_count'] ?? 0;
                $this->info('同步完成，共更新 ' . $updatedCount . ' 筆招生期間資料');
                Log::debug('排程成功同步外部招生資料', $result);
                return Command::SUCCESS;
            } else {
                $this->error('同步失敗，請檢查日誌以獲取更多資訊');
                Log::warning('排程同步外部招生資料失敗', $result);
                return Command::FAILURE;
            }
        } catch (\Throwable $e) {
            $this->error('同步過程發生例外錯誤：' . $e->getMessage());
            Log::error('排程同步發生例外', ['error' => $e->getMessage()]);
            return Command::FAILURE;
        }
    }
}
