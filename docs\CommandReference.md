# 推薦函系統 - 指令參考手冊

## 📋 可用指令總覽

### ✅ **Log 系統指令** (新增)

```bash
# 清理舊日誌記錄
php artisan log:cleanup --days=30 --type=operation --dry-run

# 顯示日誌統計
php artisan log:stats --days=7 --type=email --format=table

# 顯示最近錯誤
php artisan log:errors --hours=24 --limit=10 --level=error

# 匯出日誌資料
php artisan log:export --type=operation --days=30 --format=csv

# 測試郵件日誌功能
php artisan test:email-log
```

### ✅ **郵件系統指令** (部分新增)

```bash
# 發送待發郵件 (新增)
php artisan email:send-pending --limit=50 --dry-run

# 重試失敗郵件 (已存在)
php artisan emails:retry-failed

# 清理舊郵件記錄 (新增)
php artisan email:cleanup --days=90 --status=sent --dry-run

# 測試郵件服務 (已存在)
php artisan emails:test
```

### ✅ **推薦函系統指令** (部分新增)

```bash
# 更新推薦函狀態 (已存在)
php artisan recommendations:update-status

# 發送提醒郵件 (已存在)
php artisan recommendations:send-reminders

# 生成推薦函報告 (新增)
php artisan recommendations:generate-report --days=30 --format=csv --type=summary
```

### ✅ **系統管理指令** (Laravel 內建)

```bash
# 快取管理
php artisan cache:clear
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan optimize
php artisan optimize:clear

# 資料庫操作
php artisan migrate
php artisan migrate:rollback
php artisan db:seed
php artisan db:show
php artisan migrate:status

# 排程管理
php artisan schedule:list
php artisan schedule:run

# 佇列管理
php artisan queue:work
php artisan queue:failed
php artisan queue:retry
php artisan queue:diagnostics
```

## 🔧 指令詳細說明

### Log 清理指令

```bash
php artisan log:cleanup [選項]

選項:
  --days=30           保留天數 (預設: 30)
  --type=operation    日誌類型 (operation|system|login|email)
  --dry-run          僅顯示將要刪除的記錄數量，不實際刪除

範例:
  php artisan log:cleanup --days=90 --type=email
  php artisan log:cleanup --dry-run
```

### Log 統計指令

```bash
php artisan log:stats [選項]

選項:
  --days=7           統計天數 (預設: 7)
  --type=operation   日誌類型 (operation|system|login|email)
  --format=table     輸出格式 (table|json)

範例:
  php artisan log:stats --days=30 --type=email
  php artisan log:stats --format=json
```

### Log 錯誤查詢指令

```bash
php artisan log:errors [選項]

選項:
  --hours=24         查詢小時數 (預設: 24)
  --limit=10         顯示數量限制 (預設: 10)
  --level=error      錯誤級別 (error|critical|warning)

範例:
  php artisan log:errors --hours=48 --limit=20
  php artisan log:errors --level=critical
```

### Log 匯出指令

```bash
php artisan log:export [選項]

選項:
  --type=operation   日誌類型 (operation|system|login|email)
  --days=30          匯出天數 (預設: 30)
  --format=csv       匯出格式 (csv|json)
  --output=path      輸出檔案路徑

範例:
  php artisan log:export --type=email --days=7 --format=json
  php artisan log:export --output=logs/custom_export.csv
```

### 郵件發送指令

```bash
php artisan email:send-pending [選項]

選項:
  --limit=50         處理數量限制 (預設: 50)
  --dry-run          僅顯示待發送郵件，不實際發送

範例:
  php artisan email:send-pending --limit=100
  php artisan email:send-pending --dry-run
```

### 郵件清理指令

```bash
php artisan email:cleanup [選項]

選項:
  --days=90          保留天數 (預設: 90)
  --status=sent      指定清理的狀態 (sent|failed|bounced)
  --dry-run          僅顯示將要刪除的記錄數量

範例:
  php artisan email:cleanup --days=180 --status=failed
  php artisan email:cleanup --dry-run
```

### 推薦函報告指令

```bash
php artisan recommendations:generate-report [選項]

選項:
  --days=30          報告期間天數 (預設: 30)
  --format=table     輸出格式 (table|csv|json)
  --output=path      輸出檔案路徑
  --type=summary     報告類型 (summary|detailed|stats)

範例:
  php artisan recommendations:generate-report --days=7 --type=detailed
  php artisan recommendations:generate-report --format=csv --output=reports/monthly.csv
```

## 📊 **常用指令組合**

### 日常維護

```bash
# 檢查系統狀態
php artisan about
php artisan schedule:list
php artisan queue:diagnostics

# 清理舊資料
php artisan log:cleanup --days=30
php artisan email:cleanup --days=90

# 查看統計
php artisan log:stats --days=7
php artisan log:errors --hours=24
```

### 故障排除

```bash
# 檢查最近錯誤
php artisan log:errors --hours=24 --limit=20

# 清除快取
php artisan optimize:clear

# 重建快取
php artisan optimize

# 檢查資料庫
php artisan migrate:status
```

### 報告生成

```bash
# 生成日誌報告
php artisan log:export --type=operation --days=30 --format=csv

# 生成推薦函報告
php artisan recommendations:generate-report --days=30 --format=json

# 生成統計資料
php artisan log:stats --format=json > stats.json
```

---

📝 **注意**:

- 所有新增的指令都已經過測試並可正常使用
- 在 Docker 環境中執行指令時請使用適當的容器指令
- 使用 `--dry-run` 選項可以安全地預覽操作結果
- 定期執行清理指令以維護系統效能
