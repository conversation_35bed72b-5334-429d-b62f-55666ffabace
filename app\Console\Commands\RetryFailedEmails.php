<?php

namespace App\Console\Commands;

use App\Services\EmailService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 重試失敗郵件的 Artisan 命令
 *
 * php artisan emails:retry-failed
 * php artisan emails:retry-failed --dry-run
 */
class RetryFailedEmails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'emails:retry-failed 
                            {--dry-run : 僅顯示將要重試的郵件，不實際發送}
                            {--max-retries=3 : 最大重試次數}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '重試發送失敗的郵件';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('開始執行失敗郵件重試任務...');

        try {
            $emailService = new EmailService();

            if ($this->option('dry-run')) {
                $this->info('執行模擬模式，不會實際發送郵件');
                // 這裡可以加入模擬邏輯來顯示將要重試的郵件
                // ...
                $this->info('模擬完成');
                return Command::SUCCESS;
            }

            $retryCount = $emailService->retryFailedEmails();

            $this->info("失敗郵件重試完成，共重試 {$retryCount} 封郵件");

            Log::info('失敗郵件重試命令執行完成', [
                'retry_count' => $retryCount,
                'executed_at' => now(),
                'command' => $this->signature,
            ]);

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('失敗郵件重試失敗: ' . $e->getMessage());

            Log::error('失敗郵件重試命令執行失敗', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'executed_at' => now(),
            ]);

            return Command::FAILURE;
        }
    }
}
