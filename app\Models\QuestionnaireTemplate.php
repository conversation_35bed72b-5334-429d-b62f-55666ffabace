<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuestionnaireTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'department_name',
        'program_type',
        'template_name',
        'questions',
        'is_active',
    ];

    protected $casts = [
        'questions' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get questionnaire template by department and program type.
     */
    public static function getTemplate(string $departmentName, string $programType): ?self
    {
        return self::where('department_name', $departmentName)
            ->where('program_type', $programType)
            ->where('is_active', true)
            ->first();
    }

    /**
     * Get default template if specific one doesn't exist.
     */
    public static function getDefaultTemplate(): ?self
    {
        return self::where('department_name', 'default')
            ->where('is_active', true)
            ->first();
    }

    /**
     * Create template from CSV data.
     */
    public static function createFromCsv(string $departmentName, string $programType, array $csvData): self
    {
        $questions = [];

        foreach ($csvData as $row) {
            if (isset($row['question']) && !empty($row['question'])) {
                $questions[] = [
                    'id' => $row['id'] ?? uniqid(),
                    'question' => $row['question'],
                    'type' => $row['type'] ?? 'textarea',
                    'required' => filter_var($row['required'] ?? true, FILTER_VALIDATE_BOOLEAN),
                    'options' => isset($row['options']) ? explode('|', $row['options']) : null,
                    'max_length' => $row['max_length'] ?? null,
                ];
            }
        }

        return self::create([
            'department_name' => $departmentName,
            'program_type' => $programType,
            'template_name' => "{$departmentName} - {$programType} 推薦問卷",
            'questions' => $questions,
            'is_active' => true,
        ]);
    }
}
