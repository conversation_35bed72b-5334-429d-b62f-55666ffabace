<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use TCPDF;

/**
 * 推薦函資訊PDF生成服務
 *
 * 負責生成包含考生推薦函資訊的PDF文件
 */
class RecommendationInfoPdfService
{
    /**
     * 為考生生成推薦函資訊PDF
     *
     * @param array $applicantRecommendations 考生的推薦函列表
     * @param string $autono 考生流水號
     * @return string|null PDF檔案路徑，失敗時返回null
     */
    public function generateRecommendationInfoPdf(array $applicantRecommendations, string $autono): ?string
    {
        try {
            if (empty($applicantRecommendations)) {
                Log::warning('[PDF_INFO] 沒有推薦函資料', ['autono' => $autono]);
                return null;
            }

            // 統一轉換為物件操作
            $recommendations = array_map(function ($rec) {
                return is_array($rec) ? (object) $rec : $rec;
            }, $applicantRecommendations);

            // 獲取考生基本資訊
            $firstRec = $recommendations[0];
            $applicant = is_array($firstRec->applicant ?? null) ? (object) $firstRec->applicant : $firstRec->applicant;

            // 創建PDF
            $pdf = $this->createPdfDocument($applicant, $autono);

            // 添加推薦函資訊內容
            $this->addRecommendationContent($pdf, $recommendations, $applicant, $autono);

            // 生成PDF內容
            $pdfContent = $pdf->Output('', 'S'); // 'S' 表示返回字串

            // 存儲PDF檔案
            $pdfPath = $this->storePdfFile($pdfContent, $autono);

            Log::info('[PDF_INFO] 推薦函資訊PDF生成成功', [
                'autono' => $autono,
                'pdf_path' => $pdfPath,
                'recommendation_count' => count($recommendations)
            ]);

            return $pdfPath;
        } catch (\Exception $e) {
            Log::error('[PDF_INFO] 推薦函資訊PDF生成失敗', [
                'autono' => $autono,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 創建PDF文檔
     *
     * @param object|null $applicant 考生資訊
     * @param string $autono 考生流水號
     * @return TCPDF
     */
    private function createPdfDocument($applicant, string $autono): TCPDF
    {
        // 創建 TCPDF 實例
        $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);

        // 設定文件資訊
        $applicantName = $applicant->user->name ?? $applicant->name ?? '未知考生';
        $pdf->SetCreator('推薦函管理系統');
        $pdf->SetAuthor('推薦函管理系統');
        $pdf->SetTitle("推薦函資訊 - {$applicantName} (流水號: {$autono})");
        $pdf->SetSubject('推薦函資訊彙整');

        // 設定頁面邊距
        $pdf->SetMargins(20, 20, 20);
        $pdf->SetHeaderMargin(10);
        $pdf->SetFooterMargin(10);

        // 設定自動分頁
        $pdf->SetAutoPageBreak(true, 20);

        // 移除頁首和頁尾
        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);

        // 設定字體（使用支援中文的字體）
        try {
            $pdf->SetFont('cid0ct', '', 12); // 繁體中文
        } catch (\Exception $e) {
            try {
                $pdf->SetFont('cid0cs', '', 12); // 簡體中文
            } catch (\Exception $e2) {
                $pdf->SetFont('dejavusans', '', 12); // DejaVu Sans
            }
        }

        return $pdf;
    }

    /**
     * 添加推薦函內容到PDF
     *
     * @param TCPDF $pdf
     * @param array $recommendations 推薦函列表
     * @param object|null $applicant 考生資訊
     * @param string $autono 考生流水號
     * @return void
     */
    private function addRecommendationContent(TCPDF $pdf, array $recommendations, $applicant, string $autono): void
    {
        // 添加頁面
        $pdf->AddPage();

        // 標題
        $pdf->SetFont('cid0ct', 'B', 16);
        $pdf->Cell(0, 10, '推薦函資訊彙整', 0, 1, 'C');
        $pdf->Ln(5);

        // 考生基本資訊
        $this->addApplicantInfo($pdf, $applicant, $autono);

        // 推薦函列表
        $this->addRecommendationList($pdf, $recommendations);

        // 統計資訊
        $this->addStatistics($pdf, $recommendations);

        // 生成時間
        $pdf->Ln(10);
        $pdf->SetFont('cid0ct', '', 10);
        $pdf->Cell(0, 5, '生成時間：' . date('Y-m-d H:i:s'), 0, 1, 'R');
        $pdf->Cell(0, 5, '系統：推薦函管理系統', 0, 1, 'R');
    }

    /**
     * 添加考生基本資訊
     *
     * @param TCPDF $pdf
     * @param object|null $applicant 考生資訊
     * @param string $autono 考生流水號
     * @return void
     */
    private function addApplicantInfo(TCPDF $pdf, $applicant, string $autono): void
    {
        $pdf->SetFont('cid0ct', 'B', 14);
        $pdf->Cell(0, 8, '考生資訊', 0, 1, 'L');
        $pdf->Ln(2);

        $pdf->SetFont('cid0ct', '', 12);

        // 考生姓名
        $applicantName = $applicant->user->name ?? $applicant->name ?? '未知考生';
        $pdf->Cell(40, 6, '考生姓名：', 0, 0, 'L');
        $pdf->Cell(0, 6, $applicantName, 0, 1, 'L');

        // 流水號
        $pdf->Cell(40, 6, '報名流水號：', 0, 0, 'L');
        $pdf->Cell(0, 6, $autono, 0, 1, 'L');

        // 考試資訊
        if (isset($applicant->exam_id)) {
            $pdf->Cell(40, 6, '招生代碼：', 0, 0, 'L');
            $pdf->Cell(0, 6, $applicant->exam_id ?? 'N/A', 0, 1, 'L');
        }

        if (isset($applicant->exam_year)) {
            $pdf->Cell(40, 6, '招生年度：', 0, 0, 'L');
            $pdf->Cell(0, 6, ($applicant->exam_year ?? 'N/A') . ' 學年度', 0, 1, 'L');
        }

        $pdf->Ln(5);
    }

    /**
     * 添加推薦函列表
     *
     * @param TCPDF $pdf
     * @param array $recommendations 推薦函列表
     * @return void
     */
    private function addRecommendationList(TCPDF $pdf, array $recommendations): void
    {
        $pdf->SetFont('cid0ct', 'B', 14);
        $pdf->Cell(0, 8, '推薦函列表', 0, 1, 'L');
        $pdf->Ln(2);

        // 表格標題
        $pdf->SetFont('cid0ct', 'B', 10);
        $pdf->SetFillColor(240, 240, 240);

        $pdf->Cell(15, 8, '序號', 1, 0, 'C', true);
        $pdf->Cell(35, 8, '推薦人姓名', 1, 0, 'C', true);
        $pdf->Cell(30, 8, '推薦人職稱', 1, 0, 'C', true);
        $pdf->Cell(40, 8, '推薦人單位', 1, 0, 'C', true);
        $pdf->Cell(35, 8, '系所', 1, 0, 'C', true);
        $pdf->Cell(15, 8, '狀態', 1, 1, 'C', true);

        // 表格內容
        $pdf->SetFont('cid0ct', '', 9);
        $pdf->SetFillColor(255, 255, 255);

        foreach ($recommendations as $index => $rec) {
            $rec = is_array($rec) ? (object) $rec : $rec;

            $pdf->Cell(15, 6, ($index + 1), 1, 0, 'C');
            $pdf->Cell(35, 6, $this->truncateText($rec->recommender_name ?? 'N/A', 12), 1, 0, 'L');
            $pdf->Cell(30, 6, $this->truncateText($rec->recommender_title ?? 'N/A', 10), 1, 0, 'L');
            $pdf->Cell(40, 6, $this->truncateText($rec->recommender_department ?? 'N/A', 15), 1, 0, 'L');
            $pdf->Cell(35, 6, $this->truncateText($rec->department_name ?? 'N/A', 12), 1, 0, 'L');

            // 狀態顯示
            $status = $this->getStatusDisplay($rec->status ?? 'unknown');
            $pdf->Cell(15, 6, $status, 1, 1, 'C');
        }

        $pdf->Ln(5);
    }

    /**
     * 添加統計資訊
     *
     * @param TCPDF $pdf
     * @param array $recommendations 推薦函列表
     * @return void
     */
    private function addStatistics(TCPDF $pdf, array $recommendations): void
    {
        $pdf->SetFont('cid0ct', 'B', 14);
        $pdf->Cell(0, 8, '統計資訊', 0, 1, 'L');
        $pdf->Ln(2);

        $pdf->SetFont('cid0ct', '', 12);

        // 總推薦函數量
        $totalCount = count($recommendations);
        $pdf->Cell(40, 6, '推薦函總數：', 0, 0, 'L');
        $pdf->Cell(0, 6, $totalCount . ' 封', 0, 1, 'L');

        // 各狀態統計
        $statusCounts = [];
        foreach ($recommendations as $rec) {
            $rec = is_array($rec) ? (object) $rec : $rec;
            $status = $rec->status ?? 'unknown';
            $statusCounts[$status] = ($statusCounts[$status] ?? 0) + 1;
        }

        foreach ($statusCounts as $status => $count) {
            $statusDisplay = $this->getStatusDisplay($status);
            $pdf->Cell(40, 6, $statusDisplay . '：', 0, 0, 'L');
            $pdf->Cell(0, 6, $count . ' 封', 0, 1, 'L');
        }
    }

    /**
     * 獲取狀態顯示文字
     *
     * @param string $status
     * @return string
     */
    private function getStatusDisplay(string $status): string
    {
        return match ($status) {
            'pending' => '待處理',
            'submitted' => '已提交',
            'declined' => '已拒絕',
            'withdrawn' => '已撤回',
            'timeout' => '已超時',
            default => '未知',
        };
    }

    /**
     * 截斷文字以適應表格寬度
     *
     * @param string $text
     * @param int $maxLength
     * @return string
     */
    private function truncateText(string $text, int $maxLength): string
    {
        if (mb_strlen($text, 'UTF-8') <= $maxLength) {
            return $text;
        }
        return mb_substr($text, 0, $maxLength - 1, 'UTF-8') . '…';
    }

    /**
     * 存儲PDF檔案
     *
     * @param string $pdfContent PDF內容
     * @param string $autono 考生流水號
     * @return string PDF檔案路徑
     */
    private function storePdfFile(string $pdfContent, string $autono): string
    {
        // 使用臨時目錄存儲
        $tempDir = 'temp/recommendation_info';
        $fileName = 'recommendation_info.pdf';
        $filePath = "{$tempDir}/{$autono}_{$fileName}";

        $disk = Storage::disk('local');

        // 確保目錄存在
        if (!$disk->exists($tempDir)) {
            $disk->makeDirectory($tempDir, 0755, true);
        }

        // 存儲檔案
        $success = $disk->put($filePath, $pdfContent);

        if (!$success) {
            throw new \Exception('推薦函資訊PDF存儲失敗');
        }

        return $disk->path($filePath);
    }
}
