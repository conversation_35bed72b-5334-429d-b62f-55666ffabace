<?php

use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Auth\ApplicantLoginController;
use App\Http\Controllers\Auth\RecommenderAuthController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 登入相關路由
|--------------------------------------------------------------------------
|
| 這裡定義所有與使用者登入相關的路由，針對不同角色的專用登入路由
|
*/

Route::get('/login', [AuthenticatedSessionController::class, 'index'])->name('login'); // 管理員登入頁面

Route::post('/login', [AuthenticatedSessionController::class, 'store']);                                                        // 管理員  todo 需加入中間件驗證，以白名單限制
Route::get('/auth-from-external', [ApplicantLoginController::class, 'handleExternalAuth'])->name('applicant.externalLogin');    // 考生   (從報名系統)
Route::get('/recommender/auth/{token}', [RecommenderAuthController::class, 'authenticateWithToken'])->name('recommender.auth'); // 推薦人 (token)

// 已認證使用者路由 (需要登入)
Route::middleware('auth')->group(function () {
    Route::post('/logout', [AuthenticatedSessionController::class, 'destroy'])->name('logout'); // 通一登出邏輯
});
