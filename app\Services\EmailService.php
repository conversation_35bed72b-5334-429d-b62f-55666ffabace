<?php

namespace App\Services;

use App\Models\EmailLog;
use App\Models\RecommendationLetter;
use App\Enums\LogConstants;
use Illuminate\Support\Facades\Log;

/**
 * 郵件服務類
 *
 * 處理推薦函系統中的郵件發送功能
 * 統一使用外部 API 發送方式
 */
class EmailService
{
    /**
     * 外部郵件服務實例
     */
    private ExternalMailService $externalMailService;

    public function __construct()
    {
        $this->externalMailService = new ExternalMailService();
    }
   /**
     * 發送推薦函邀請信
     */
    public function sendInvitationEmail(RecommendationLetter $letter): bool
    {
        $recommender = $letter->recommender;
        if (!$recommender) {
            return false;
        }

        return $this->sendAndLog(
            $letter,
            $recommender->email,
            $recommender->name,
            EmailLog::TYPE_INVITATION,
            '推薦函邀請 – ' . $letter->display_title,
            'emails.recommendation-invitation',
            [
                'recommender' => $recommender,
                'recommendationLetter' => $letter,
                'applicant' => $letter->applicant,
                'loginUrl' => route('recommender.auth', ['token' => $recommender->login_token]),
            ],
            LogConstants::EMAIL_TYPE_INVITATION
        );
    }

    /**
     * 發送推薦函提醒信
     */
    public function sendReminderEmail(RecommendationLetter $letter): bool
    {
        if (!$letter->canSendReminder()) {
            return false;
        }

        $recommender = $letter->recommender;
        if (!$recommender) {
            return false;
        }

        $result = $this->sendAndLog(
            $letter,
            $recommender->email,
            $recommender->name,
            EmailLog::TYPE_REMINDER,
            '推薦函提醒 – ' . $letter->display_title,
            'emails.recommendation-reminder',
            [
                'recommender' => $recommender,
                'recommendationLetter' => $letter,
                'applicant' => $letter->applicant,
                'loginUrl' => route('recommender.auth', ['token' => $recommender->login_token]),
                'isReminder' => true,
            ],
            LogConstants::EMAIL_TYPE_REMINDER
        );

        if ($result) {
            $letter->updateLastReminded();
        }

        return $result;
    }

    /**
     * 發送推薦函提交通知信
     */
    public function sendSubmissionNotificationEmail(RecommendationLetter $letter): bool
    {
        $applicant = $letter->applicant?->user;
        if (!$applicant) {
            return false;
        }

        return $this->sendAndLog(
            $letter,
            $applicant->email,
            $applicant->name,
            EmailLog::TYPE_NOTIFICATION,
            '推薦函已提交通知 – ' . $letter->display_title,
            'emails.recommendation-submitted',
            [
                'applicant' => $letter->applicant,
                'recommendationLetter' => $letter,
                'recommender' => $letter->recommender,
                'submitted_at' => $letter->submitted_at,
            ],
            LogConstants::EMAIL_TYPE_NOTIFICATION
        );
    }

    /**
     * 發送推薦函婉拒通知信
     */
    public function sendDeclineNotificationEmail(RecommendationLetter $letter): bool
    {
        $applicant = $letter->applicant?->user;
        $recommender = $letter->recommender;
        if (!$applicant || !$recommender) {
            return false;
        }

        return $this->sendAndLog(
            $letter,
            $applicant->email,
            $applicant->name,
            EmailLog::TYPE_NOTIFICATION,
            '推薦函邀請婉拒通知 – ' . $letter->display_title,
            'emails.recommendation-declined',
            [
                'applicant' => $letter->applicant,
                'recommender' => $recommender,
                'recommendationLetter' => $letter,
            ],
            LogConstants::EMAIL_TYPE_NOTIFICATION
        );
    }


    /**
     * 通用發送 + 紀錄方法
     *
     * @param RecommendationLetter $letter 推薦函
     * @param string $recipientEmail 收件人信箱
     * @param string $recipientName 收件人姓名
     * @param string $emailType 郵件類型
     * @param string $subject 郵件主旨
     * @param string $template Blade 模板名稱
     * @param array $data 模板資料
     * @param string|null $logType 日誌類型（用於 LogService）
     * @return bool 發送是否成功
     */
    private function sendAndLog(
        RecommendationLetter $letter,
        string $recipientEmail,
        string $recipientName,
        string $emailType,
        string $subject,
        string $template,
        array $data,
        string $logType
    ): bool {
        $emailLog = $this->createEmailLog($letter, $recipientEmail, $recipientName, $emailType, $subject, $data);

        try {
            $result = $this->sendEmail($template, $data, $recipientEmail, $subject);

            if (!$result['success']) {
                throw new \Exception($result['message']);
            }

            $emailLog->markAsSent();

            Log::debug("郵件發送成功", [
                'type' => $emailType,
                'recommendation_id' => $letter->id,
                'recipient' => $recipientEmail,
                'email_log_id' => $emailLog->id,
            ]);

                LogService::emailSent($recipientEmail, $subject, $logType, [
                    'recommendation_id' => $letter->id,
                    'email_log_id' => $emailLog->id,
                ]);

            return true;
        } catch (\Exception $e) {
            $emailLog->markAsFailed($e->getMessage());

            Log::error("郵件發送失敗", [
                'type' => $emailType,
                'recommendation_id' => $letter->id,
                'recipient' => $recipientEmail,
                'error' => $e->getMessage(),
            ]);

            if ($logType) {
                LogService::emailFailed($recipientEmail, $subject, $e->getMessage(), $logType, [
                    'recommendation_id' => $letter->id,
                    'email_log_id' => $emailLog->id,
                    'trace' => $e->getTraceAsString(),
                ]);
            }

            return false;
        }
    }

    /**
     * 建立郵件記錄
     *
     * @param RecommendationLetter $recommendationLetter
     * @param string $recipientEmail
     * @param string $recipientName
     * @param string $emailType
     * @param string $subject
     * @param array $metadata
     * @return EmailLog
     */
    private function createEmailLog(
        RecommendationLetter $recommendationLetter,
        string $recipientEmail,
        string $recipientName,
        string $emailType,
        string $subject,
        array $metadata = []
    ): EmailLog {
        return EmailLog::create([
            'recommendation_letter_id' => $recommendationLetter->id,
            'recipient_email' => $recipientEmail,
            'recipient_name' => $recipientName,
            'email_type' => $emailType,
            'subject' => $subject,
            'content' => '', // 實際內容會在郵件模板中生成
            'status' => EmailLog::STATUS_PENDING,
            'metadata' => $metadata,
        ]);
    }

    /**
     * 統一的郵件發送方法
     *
     * @param string $template Blade 模板名稱
     * @param array $data 模板資料
     * @param string $recipientEmail 收件人信箱
     * @param string $subject 郵件主旨
     * @return array 發送結果
     */
    private function sendEmail(string $template, array $data, string $recipientEmail, string $subject): array
    {
        return $this->externalMailService->sendWithTemplate($template, $data, $subject, $recipientEmail);
    }



    /**
     * 重試失敗的郵件
     *
     * @param EmailLog $emailLog
     * @return bool
     */
    public function retryFailedEmail(EmailLog $emailLog): bool
    {
        if (!$emailLog->canRetry()) {
            return false;
        }

        // 根據郵件類型重新發送
        switch ($emailLog->email_type) {
            case EmailLog::TYPE_INVITATION:
                return $this->sendInvitationEmail($emailLog->recommendationLetter);
            case EmailLog::TYPE_REMINDER:
                return $this->sendReminderEmail($emailLog->recommendationLetter);
            case EmailLog::TYPE_NOTIFICATION:
                return $this->sendSubmissionNotificationEmail($emailLog->recommendationLetter);
            default:
                return false;
        }
    }

    /**
     * 發送自動提醒信
     *
     * @return int 發送成功的數量
     */
    public function sendAutomaticReminders(): int
    {
        /**
         * 從推薦函中選取狀態為「待處理」且創建時間超過7天的推薦函，
         */
        $pendingRecommendations = RecommendationLetter::where('status', 'pending')
            ->where('created_at', '<=', now()->subDays(7))
            ->whereDoesntHave('emailLogs', function ($query) {
                $query->where('email_type', EmailLog::TYPE_REMINDER)
                    ->where('status', EmailLog::STATUS_SENT)
                    ->where('sent_at', '>=', now()->subDays(3)); // 3天內未發送過提醒
            })
            ->get();

        $sentCount = 0;

        foreach ($pendingRecommendations as $recommendation) {
            if ($this->sendReminderEmail($recommendation)) {
                $sentCount++;
            }
        }

        Log::info('自動提醒信發送完成', [
            'total_sent' => $sentCount,
            'total_pending' => $pendingRecommendations->count(),
        ]);

        return $sentCount;
    }

    /**
     * 重試失敗的郵件
     *
     * @return int 重試成功的數量
     */
    public function retryFailedEmails(): int
    {
        $failedEmails = EmailLog::where('status', EmailLog::STATUS_FAILED)
            ->where('retry_count', '<', 3)
            ->with('recommendationLetter')
            ->get();

        $retryCount = 0;

        foreach ($failedEmails as $emailLog) {
            if ($this->retryFailedEmail($emailLog)) {
                $retryCount++;
            }
        }

        Log::info('失敗郵件重試完成', [
            'total_retried' => $retryCount,
            'total_failed' => $failedEmails->count(),
        ]);

        return $retryCount;
    }

    public static function logEmail(array $data): EmailLog
    {
        return EmailLog::create($data);
    }

    public static function markAsSent(EmailLog $email): bool
    {
        return $email->update([
            'status' => EmailLog::STATUS_SENT,
            'sent_at' => now(),
        ]);
    }

    public static function markAsFailed(EmailLog $email, string $errorMessage): bool
    {
        return $email->update([
            'status' => EmailLog::STATUS_FAILED,
            'error_message' => $errorMessage,
            'retry_count' => $email->retry_count + 1,
        ]);
    }

    public static function canRetry(EmailLog $email, int $maxRetries = 3): bool
    {
        return $email->retry_count < $maxRetries && $email->status === EmailLog::STATUS_FAILED;
    }

    public static function getStats(int $days = 7): array
    {
        $query = EmailLog::query();
        $recent = now()->subDays($days);

        return [
            'total'   => $query->count(),
            'sent'    => $query->where('status', EmailLog::STATUS_SENT)->count(),
            'failed'  => $query->where('status', EmailLog::STATUS_FAILED)->count(),
            'pending' => $query->where('status', EmailLog::STATUS_PENDING)->count(),
            'recent'  => $query->where('created_at', '>=', $recent)->count(),
        ];
    }
}
