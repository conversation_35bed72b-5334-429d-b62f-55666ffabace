<?php

namespace App\Console\Commands;

use App\Models\EmailLog;
use App\Enums\LogConstants;
use App\Services\LogService;
use Illuminate\Console\Command;

class EmailSendPendingCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:send-pending 
                            {--limit=50 : 處理數量限制}
                            {--dry-run : 僅顯示待發送郵件，不實際發送}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '發送待發送的郵件';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $limit = (int) $this->option('limit');
        $dryRun = $this->option('dry-run');

        $this->info("📧 處理待發送郵件...");

        if ($dryRun) {
            $this->warn('⚠️  DRY RUN 模式 - 不會實際發送郵件');
        }

        // 查詢待發送的郵件
        $pendingEmails = EmailLog::where('status', LogConstants::EMAIL_STATUS_PENDING)
            ->orderBy('created_at')
            ->limit($limit)
            ->get();

        if ($pendingEmails->isEmpty()) {
            $this->info("✅ 沒有待發送的郵件");
            return 0;
        }

        $this->info("找到 {$pendingEmails->count()} 封待發送郵件");

        $sent = 0;
        $failed = 0;

        foreach ($pendingEmails as $emailLog) {
            if ($dryRun) {
                $this->line("📧 [{$emailLog->id}] {$emailLog->recipient_email} - {$emailLog->subject}");
                continue;
            }

            try {
                $this->sendEmail($emailLog);
                $emailLog->markAsSent();
                $sent++;
                $this->line("✅ 已發送: {$emailLog->recipient_email}");
            } catch (\Exception $e) {
                $emailLog->markAsFailed($e->getMessage());
                $failed++;
                $this->line("❌ 發送失敗: {$emailLog->recipient_email} - {$e->getMessage()}");
            }
        }

        if (!$dryRun) {
            $this->info("📊 處理完成！成功: {$sent}, 失敗: {$failed}");

            LogService::operationSuccess(
                '批量發送待發郵件',
                [
                    'total_processed' => $pendingEmails->count(),
                    'sent' => $sent,
                    'failed' => $failed
                ],
                LogConstants::ACTION_CREATE
            );
        }

        return 0;
    }

    /**
     * 發送郵件
     */
    private function sendEmail(EmailLog $emailLog): void
    {
        // 這裡應該根據郵件類型發送對應的郵件
        // 由於這是一個通用的發送指令，我們需要根據 email_type 來決定發送什麼郵件

        switch ($emailLog->email_type) {
            case LogConstants::EMAIL_TYPE_INVITATION:
                $this->sendInvitationEmail($emailLog);
                break;
            case LogConstants::EMAIL_TYPE_REMINDER:
                $this->sendReminderEmail($emailLog);
                break;
            case LogConstants::EMAIL_TYPE_NOTIFICATION:
                $this->sendNotificationEmail($emailLog);
                break;
            default:
                throw new \Exception("不支援的郵件類型: {$emailLog->email_type}");
        }
    }

    /**
     * 發送邀請郵件
     */
    private function sendInvitationEmail(EmailLog $emailLog): void
    {
        // 實際的邀請郵件發送邏輯
        // 這裡應該調用相應的 Mailable 類別
        $this->line("發送邀請郵件到: {$emailLog->recipient_email}");
    }

    /**
     * 發送提醒郵件
     */
    private function sendReminderEmail(EmailLog $emailLog): void
    {
        // 實際的提醒郵件發送邏輯
        $this->line("發送提醒郵件到: {$emailLog->recipient_email}");
    }

    /**
     * 發送通知郵件
     */
    private function sendNotificationEmail(EmailLog $emailLog): void
    {
        // 實際的通知郵件發送邏輯
        $this->line("發送通知郵件到: {$emailLog->recipient_email}");
    }
}
