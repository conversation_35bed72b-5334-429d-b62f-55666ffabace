<?php

namespace App\Services;

use App\Models\User;
use App\Models\Recommender;
use App\Enums\LogConstants;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class RecommenderService
{
    /**
     * 建立或取得推薦人資料
     *
     * @param array $recommenderData
     * @param array|null $examInfo 考試資訊 (exam_id, exam_year)
     * @return array ['user' => User, 'recommender' => Recommender, 'created' => bool]
     */
    public function createOrGetRecommender(array $recommenderData, array $examInfo): array
    {
        $email = $recommenderData['email'];
        $name = $recommenderData['name'] ?? '';
        $title = $recommenderData['title'] ?? '';
        $department = $recommenderData['department'] ?? '';
        $phone = $recommenderData['phone'] ?? '';

        // 考試資訊
        $exam_year = $examInfo['exam_year'] ?? null;
        $exam_id = $examInfo['exam_id'] ?? null;

        return DB::transaction(function () use ($email, $name, $title, $department, $phone, $exam_year, $exam_id) {
            $userCreated = false;
            $recommenderCreated = false;

            // 查詢是否已存在使用者
            $user = User::where('email', $email)->first();

            // 若無使用者，則建立新的使用者
            if (!$user) {
                $lastRecommenderId = Recommender::max('id');
                $user = User::create([
                    'name' => '推薦人_' . ($lastRecommenderId + 1), // 預設名稱
                    'email' => $email,
                    'role' => 'recommender',
                    'password' => null, // 推薦人使用 url 登入，無需密碼
                    'email_verified_at' => now(),
                ]);
                $userCreated = true;

                LogService::operationSuccess(
                    '考生新增推薦人成功(自動建立)',
                    ['user_id' => $user->id, 'email' => $email]
                );
            } else {
                // 如果該使用者是考生，則拋出異常(不允許將考生信箱用於推薦人)
                if ($user->role == 'applicant') {
                    LogService::operationFailure(
                        '考生嘗試將以存在的考生信箱用於擔任推薦人',
                        ['user_id' => $user->id, 'email' => $email],
                        LogConstants::LEVEL_WARNING
                    );
                    Log::warning('考生嘗試將以存在的考生信箱用於擔任推薦人', [
                        'user_id' => $user->id,
                        'email' => $email
                    ]);
                    throw new \Exception("此信箱已被註冊為考生，不能作為推薦人: {$email}");
                }
            }
            // 使用者已存在或完成建立後

            // 建立或取得"推薦人"資料
            $recommender = Recommender::where('user_id', $user->id)
                ->where('exam_year', $exam_year)
                ->where('exam_id', $exam_id)
                ->first();

            // 如果推薦人不存在，則建立新的推薦人資料 （初次建立時，依照新增來源設定個人資訊，後續推薦人可自行維護）
            if (!$recommender) {
                $recommender = Recommender::create([
                    'user_id' => $user->id,
                    'email' => $email,
                    'name' => $name ?: $user->name,
                    'title' => $title,
                    'department' => $department,
                    'phone' => $phone,
                    'login_token' => $this->generateUniqueLoginToken(),
                    'exam_year' => $exam_year,
                    'exam_id' => $exam_id,
                ]);
                $recommenderCreated = true;
            }

            return [
                'user' => $user,
                'recommender' => $recommender,
                'user_created' => $userCreated,
                'recommender_created' => $recommenderCreated,
                'created' => $userCreated || $recommenderCreated,
            ];
        });
    }

    /**
     * 產生唯一且安全的登入 Token
     *
     * 使用更複雜的 Token 生成策略：
     * - 結合時間戳和隨機字串
     * - 使用 SHA256 雜湊增加安全性
     * - 確保 Token 唯一性
     */
    private function generateUniqueLoginToken(): string
    {
        do {
            // 結合時間戳、隨機字串和推薦人 ID 來增加複雜度
            $timestamp = now()->timestamp;
            $randomString = Str::random(32);
            $salt = config('app.key', 'default-salt');

            // 使用 SHA256 雜湊生成更安全的 Token
            $rawToken = $timestamp . $randomString . $salt;
            $token = hash('sha256', $rawToken);

            // 截取前 64 個字符作為最終 Token
            $token = substr($token, 0, 64);
        } while (Recommender::where('login_token', $token)->exists());

        Log::debug('Generated secure login token', [
            'token_length' => strlen($token),
            'timestamp' => $timestamp
        ]);

        return $token;
    }

    /**
     * 透過 Email 取得推薦人資料
     */
    public function getRecommenderByEmail(string $email): ?Recommender
    {
        return Recommender::where('email', $email)->first();
    }

    /**
     * 透過 Token 取得推薦人登入資料
     */
    public function getRecommenderByToken(string $token): ?Recommender
    {
        return Recommender::where('login_token', $token)->first();
    }

    /**
     * 更新推薦人的最後登入時間
     */
    public function updateLastLogin(Recommender $recommender): void
    {
        $recommender->update(['last_login_at' => now()]);
    }

    /**
     * 重新產生推薦人登入 Token
     */
    public function regenerateLoginToken(Recommender $recommender): string
    {
        $newToken = $this->generateUniqueLoginToken();
        $recommender->update(['login_token' => $newToken]);

        Log::info('Regenerated login token for recommender', [
            'recommender_id' => $recommender->id,
            'email' => $recommender->email
        ]);

        return $newToken;
    }

    /**
     * 取得推薦人的登入 URL
     */
    public function getLoginUrl(Recommender $recommender): string
    {
        return route('recommender.auth', ['token' => $recommender->login_token]);
    }
}
