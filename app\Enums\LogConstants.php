<?php

namespace App\Enums;

/**
 * Log 系統常數管理類
 *
 * 統一管理所有 Log 相關的常數定義
 */
class LogConstants
{
    /**
     * 操作日誌類型
     */
    public const OPERATION_TYPE_OPERATION = 'operation';  // 正常業務流程
    public const OPERATION_TYPE_SYSTEM = 'system';        // 系統相關操作
    public const OPERATION_TYPE_ERROR = 'error';          // 業務流程錯誤
    public const OPERATION_TYPE_SECURITY = 'security';    // 安全相關操作

    /**
     * 日誌級別
     */
    public const LEVEL_DEBUG = 'debug';
    public const LEVEL_INFO = 'info';
    public const LEVEL_WARNING = 'warning';
    public const LEVEL_ERROR = 'error';
    public const LEVEL_CRITICAL = 'critical';

    /**
     * 操作動作
     */
    public const ACTION_VIEW = 'view';
    public const ACTION_CREATE = 'create';
    public const ACTION_UPDATE = 'update';
    public const ACTION_DELETE = 'delete';
    public const ACTION_UPLOAD = 'upload';
    public const ACTION_DOWNLOAD = 'download';
    public const ACTION_LOGIN = 'login';
    public const ACTION_LOGOUT = 'logout';
    public const ACTION_EXPORT = 'export';
    public const ACTION_IMPORT = 'import';
    public const ACTION_SEND = 'send';
    public const ACTION_SYSTEM_SETTING = 'system_setting';

    /**
     * 郵件類型
     */
    public const EMAIL_TYPE_INVITATION = 'invitation';     // 邀請信
    public const EMAIL_TYPE_REMINDER = 'reminder';         // 提醒信
    public const EMAIL_TYPE_NOTIFICATION = 'notification'; // 通知信
    public const EMAIL_TYPE_ALERT = 'alert';               // 系統異常通知
    public const EMAIL_TYPE_WELCOME = 'welcome';           // 歡迎信
    public const EMAIL_TYPE_RESET_PASSWORD = 'reset_password'; // 密碼重設

    /**
     * 郵件狀態
     */
    public const EMAIL_STATUS_PENDING = 'pending';  // 待發送
    public const EMAIL_STATUS_SENT = 'sent';        // 已發送
    public const EMAIL_STATUS_FAILED = 'failed';    // 發送失敗
    public const EMAIL_STATUS_BOUNCED = 'bounced';  // 退信

    /**
     * 登入狀態
     */
    public const LOGIN_SUCCESS = true;
    public const LOGIN_FAILURE = false;

    /**
     * 系統日誌類型
     */
    public const SYSTEM_TYPE_API = 'api';
    public const SYSTEM_TYPE_MIDDLEWARE = 'middleware';
    public const SYSTEM_TYPE_COMMAND = 'command';
    public const SYSTEM_TYPE_SCHEDULE = 'schedule';
    public const SYSTEM_TYPE_QUEUE = 'queue';

    /**
     * HTTP 狀態碼分類
     */
    public const HTTP_SUCCESS_CODES = [200, 201, 202, 204];
    public const HTTP_CLIENT_ERROR_CODES = [400, 401, 403, 404, 422];
    public const HTTP_SERVER_ERROR_CODES = [500, 502, 503, 504];

    /**
     * 預設配置
     */
    public const DEFAULT_LOG_RETENTION_DAYS = 730;  // 預設保留天數
    public const DEFAULT_EMAIL_RETRY_LIMIT = 3;     // 預設郵件重試次數
    public const DEFAULT_BATCH_SIZE = 100;          // 預設批量處理大小

    /**
     * 取得所有操作類型
     */
    public static function getOperationTypes(): array
    {
        return [
            self::OPERATION_TYPE_OPERATION => '正常業務流程',
            self::OPERATION_TYPE_SYSTEM => '系統相關操作',
            self::OPERATION_TYPE_ERROR => '業務流程錯誤',
            self::OPERATION_TYPE_SECURITY => '安全相關操作',
        ];
    }

    /**
     * 取得所有日誌級別
     */
    public static function getLogLevels(): array
    {
        return [
            self::LEVEL_DEBUG => '除錯',
            self::LEVEL_INFO => '資訊',
            self::LEVEL_WARNING => '警告',
            self::LEVEL_ERROR => '錯誤',
            self::LEVEL_CRITICAL => '嚴重',
        ];
    }

    /**
     * 取得所有操作動作
     */
    public static function getActions(): array
    {
        return [
            self::ACTION_VIEW => '查看',
            self::ACTION_CREATE => '建立',
            self::ACTION_UPDATE => '更新',
            self::ACTION_DELETE => '刪除',
            self::ACTION_UPLOAD => '上傳',
            self::ACTION_DOWNLOAD => '下載',
            self::ACTION_LOGIN => '登入',
            self::ACTION_LOGOUT => '登出',
            self::ACTION_EXPORT => '匯出',
            self::ACTION_IMPORT => '匯入',
            self::ACTION_SEND => '發送',
        ];
    }

    /**
     * 取得所有郵件類型
     */
    public static function getEmailTypes(): array
    {
        return [
            self::EMAIL_TYPE_INVITATION => '邀請信',
            self::EMAIL_TYPE_REMINDER => '提醒信',
            self::EMAIL_TYPE_NOTIFICATION => '通知信',
            self::EMAIL_TYPE_ALERT => '系統異常通知',
            self::EMAIL_TYPE_WELCOME => '歡迎信',
            self::EMAIL_TYPE_RESET_PASSWORD => '密碼重設',
        ];
    }

    /**
     * 取得所有郵件狀態
     */
    public static function getEmailStatuses(): array
    {
        return [
            self::EMAIL_STATUS_PENDING => '待發送',
            self::EMAIL_STATUS_SENT => '已發送',
            self::EMAIL_STATUS_FAILED => '發送失敗',
            self::EMAIL_STATUS_BOUNCED => '退信',
        ];
    }

    /**
     * 取得所有系統日誌類型
     */
    public static function getSystemTypes(): array
    {
        return [
            self::SYSTEM_TYPE_API => 'API',
            self::SYSTEM_TYPE_MIDDLEWARE => '中間件',
            self::SYSTEM_TYPE_COMMAND => '指令',
            self::SYSTEM_TYPE_SCHEDULE => '排程',
            self::SYSTEM_TYPE_QUEUE => '佇列',
        ];
    }

    /**
     * 檢查是否為成功的HTTP狀態碼
     */
    public static function isSuccessHttpCode(int $code): bool
    {
        return in_array($code, self::HTTP_SUCCESS_CODES);
    }

    /**
     * 檢查是否為客戶端錯誤的HTTP狀態碼
     */
    public static function isClientErrorHttpCode(int $code): bool
    {
        return in_array($code, self::HTTP_CLIENT_ERROR_CODES);
    }

    /**
     * 檢查是否為伺服器錯誤的HTTP狀態碼
     */
    public static function isServerErrorHttpCode(int $code): bool
    {
        return in_array($code, self::HTTP_SERVER_ERROR_CODES);
    }
}
