<?php

namespace App\Http\Controllers;

use App\Models\Applicant;
use App\Models\QuestionnaireTemplate;
use App\Models\RecommendationLetter;
use App\Models\Recommender;
use App\Models\SystemSetting;
use App\Services\LogService;
use App\Enums\LogConstants;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cache;
use Inertia\Inertia;

/**
 * 儀表板控制器(考生與推薦人)
 */
class DashboardController extends Controller
{
    // 主要功能：撈資料(依登入人身份索取資料)
    public function index(Request $request)
    {
        $user = Auth::user();
        $role = $user->role;

        LogService::operationSuccess(
            '用戶訪問儀表板',
            [
            'user_id' => $user->id,
            'user_role' => $role,
            'exam_id' => Session::get('exam_id'),
            'exam_year' => Session::get('exam_year')
            ],
            LogConstants::ACTION_VIEW
        );

        $data = match ($role) {
            'applicant' => $this->getApplicantData($user),
            'recommender' => $this->getRecommenderData($user),
            'admin' => $this->getAllRecommendationData(),
            default => [],
        };

        $isUserAllowed = SystemSetting::canUserAccessByExamPeriod(
            Session::get('exam_id'),
            Session::get('exam_year')
        );

        if ($user->role != 'admin') {
            $periodInfo = app(\App\Services\RecruitmentPeriodService::class)->getPeriodByExamId(Session::get('exam_id'));
        }


        $dynamicSystemInfo = [
          'is_user_allowed' => $isUserAllowed['can_access'],
          'period_info' => $periodInfo ?? [],
        ];
        
        return Inertia::render('Dashboard', [
          ...$data,
          'user_role' => $role,
          'system_info' => $dynamicSystemInfo,
        ]);
    }

    // 輔助函數：根據用戶角色撈取資料(考生)
    private function getApplicantData($user): array
    {
        $applicantId = Session::get('applicant_id');
        $applicant = $applicantId
          ? Applicant::find($applicantId)
          : Applicant::where('user_id', $user->id)->first();

        if ($applicant && !$applicantId) {
            Session::put('applicant_id', $applicant->id);
        }

        return [
          // 推薦函列表(包含推薦人資訊)
          'recommendations' => $applicant
            ? RecommendationLetter::where('applicant_id', $applicant->id)
            ->with(['applicant.user', 'recommender'])->get([
              'id',
              'applicant_id',
              'external_autono',
              'department_name',
              'program_type',
              'recommender_department',
              'status',
              'recommender_email',
              'recommender_name',
              'recommender_title',
              'recommender_phone',
              'pdf_path',
              'submitted_at',
              'created_at',
              'updated_at'
            ])
            : [],
            // 考生的申請資料(如果有)
          'applications' => $applicant?->external_uid
            ? Session::get('applications', [])
            : [],
            // 考生的基本資訊(包含用戶資料)
          'applicant_info' => $applicant ? [
            'user' => [
              'id' => $user->id,
              'role' => $user->role,
              'name' => $user->name,
              'email' => $user->email,
              'phone' => $applicant->phone,
            ]
          ] : null,
        ];
    }

    // 輔助函數：根據用戶角色撈取資料(推薦人)
    private function getRecommenderData($user): array
    {
        $recommenderId = Session::get('recommender_id');
        $recommender = $recommenderId
          ? Recommender::find($recommenderId)
          : Recommender::where('email', $user->email)->first();

        $recommendations = [];
        $questionnaireTemplates = [];

        if ($recommender) {
            // 只過濾出符合當前登入狀態下的年份與考試類別內的推薦函
            $recommendations = RecommendationLetter::where('recommender_email', $recommender->email)
              ->where('exam_id', Session::get('exam_id'))
              ->where('exam_year', Session::get('exam_year'))
              ->with(['applicant.user'])->get([
                'id',
                'applicant_id',
                'external_autono',
                'department_name',
                'program_type',
                'status',
                'pdf_path',
                'submitted_at',
                'created_at'
              ]);

            $departmentPrograms = $recommendations->map(fn ($r) => [
              'department_name' => $r->department_name,
              'program_type' => $r->program_type,
            ])->unique()->values();

            foreach ($departmentPrograms as $dp) {
                $template = QuestionnaireTemplate::where('department_name', $dp['department_name'])
                  ->where('program_type', $dp['program_type'])
                  ->where('is_active', true)->first()
                  ?? QuestionnaireTemplate::where('department_name', '通用')
                  ->where('program_type', '一般')
                  ->where('is_active', true)->first();

                if ($template) {
                    $template->questions = is_string($template->questions)
                      ? json_decode($template->questions, true)
                      : $template->questions;

                    $key = "{$dp['department_name']}_{$dp['program_type']}";
                    $questionnaireTemplates[$key] = $template;
                }
            }
        }

        return [
          // 推薦人基本資訊(包含用戶資料)
          'recommender' => $recommender,
          // 推薦人收到的推薦信列表
          'recommendations' => $recommendations,
          // 問卷模板(依部門與類型)
          'questionnaire_templates' => $questionnaireTemplates,
          // 提交設定(是否允許上傳PDF與填寫問卷)
          'submission_settings' => [
            'allow_pdf_upload' => SystemSetting::isAllowPdfUpload(),
            'allow_questionnaire_submission' => SystemSetting::isAllowQuestionnaireSubmission(),
          ]
        ];
    }

    // 輔助函數：根據用戶角色撈取資料(管理員)
    private function getAllRecommendationData(): array
    {
        $recommendations = [];

        return [
          'recommendations' => $recommendations,
        ];
    }
}
