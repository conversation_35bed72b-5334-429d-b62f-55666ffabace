<?php

namespace App\Http\Controllers;

use App\Models\UserAgreement;
use App\Services\LogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class UserAgreementController extends Controller
{
    /**
     * 顯示使用者協議頁面
     */
    public function show(): Response
    {
        $user = Auth::user();

        return Inertia::render('UserAgreement', [
            'user' => [
                'id'    => $user->id,
                'name'  => $user->display_name,   // 來自 Applicant / Recommender
                'email' => $user->email,
                'phone' => $user->phone_number,   // 來自 Applicant / Recommender
            ],
            'userRole' => $user->role,
        ]);
    }

    /**
     * 保存使用者同意協議
     */
    public function store(Request $request)
    {
        $request->validate([
            'agree' => 'required|boolean',
        ]);

        $user = Auth::user();

        // 如果不同意，則登出使用者
        if (!$request->agree) {
            LogService::operationFailure(
                '不同意使用者協議並登出',
                ['info' => $request->all()],
            );
            Auth::logout();
            return back()->withErrors([
                'agree' => '您必須同意個人資料使用條款才能繼續使用系統。'
            ]);
        }

        LogService::operationSuccess(
            '使用者同意個人資料使用條款',
            ['info' => $request->all()],
        );

        UserAgreement::setAgreement($user, true);

        return redirect()->route('dashboard')->with('success', '感謝您同意個人資料使用條款。');
    }
}
