<?php

return [
    /*
    |--------------------------------------------------------------------------
    | API 系統整合設定
    |--------------------------------------------------------------------------
    |
    | 此配置文件包含與外部API系統整合的相關設定，包括基礎URL、密鑰、超時設定等。
    */

    /*
    |--------------------------------------------------------------------------
    | eapapi API 基礎設定(向主系統請求的資料來源)
    |--------------------------------------------------------------------------
    */
    'eapapi_base_url' => env('EAPAPI_BASE_URL'),
    'eapapi_secret' => env('EAPAPI_API_KEY'),

    /*
    |--------------------------------------------------------------------------
    | exam API 基礎設定(向主系統請求的資料來源)
    |--------------------------------------------------------------------------
    */
    'exam_base_url' => env('EXAM_BASE_URL'),
    'exam_secret' => env('EXAM_API_KEY'),

    /*
    |--------------------------------------------------------------------------
    | API 基礎設定
    |--------------------------------------------------------------------------
    */
    'secret' => env('API_SECRET'), // API密鑰，用於驗證API請求
    'timeout' => env('API_TIMEOUT', 30),

    /*
    |--------------------------------------------------------------------------
    | API 白名單設定(允許請求的IP和User-Agent)
    |--------------------------------------------------------------------------
    */
    'whitelist' => [
        'ips' => array_filter(
            [
                env('EAPAPI_API_IP'), // eapapi 系統的IP地址
                env('EXAM_API_IP'), // exam 系統的IP地址
            ]
        ),

        'user_agents' => array_filter(array_map('trim', explode(',', env('API_WHITELIST_USER_AGENTS')))),
    ],

    /*
    |--------------------------------------------------------------------------
    | API 端點設定
    |--------------------------------------------------------------------------
    */
    'endpoints' => [
        'sync_exam_period' => '/sync_exam_period',
        'get_applicant_data' => '/get_applicant_data',
        'health' => '/health',
    ],

    /*
    |--------------------------------------------------------------------------
    | API 請求設定
    |--------------------------------------------------------------------------
    */
    'request' => [
        'retry_attempts' => env('API_RETRY_ATTEMPTS', 3),
        'retry_delay' => env('API_RETRY_DELAY', 1000), // 毫秒
        'connect_timeout' => env('API_CONNECT_TIMEOUT', 10),
        'read_timeout' => env('API_READ_TIMEOUT', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | API 響應設定
    |--------------------------------------------------------------------------
    */
    'response' => [
        'cache_ttl' => env('API_CACHE_TTL', 3600), // 秒
        'cache_enabled' => env('API_CACHE_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | API 日誌設定
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'enabled' => env('API_LOGGING_ENABLED', true),
        'level' => env('API_LOGGING_LEVEL', 'info'),
        'channel' => env('API_LOGGING_CHANNEL', 'stack'),
    ],
];
