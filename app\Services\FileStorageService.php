<?php

namespace App\Services;

use App\Models\RecommendationLetter;
use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

/**
 * 檔案存儲服務
 *
 * 負責管理推薦函PDF檔案的存儲、權限控制和分類組織
 */
class FileStorageService
{
    /**
     * 存儲上傳的PDF檔案
     *
     * @param UploadedFile $file
     * @param RecommendationLetter $recommendation
     * @return string 檔案路徑
     */
    public function storePdfFile(UploadedFile $file, RecommendationLetter $recommendation): string
    {
        $examId = $recommendation->exam_id;
        $examYear = $recommendation->exam_year;
        $autono = $recommendation->external_autono;

        /**
         * 檔案存儲路徑格式：
         *
         * 環境變數設定路徑/<招生代碼>/<學年度>/<考生報名流水號>/<檔案名稱>
         *
         * 將同一位考生的所有推薦函存放在同一個目錄下，方便管理和查找
         */
        $filePath = config('recommendation.upload.storage_path') . "/{$examId}/{$examYear}/{$autono}";

        // 使用private disk存儲，確保安全性
        $disk = Storage::disk('local');

        // 確保目錄存在
        if (!$disk->exists($filePath)) {
            $disk->makeDirectory($filePath, 0755, true);
        }
        // 取得副檔名
        $extension = $file->getClientOriginalExtension();

        // 產生檔名
        $filename = $this->generateFileName($recommendation, $extension);
        $fullFilePath = "{$filePath}/{$filename}";

        // 檢查是否已存在同名檔案（相同推薦人重複上傳）
        $isOverwrite = $disk->exists($fullFilePath);

        if ($isOverwrite) {
            RecommendationLetter::where('id', $recommendation->id)
                ->update(['submit_count' => $recommendation->submit_count + 1]);
        }

        // 存儲檔案（如果已存在會自動覆蓋）
        $stored = $disk->putFileAs($filePath, $file, $filename);

        if (!$stored) {
            Log::error('PDF檔案存儲失敗', [
                'recommendation_id' => $recommendation->id,
                'file_path' => $fullFilePath,
                'file_size' => $file->getSize(),
                'original_name' => $file->getClientOriginalName(),
                'is_overwrite' => $isOverwrite,
                'recommender_id' => $recommendation->recommender_id
            ]);
            LogService::operationFailure(
                'PDF檔案存儲失敗',
                [
                    'recommendation_id' => $recommendation->id,
                    'file_path' => $fullFilePath,
                    'file_size' => $file->getSize(),
                    'original_name' => $file->getClientOriginalName(),
                    'is_overwrite' => $isOverwrite,
                    'recommender_id' => $recommendation->recommender_id
                ]
            );
            throw new \Exception('檔案存儲失敗');
        }


        LogService::operationSuccess(
            $isOverwrite ? 'PDF檔案已覆蓋' : 'PDF檔案上傳成功',
            [
                'recommendation_id' => $recommendation->id,
                'file_path' => $fullFilePath,
                'file_size' => $file->getSize(),
                'original_name' => $file->getClientOriginalName(),
                'is_overwrite' => $isOverwrite,
                'submit_count' => $recommendation->submit_count,
                'recommender_id' => $recommendation->recommender_id
            ],
            $recommendation->recommender_id
        );

        return $stored;
    }


    /**
     * 存儲生成的PDF檔案(問卷)
     *
     * @param string $pdfContent
     * @param RecommendationLetter $recommendation
     * @return string 檔案路徑
     */
    public function storeGeneratedPdf(string $pdfContent, RecommendationLetter $recommendation): string
    {
        $examId = $recommendation->exam_id;
        $examYear = $recommendation->exam_year;
        $autono = $recommendation->external_autono;

        $fileName = $this->generateFileName($recommendation, 'pdf');
        $filePath = "recommendations/{$examId}/{$examYear}/{$autono}/{$fileName}";

        $fullFilePath = "{$filePath}/{$fileName}";

        // 使用private disk存儲
        $disk = Storage::disk('local');

        // 確保目錄存在
        if (!$disk->exists(dirname($filePath))) {
            $disk->makeDirectory(dirname($filePath), 0755, true);
        }

        // 檢查是否已存在同名檔案（相同推薦人重複上傳）
        $isOverwrite = $disk->exists($fullFilePath);

        if ($isOverwrite) {
            RecommendationLetter::where('id', $recommendation->id)
                ->update(['submit_count' => $recommendation->submit_count + 1]);
        }

        // 存儲檔案
        $success = $disk->put($filePath, $pdfContent);

        if (!$success) {
            Log::error('PDF檔案存儲失敗', [
                'recommendation_id' => $recommendation->id,
                'file_path' => $filePath,
                'file_size' => strlen($pdfContent),
                'recommender_id' => $recommendation->recommender_id
            ]);
            LogService::operationFailure(
                'PDF檔案存儲失敗',
                [
                    'recommendation_id' => $recommendation->id,
                    'file_path' => $filePath,
                    'file_size' => strlen($pdfContent),
                    'recommender_id' => $recommendation->recommender_id
                ]
            );
            throw new \Exception('PDF檔案存儲失敗');
        }

        LogService::operationSuccess(
            'PDF檔案生成並存儲成功',
            [
                'recommendation_id' => $recommendation->id,
                'file_path' => $filePath,
                'file_size' => strlen($pdfContent),
                'recommender_id' => $recommendation->recommender_id
            ],
            $recommendation->recommender_id
        );

        return $filePath;
    }

    /**
     * 檢查用戶是否有權限訪問檔案
     *
     * @param string $filePath
     * @param User $user
     * @return bool
     */
    public function canUserAccessFile(string $filePath, User $user): bool
    {
        // 管理員可以訪問所有檔案
        if ($user->role === 'admin') {
            LogService::operationSuccess(
                '管理員訪問檔案',
                [
                    'file_path' => $filePath,
                    'user_id' => $user->id,
                ],
                $user->id
            );
            return true;
        }

        // 考生不能檢視推薦函
        if ($user->role === 'applicant') {
            Log::warning('考生嘗試訪問推薦函', [
                'file_path' => $filePath,
                'user_id' => $user->id,
            ]);
            LogService::operationFailure(
                '考生嘗試訪問推薦函',
                [
                    'file_path' => $filePath,
                    'user_id' => $user->id,
                ],
                $user->id
            );
            return false;
        }


        // 從資料庫中查找符合該 filePath 的推薦函
        $recommendation = RecommendationLetter::where('pdf_path', $filePath)->first();

        if (!$recommendation) {
            Log::warning('找不到與檔案路徑對應的推薦函', [
                'file_path' => $filePath,
                'user_id' => $user->id,
            ]);
            LogService::operationFailure(
                '找不到與檔案路徑對應的推薦函',
                [
                    'file_path' => $filePath,
                    'user_id' => $user->id,
                ],
                $user->id
            );
            return false;
        }

        // 推薦人只能訪問自己的推薦函
        if ($user->role === 'recommender') {
            LogService::operationSuccess(
                '推薦人訪問自己的推薦函',
                [
                    'file_path' => $filePath,
                    'user_id' => $user->id,
                    'recommendation_id' => $recommendation->id,
                ],
                $user->id
            );
            return $recommendation->recommender_email === $user->email;
        }

        return false;
    }

    /**
     * 獲取檔案內容
     *
     * @param string $filePath
     * @param User $user
     * @return string
     * @throws \Exception
     */
    public function getFileContent(string $filePath, User $user): string
    {
        if (!$this->canUserAccessFile($filePath, $user)) {
            throw new \Exception('無權限訪問此檔案');
        }

        $disk = Storage::disk('local');

        if (!$disk->exists($filePath)) {
            throw new \Exception('檔案不存在');
        }

        return $disk->get($filePath);
    }

    /**
     * 獲取檔案下載響應
     *
     * @param string $filePath
     * @param User $user
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     * @throws \Exception
     */
    public function downloadFile(string $filePath, User $user)
    {
        if (!$this->canUserAccessFile($filePath, $user)) {
            throw new \Exception('無權限下載此檔案');
        }

        $disk = Storage::disk('local');

        if (!$disk->exists($filePath)) {
            throw new \Exception('檔案不存在');
        }

        $fileName = basename($filePath);
        $fullPath = storage_path('app/' . $filePath);

        return response()->download($fullPath, $fileName);
    }

    /**
     * 生成檔案名稱
     *
     * @param RecommendationLetter $recommendation
     * @param string $extension
     * @return string
     */
    private function generateFileName(RecommendationLetter $recommendation, string $extension): string
    {
        // 查詢此考生的所有推薦函（依 ID 排序，保證一致性）
        $recommendations = RecommendationLetter::where('external_autono', $recommendation->external_autono)
            ->where('exam_id', $recommendation->exam_id)
            ->where('exam_year', $recommendation->exam_year)
            ->orderBy('id')
            ->get();

        // 找出目前推薦人的位置（index + 1 = 檔名）
        $index = $recommendations->search(function ($r) use ($recommendation) {
            return $r->recommender_id === $recommendation->recommender_id;
        });

        if ($index === false) {
            Log::error('無法判斷推薦人在清單中的順序', [
                'recommendation_id' => $recommendation->id,
                'external_autono' => $recommendation->external_autono,
                'exam_id' => $recommendation->exam_id,
                'exam_year' => $recommendation->exam_year,
                'recommender_id' => $recommendation->recommender_id
            ]);
            throw new \Exception('無法判斷推薦人在清單中的順序');
        }

        $fileNumber = $index + 1;

        return "{$fileNumber}.{$extension}";
    }

    /**
     * 從檔案路徑提取推薦函ID
     *
     * @param string $filePath
     * @return int|null
     */
    private function extractRecommendationIdFromPath(string $filePath): ?int
    {
        // 檔案名格式：rec_{recommendation_id}_{applicant}_{recommender}_{timestamp}.pdf
        $fileName = basename($filePath);

        if (preg_match('/^rec_(\d+)_/', $fileName, $matches)) {
            return (int) $matches[1];
        }

        return null;
    }

    /**
     * 獲取用戶可訪問的檔案列表
     *
     * @param User $user
     * @return array
     */
    public function getUserAccessibleFiles(User $user): array
    {
        $disk = Storage::disk('local');
        $files = [];

        if ($user->role === 'admin') {
            // 管理員可以看到所有檔案
            $allFiles = $disk->allFiles('recommendations');
            foreach ($allFiles as $filePath) {
                if (pathinfo($filePath, PATHINFO_EXTENSION) === 'pdf') {
                    $files[] = $this->getFileInfo($filePath);
                }
            }
        } else {
            // 其他用戶只能看到有權限的檔案
            $recommendations = $this->getUserRecommendations($user);
            foreach ($recommendations as $recommendation) {
                if ($recommendation->pdf_path && $disk->exists($recommendation->pdf_path)) {
                    $files[] = $this->getFileInfo($recommendation->pdf_path, $recommendation);
                }
            }
        }

        return $files;
    }

    /**
     * 獲取檔案資訊
     *
     * @param string $filePath
     * @param RecommendationLetter|null $recommendation
     * @return array
     */
    private function getFileInfo(string $filePath, ?RecommendationLetter $recommendation = null): array
    {
        $disk = Storage::disk('local');

        if (!$recommendation) {
            $recommendationId = $this->extractRecommendationIdFromPath($filePath);
            $recommendation = RecommendationLetter::with('applicant.user')->find($recommendationId);
        }

        return [
            'path' => $filePath,
            'name' => basename($filePath),
            'size' => $disk->size($filePath),
            'modified' => $disk->lastModified($filePath),
            'recommendation_id' => $recommendation?->id,
            'applicant_name' => $recommendation?->applicant?->user?->name,
            'recommender_name' => $recommendation?->recommender_name,
            'department' => $recommendation?->department_name,
            'program' => $recommendation?->program_type,
            'status' => $recommendation?->status,
        ];
    }

    /**
     * 獲取用戶相關的推薦函
     *
     * @param User $user
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getUserRecommendations(User $user)
    {
        if ($user->role === 'recommender') {
            return RecommendationLetter::with('applicant.user')
                ->where('recommender_email', $user->email)
                ->get();
        } elseif ($user->role === 'applicant') {
            $applicant = $user->applicant;
            if ($applicant) {
                return RecommendationLetter::with('applicant.user')
                    ->where('applicant_id', $applicant->id)
                    ->get();
            }
        }

        return collect();
    }
}
