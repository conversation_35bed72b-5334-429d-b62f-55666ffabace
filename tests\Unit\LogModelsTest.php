<?php

namespace Tests\Unit;

use App\Models\OperationLog;
use App\Models\SystemLog;
use App\Models\LoginLog;
use App\Models\EmailLog;
use App\Models\User;
use App\Enums\LogConstants;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LogModelsTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
    }

    /** @test */
    public function operation_log_can_use_scopes()
    {
        OperationLog::factory()->create([
            'type' => LogConstants::OPERATION_TYPE_OPERATION,
            'level' => LogConstants::LEVEL_INFO,
            'user_id' => $this->user->id,
        ]);
        
        OperationLog::factory()->create([
            'type' => LogConstants::OPERATION_TYPE_ERROR,
            'level' => LogConstants::LEVEL_ERROR,
            'user_id' => $this->user->id,
        ]);

        // 測試類型範圍查詢
        $operationLogs = OperationLog::ofType(LogConstants::OPERATION_TYPE_OPERATION)->get();
        $this->assertCount(1, $operationLogs);

        // 測試級別範圍查詢
        $errorLogs = OperationLog::ofLevel(LogConstants::LEVEL_ERROR)->get();
        $this->assertCount(1, $errorLogs);

        // 測試錯誤範圍查詢
        $errors = OperationLog::errors()->get();
        $this->assertCount(1, $errors);

        // 測試用戶範圍查詢
        $userLogs = OperationLog::forUser($this->user->id)->get();
        $this->assertCount(2, $userLogs);
    }

    /** @test */
    public function operation_log_can_get_statistics()
    {
        OperationLog::factory()->count(3)->create([
            'type' => LogConstants::OPERATION_TYPE_OPERATION,
            'created_at' => now()->subDays(10)
        ]);
        
        OperationLog::factory()->count(2)->create([
            'type' => LogConstants::OPERATION_TYPE_ERROR,
            'created_at' => now()->subDays(10)
        ]);

        $typeStats = OperationLog::getTypeStatistics(30);
        $this->assertIsArray($typeStats);
        $this->assertEquals(3, $typeStats[LogConstants::OPERATION_TYPE_OPERATION]);
        $this->assertEquals(2, $typeStats[LogConstants::OPERATION_TYPE_ERROR]);

        $levelStats = OperationLog::getLevelStatistics(30);
        $this->assertIsArray($levelStats);
    }

    /** @test */
    public function operation_log_has_helper_methods()
    {
        $errorLog = OperationLog::factory()->create([
            'level' => LogConstants::LEVEL_ERROR
        ]);
        
        $warningLog = OperationLog::factory()->create([
            'level' => LogConstants::LEVEL_WARNING
        ]);

        $this->assertTrue($errorLog->isError());
        $this->assertFalse($errorLog->isWarning());
        
        $this->assertTrue($warningLog->isWarning());
        $this->assertFalse($warningLog->isError());
    }

    /** @test */
    public function system_log_can_use_scopes()
    {
        SystemLog::factory()->create([
            'method' => 'GET',
            'status_code' => 200,
        ]);
        
        SystemLog::factory()->create([
            'method' => 'POST',
            'status_code' => 500,
        ]);

        // 測試HTTP方法範圍查詢
        $getLogs = SystemLog::ofMethod('GET')->get();
        $this->assertCount(1, $getLogs);

        // 測試成功請求範圍查詢
        $successLogs = SystemLog::successful()->get();
        $this->assertCount(1, $successLogs);

        // 測試伺服器錯誤範圍查詢
        $errorLogs = SystemLog::serverErrors()->get();
        $this->assertCount(1, $errorLogs);
    }

    /** @test */
    public function system_log_can_get_statistics()
    {
        SystemLog::factory()->create([
            'status_code' => 200,
            'method' => 'GET',
            'created_at' => now()->subHours(2)
        ]);
        
        SystemLog::factory()->create([
            'status_code' => 404,
            'method' => 'POST',
            'created_at' => now()->subHours(2)
        ]);

        $statusStats = SystemLog::getStatusCodeStats(24);
        $this->assertIsArray($statusStats);
        $this->assertEquals(1, $statusStats[200]);
        $this->assertEquals(1, $statusStats[404]);

        $methodStats = SystemLog::getMethodStats(24);
        $this->assertIsArray($methodStats);
        $this->assertEquals(1, $methodStats['GET']);
        $this->assertEquals(1, $methodStats['POST']);
    }

    /** @test */
    public function system_log_has_helper_methods()
    {
        $successLog = SystemLog::factory()->create(['status_code' => 200]);
        $errorLog = SystemLog::factory()->create(['status_code' => 500]);

        $this->assertTrue($successLog->isSuccessful());
        $this->assertFalse($successLog->isError());
        
        $this->assertFalse($errorLog->isSuccessful());
        $this->assertTrue($errorLog->isError());
        
        $this->assertEquals('success', $successLog->status_type);
        $this->assertEquals('server_error', $errorLog->status_type);
    }

    /** @test */
    public function login_log_can_use_scopes()
    {
        LoginLog::factory()->create([
            'success' => true,
            'user_id' => $this->user->id,
            'ip_address' => '***********',
            'login_at' => now()
        ]);
        
        LoginLog::factory()->create([
            'success' => false,
            'failure_reason' => '密碼錯誤',
            'ip_address' => '***********',
            'login_at' => now()->subDays(1)
        ]);

        // 測試成功登入範圍查詢
        $successLogs = LoginLog::successful()->get();
        $this->assertCount(1, $successLogs);

        // 測試失敗登入範圍查詢
        $failedLogs = LoginLog::failed()->get();
        $this->assertCount(1, $failedLogs);

        // 測試IP範圍查詢
        $ipLogs = LoginLog::fromIp('***********')->get();
        $this->assertCount(1, $ipLogs);

        // 測試今日範圍查詢
        $todayLogs = LoginLog::today()->get();
        $this->assertCount(1, $todayLogs);
    }

    /** @test */
    public function login_log_can_get_statistics()
    {
        LoginLog::factory()->count(7)->create([
            'success' => true,
            'login_at' => now()->subDays(5)
        ]);
        
        LoginLog::factory()->count(3)->create([
            'success' => false,
            'failure_reason' => '密碼錯誤',
            'login_at' => now()->subDays(5)
        ]);

        $successRate = LoginLog::getSuccessRateStats(30);
        $this->assertIsArray($successRate);
        $this->assertEquals(10, $successRate['total_attempts']);
        $this->assertEquals(7, $successRate['successful_logins']);
        $this->assertEquals(3, $successRate['failed_logins']);
        $this->assertEquals(70, $successRate['success_rate']);

        $failureReasons = LoginLog::getFailureReasonStats(30);
        $this->assertIsArray($failureReasons);
        $this->assertEquals(3, $failureReasons['密碼錯誤']);
    }

    /** @test */
    public function login_log_has_helper_methods()
    {
        $successLog = LoginLog::factory()->create(['success' => true]);
        $failedLog = LoginLog::factory()->create(['success' => false]);

        $this->assertEquals('成功', $successLog->status_text);
        $this->assertEquals('失敗', $failedLog->status_text);
    }

    /** @test */
    public function email_log_can_use_scopes()
    {
        EmailLog::factory()->create([
            'status' => LogConstants::EMAIL_STATUS_SENT,
            'email_type' => LogConstants::EMAIL_TYPE_INVITATION,
            'retry_count' => 0
        ]);
        
        EmailLog::factory()->create([
            'status' => LogConstants::EMAIL_STATUS_FAILED,
            'email_type' => LogConstants::EMAIL_TYPE_REMINDER,
            'retry_count' => 2
        ]);

        // 測試狀態範圍查詢
        $sentLogs = EmailLog::sent()->get();
        $this->assertCount(1, $sentLogs);

        $failedLogs = EmailLog::failed()->get();
        $this->assertCount(1, $failedLogs);

        // 測試類型範圍查詢
        $invitationLogs = EmailLog::ofType(LogConstants::EMAIL_TYPE_INVITATION)->get();
        $this->assertCount(1, $invitationLogs);

        // 測試可重試範圍查詢
        $retryableLogs = EmailLog::retryable()->get();
        $this->assertCount(1, $retryableLogs);
    }

    /** @test */
    public function email_log_can_get_statistics()
    {
        EmailLog::factory()->count(8)->create([
            'status' => LogConstants::EMAIL_STATUS_SENT,
            'created_at' => now()->subDays(5)
        ]);
        
        EmailLog::factory()->count(2)->create([
            'status' => LogConstants::EMAIL_STATUS_FAILED,
            'created_at' => now()->subDays(5)
        ]);

        $stats = EmailLog::getStats(30);
        $this->assertIsArray($stats);
        $this->assertEquals(10, $stats['total']);
        $this->assertEquals(8, $stats['sent']);
        $this->assertEquals(2, $stats['failed']);
        $this->assertEquals(80, $stats['success_rate']);
    }

    /** @test */
    public function email_log_has_helper_methods()
    {
        $failedLog = EmailLog::factory()->create([
            'status' => LogConstants::EMAIL_STATUS_FAILED,
            'retry_count' => 1
        ]);
        
        $sentLog = EmailLog::factory()->create([
            'status' => LogConstants::EMAIL_STATUS_SENT
        ]);

        $this->assertTrue($failedLog->canRetry());
        $this->assertFalse($sentLog->canRetry());

        // 測試標記為已發送
        $result = $failedLog->markAsSent();
        $this->assertTrue($result);
        $this->assertEquals(LogConstants::EMAIL_STATUS_SENT, $failedLog->fresh()->status);

        // 測試標記為失敗
        $result = $sentLog->markAsFailed('測試錯誤');
        $this->assertTrue($result);
        $this->assertEquals(LogConstants::EMAIL_STATUS_FAILED, $sentLog->fresh()->status);
        $this->assertEquals('測試錯誤', $sentLog->fresh()->error_message);
    }
}
