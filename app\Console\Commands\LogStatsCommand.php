<?php

namespace App\Console\Commands;

use App\Services\LogService;
use App\Models\OperationLog;
use App\Models\SystemLog;
use App\Models\LoginLog;
use App\Models\EmailLog;
use Illuminate\Console\Command;

class LogStatsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'log:stats 
                            {--days=7 : 統計天數}
                            {--type= : 指定日誌類型 (operation|system|login|email)}
                            {--format=table : 輸出格式 (table|json)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '顯示日誌統計資訊';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        $type = $this->option('type');
        $format = $this->option('format');

        $this->info("📊 最近 {$days} 天的日誌統計");

        if ($type) {
            $this->showSpecificTypeStats($type, $days, $format);
        } else {
            $this->showComprehensiveStats($days, $format);
        }

        return 0;
    }

    /**
     * 顯示特定類型的統計
     */
    private function showSpecificTypeStats(string $type, int $days, string $format): void
    {
        $modelClass = $this->getModelClass($type);
        if (!$modelClass) {
            $this->error("❌ 不支援的日誌類型: {$type}");
            return;
        }

        $stats = LogService::getLogStatistics($modelClass, $days);

        if ($format === 'json') {
            $this->line(json_encode($stats, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            return;
        }

        $this->info("📈 {$type} 日誌統計:");
        $this->line("總記錄數: {$stats['total_logs']}");

        if (isset($stats['by_type'])) {
            $this->info("\n按類型分布:");
            foreach ($stats['by_type'] as $logType => $count) {
                $this->line("  {$logType}: {$count}");
            }
        }

        if (isset($stats['by_level'])) {
            $this->info("\n按級別分布:");
            foreach ($stats['by_level'] as $level => $count) {
                $this->line("  {$level}: {$count}");
            }
        }

        if (isset($stats['by_status'])) {
            $this->info("\n按狀態分布:");
            foreach ($stats['by_status'] as $status => $count) {
                $this->line("  {$status}: {$count}");
            }
        }
    }

    /**
     * 顯示綜合統計
     */
    private function showComprehensiveStats(int $days, string $format): void
    {
        $stats = LogService::getComprehensiveStats($days);

        if ($format === 'json') {
            $this->line(json_encode($stats, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            return;
        }

        // 操作日誌統計
        $this->info("📋 操作日誌:");
        $operationStats = $stats['operation_logs'] ?? [];
        $this->line("  總數: " . ($operationStats['total_logs'] ?? 0));
        if (isset($operationStats['by_level'])) {
            foreach ($operationStats['by_level'] as $level => $count) {
                $this->line("  {$level}: {$count}");
            }
        }

        // 系統日誌統計
        $this->info("\n🖥️  系統日誌:");
        $systemStats = $stats['system_logs'] ?? [];
        $this->line("  總數: " . ($systemStats['total_logs'] ?? 0));

        // 登入日誌統計
        $this->info("\n🔐 登入日誌:");
        $loginStats = $stats['login_logs'] ?? [];
        $this->line("  總數: " . ($loginStats['total_logs'] ?? 0));

        // 郵件日誌統計
        $this->info("\n📧 郵件日誌:");
        $emailStats = $stats['email_logs'] ?? [];
        $this->line("  總數: " . ($emailStats['total_logs'] ?? 0));
        if (isset($emailStats['by_status'])) {
            foreach ($emailStats['by_status'] as $status => $count) {
                $this->line("  {$status}: {$count}");
            }
        }

        // 最近錯誤
        $this->info("\n🚨 最近錯誤 (24小時):");
        $recentErrors = LogService::getRecentErrors(24, 5);
        if ($recentErrors->count() > 0) {
            foreach ($recentErrors as $error) {
                $this->line("  [{$error->created_at}] {$error->description}");
            }
        } else {
            $this->line("  無錯誤記錄");
        }
    }

    /**
     * 取得模型類別
     */
    private function getModelClass(string $type): ?string
    {
        $types = [
            'operation' => OperationLog::class,
            'system' => SystemLog::class,
            'login' => LoginLog::class,
            'email' => EmailLog::class,
        ];

        return $types[$type] ?? null;
    }
}
