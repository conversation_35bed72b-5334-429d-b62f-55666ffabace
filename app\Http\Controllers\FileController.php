<?php

namespace App\Http\Controllers;

use App\Services\FileStorageService;
use App\Services\LogService;
use App\Enums\LogConstants;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

/**
 * 檔案訪問控制器
 *
 * 處理PDF檔案的安全訪問、預覽和下載
 */
class FileController extends Controller
{
    protected FileStorageService $fileStorageService;

    public function __construct(FileStorageService $fileStorageService)
    {
        $this->fileStorageService = $fileStorageService;
    }

    /**
     * 預覽PDF檔案
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function previewPdf(Request $request)
    {
        try {
            $filePath = $request->query('path');

            if (!$filePath) {
                abort(400, '缺少檔案路徑參數');
            }

            $user = Auth::user();
            $content = $this->fileStorageService->getFileContent($filePath, $user);

            LogService::operationSuccess(
                'PDF檔案預覽',
                [
                    'user_id' => $user->id,
                    'user_role' => $user->role,
                    'file_path' => basename($filePath),
                    'file_size' => strlen($content)
                ],
                LogConstants::ACTION_VIEW
            );

            return response($content)
                ->header('Content-Type', 'application/pdf')
                ->header('Content-Disposition', 'inline; filename="' . basename($filePath) . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');
        } catch (\Exception $e) {
            Log::error('PDF預覽失敗', [
                'user_id' => Auth::id(),
                'file_path' => $request->query('path'),
                'error' => $e->getMessage()
            ]);

            LogService::operationFailure(
                'PDF檔案預覽失敗',
                [
                    'user_id' => Auth::id(),
                    'file_path' => basename($request->query('path') ?? ''),
                    'error' => $e->getMessage()
                ],
                LogConstants::LEVEL_ERROR
            );

            abort(403, $e->getMessage());
        }
    }
}
