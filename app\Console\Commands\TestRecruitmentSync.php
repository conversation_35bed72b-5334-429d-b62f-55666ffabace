<?php

namespace App\Console\Commands;

use App\Services\ExternalApiSyncService;
use App\Services\RecruitmentPeriodService;
use Illuminate\Console\Command;

/**
 * 測試招生期間同步命令
 */
class TestRecruitmentSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:recruitment-sync {--show-data : 顯示同步後的資料}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '測試招生期間資料同步功能';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('開始測試招生期間同步功能...');

        try {
            // 1. 測試外部API同步
            $this->info('1. 測試外部API同步...');
            $syncService = app(ExternalApiSyncService::class);
            $syncResult = $syncService->syncExamPeriods();

            if ($syncResult['success']) {
                $this->info("✓ 同步成功！更新了 {$syncResult['data']['updated_count']} 個招生期間");
            } else {
                $this->error("✗ 同步失敗：{$syncResult['message']}");
                return 1;
            }

            // 2. 測試招生期間服務
            $this->info('2. 測試招生期間服務...');
            $recruitmentService = app(RecruitmentPeriodService::class);

            $allPeriods = $recruitmentService->getAllPeriods();
            $this->info("✓ 獲取到 " . count($allPeriods) . " 個招生期間");

            $activePeriods = $recruitmentService->getActiveRecruitmentPeriods();
            $this->info("✓ 當前有 " . count($activePeriods) . " 個進行中的招生期間");

            $lastSyncTime = $recruitmentService->getLastSyncTime();
            if ($lastSyncTime) {
                $this->info("✓ 最後同步時間：{$lastSyncTime->toDateTimeString()}");
            }

            // 3. 顯示詳細資料（如果要求）
            if ($this->option('show-data')) {
                $this->info('3. 招生期間詳細資料：');
                $this->table(
                    ['考試ID', '考試名稱', '開始時間', '結束時間', '狀態'],
                    array_map(function ($period) use ($recruitmentService) {
                        $info = $recruitmentService->getPeriodInfo($period['exam_id']);
                        return [
                            $period['exam_id'],
                            $period['exam_name'] ?? 'N/A',
                            $period['app_date1_start'] ?? 'N/A',
                            $period['app_date1_end'] ?? 'N/A',
                            $info['status'] ?? 'unknown'
                        ];
                    }, $allPeriods)
                );
            }

            // 4. 測試特定考試期間檢查
            if (!empty($allPeriods)) {
                $this->info('4. 測試特定考試期間檢查...');
                $testExamId = $allPeriods[0]['exam_id'];
                $isInPeriod = $recruitmentService->isInRecruitmentPeriod($testExamId);
                $periodInfo = $recruitmentService->getPeriodInfo($testExamId);

                $this->info("✓ 測試考試ID {$testExamId}：");
                $this->info("  - 是否在招生期間：" . ($isInPeriod ? '是' : '否'));
                $this->info("  - 期間狀態：{$periodInfo['status']}");
            }

            $this->info('');
            $this->info('🎉 所有測試通過！招生期間同步功能正常運作。');

            return 0;

        } catch (\Exception $e) {
            $this->error("✗ 測試失敗：{$e->getMessage()}");
            $this->error("錯誤詳情：{$e->getTraceAsString()}");
            return 1;
        }
    }
}
