<?php

namespace Tests\Feature;

use App\Services\LogService;
use App\Enums\LogConstants;
use App\Models\OperationLog;
use App\Models\SystemLog;
use App\Models\LoginLog;
use App\Models\EmailLog;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Tests\TestCase;

class LogIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create([
            'role' => 'admin'
        ]);
    }

    /** @test */
    public function it_can_record_complete_user_session_flow()
    {
        // 1. 記錄登入成功
        Auth::login($this->user);
        $loginLog = LogService::loginSuccess($this->user->id);
        
        $this->assertInstanceOf(LoginLog::class, $loginLog);
        $this->assertTrue($loginLog->success);
        $this->assertEquals($this->user->id, $loginLog->user_id);

        // 2. 記錄用戶操作
        $operationLog = LogService::operationSuccess(
            '用戶查看推薦函列表',
            ['page' => 1, 'limit' => 20],
            LogConstants::ACTION_VIEW
        );
        
        $this->assertInstanceOf(OperationLog::class, $operationLog);
        $this->assertEquals($this->user->id, $operationLog->user_id);

        // 3. 記錄API訪問
        $apiLog = LogService::apiAccess(
            ['endpoint' => '/api/recommendations'],
            ['count' => 5],
            200
        );
        
        $this->assertInstanceOf(SystemLog::class, $apiLog);
        $this->assertEquals(200, $apiLog->status_code);

        // 4. 記錄郵件發送
        $emailLog = LogService::emailSent(
            '<EMAIL>',
            '推薦函邀請',
            LogConstants::EMAIL_TYPE_INVITATION
        );
        
        $this->assertInstanceOf(EmailLog::class, $emailLog);
        $this->assertEquals(LogConstants::EMAIL_STATUS_SENT, $emailLog->status);

        // 驗證所有記錄都已創建
        $this->assertDatabaseCount('login_logs', 1);
        $this->assertDatabaseCount('operation_logs', 1);
        $this->assertDatabaseCount('system_logs', 1);
        $this->assertDatabaseCount('email_logs', 1);
    }

    /** @test */
    public function it_can_handle_batch_logging()
    {
        Auth::login($this->user);
        
        // 啟用批量模式
        LogService::enableAsync();

        // 記錄多個操作
        for ($i = 1; $i <= 5; $i++) {
            LogService::operationSuccess("批量操作 {$i}");
        }

        // 在批量模式下，資料庫中應該沒有記錄
        $this->assertDatabaseCount('operation_logs', 0);

        // 刷新批量緩存
        $insertedCount = LogService::flushBatch();
        
        // 現在應該有5條記錄
        $this->assertEquals(5, $insertedCount);
        $this->assertDatabaseCount('operation_logs', 5);

        // 停用批量模式
        LogService::disableAsync();
    }

    /** @test */
    public function it_can_generate_comprehensive_statistics()
    {
        Auth::login($this->user);
        
        // 創建各種類型的日誌
        OperationLog::factory()->count(10)->create([
            'type' => LogConstants::OPERATION_TYPE_OPERATION,
            'level' => LogConstants::LEVEL_INFO,
            'created_at' => now()->subDays(5)
        ]);
        
        OperationLog::factory()->count(3)->create([
            'type' => LogConstants::OPERATION_TYPE_ERROR,
            'level' => LogConstants::LEVEL_ERROR,
            'created_at' => now()->subDays(5)
        ]);

        SystemLog::factory()->count(15)->create([
            'status_code' => 200,
            'created_at' => now()->subDays(5)
        ]);
        
        SystemLog::factory()->count(2)->create([
            'status_code' => 500,
            'created_at' => now()->subDays(5)
        ]);

        LoginLog::factory()->count(8)->create([
            'success' => true,
            'login_at' => now()->subDays(5)
        ]);
        
        LoginLog::factory()->count(2)->create([
            'success' => false,
            'login_at' => now()->subDays(5)
        ]);

        EmailLog::factory()->count(12)->create([
            'status' => LogConstants::EMAIL_STATUS_SENT,
            'created_at' => now()->subDays(5)
        ]);
        
        EmailLog::factory()->count(3)->create([
            'status' => LogConstants::EMAIL_STATUS_FAILED,
            'created_at' => now()->subDays(5)
        ]);

        // 取得綜合統計
        $stats = LogService::getComprehensiveStats(30);

        $this->assertIsArray($stats);
        $this->assertArrayHasKey('operation_logs', $stats);
        $this->assertArrayHasKey('system_logs', $stats);
        $this->assertArrayHasKey('login_logs', $stats);
        $this->assertArrayHasKey('email_logs', $stats);

        // 驗證操作日誌統計
        $operationStats = $stats['operation_logs'];
        $this->assertEquals(13, $operationStats['total_logs']);
        $this->assertEquals(10, $operationStats['by_type'][LogConstants::OPERATION_TYPE_OPERATION]);
        $this->assertEquals(3, $operationStats['by_type'][LogConstants::OPERATION_TYPE_ERROR]);

        // 驗證系統日誌統計
        $systemStats = $stats['system_logs'];
        $this->assertEquals(17, $systemStats['total_logs']);

        // 驗證登入日誌統計
        $loginStats = $stats['login_logs'];
        $this->assertEquals(10, $loginStats['total_logs']);

        // 驗證郵件日誌統計
        $emailStats = $stats['email_logs'];
        $this->assertEquals(15, $emailStats['total_logs']);
        $this->assertEquals(12, $emailStats['by_status'][LogConstants::EMAIL_STATUS_SENT]);
        $this->assertEquals(3, $emailStats['by_status'][LogConstants::EMAIL_STATUS_FAILED]);
    }

    /** @test */
    public function it_can_cleanup_old_logs()
    {
        Auth::login($this->user);
        
        // 創建舊的日誌記錄
        OperationLog::factory()->count(5)->create([
            'created_at' => now()->subDays(800) // 超過預設保留期限
        ]);
        
        OperationLog::factory()->count(3)->create([
            'created_at' => now()->subDays(100) // 在保留期限內
        ]);

        SystemLog::factory()->count(4)->create([
            'created_at' => now()->subDays(400) // 超過系統日誌保留期限
        ]);
        
        SystemLog::factory()->count(2)->create([
            'created_at' => now()->subDays(50) // 在保留期限內
        ]);

        // 清理舊的操作日誌（保留730天）
        $deletedOperationLogs = LogService::cleanupOldLogs(OperationLog::class, 730);
        $this->assertEquals(5, $deletedOperationLogs);
        $this->assertDatabaseCount('operation_logs', 3);

        // 清理舊的系統日誌（保留365天）
        $deletedSystemLogs = LogService::cleanupOldLogs(SystemLog::class, 365);
        $this->assertEquals(4, $deletedSystemLogs);
        $this->assertDatabaseCount('system_logs', 2);
    }

    /** @test */
    public function it_can_track_user_activity()
    {
        Auth::login($this->user);
        
        // 創建用戶活動記錄
        OperationLog::factory()->count(5)->create([
            'user_id' => $this->user->id,
            'created_at' => now()->subDays(2)
        ]);
        
        LoginLog::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'login_at' => now()->subDays(1)
        ]);

        // 創建其他用戶的記錄（不應該包含在結果中）
        $otherUser = User::factory()->create();
        OperationLog::factory()->count(2)->create([
            'user_id' => $otherUser->id,
            'created_at' => now()->subDays(1)
        ]);

        $activity = LogService::getUserRecentActivity($this->user->id, 7);

        $this->assertIsArray($activity);
        $this->assertArrayHasKey('operations', $activity);
        $this->assertArrayHasKey('logins', $activity);
        
        $this->assertCount(5, $activity['operations']);
        $this->assertCount(3, $activity['logins']);
        
        // 確保只包含指定用戶的記錄
        $activity['operations']->each(function ($log) {
            $this->assertEquals($this->user->id, $log->user_id);
        });
        
        $activity['logins']->each(function ($log) {
            $this->assertEquals($this->user->id, $log->user_id);
        });
    }

    /** @test */
    public function it_can_identify_recent_errors()
    {
        Auth::login($this->user);
        
        // 創建最近的錯誤記錄
        OperationLog::factory()->count(3)->create([
            'level' => LogConstants::LEVEL_ERROR,
            'created_at' => now()->subHours(2)
        ]);
        
        OperationLog::factory()->count(2)->create([
            'level' => LogConstants::LEVEL_CRITICAL,
            'created_at' => now()->subHours(1)
        ]);
        
        // 創建非錯誤記錄（不應該包含在結果中）
        OperationLog::factory()->count(5)->create([
            'level' => LogConstants::LEVEL_INFO,
            'created_at' => now()->subHours(1)
        ]);
        
        // 創建舊的錯誤記錄（不應該包含在結果中）
        OperationLog::factory()->count(2)->create([
            'level' => LogConstants::LEVEL_ERROR,
            'created_at' => now()->subDays(2)
        ]);

        $recentErrors = LogService::getRecentErrors(24, 10);

        $this->assertCount(5, $recentErrors);
        
        $recentErrors->each(function ($error) {
            $this->assertContains($error->level, [
                LogConstants::LEVEL_ERROR,
                LogConstants::LEVEL_CRITICAL
            ]);
            $this->assertTrue($error->created_at->isAfter(now()->subHours(24)));
        });
    }
}
