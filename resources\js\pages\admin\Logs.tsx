import React, { useState } from 'react';
import { Head, useForm, router } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select';
import { Badge } from '@/components/ui/Badge';
import { Activity, Mail, ScanFace, Database, Search, Download, Trash2, Eye } from 'lucide-react';

interface LogsProps {
    logs: {
        data: Array<any>;
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    statistics: {
        system: {
            total: number;
            today: number;
            errors: number;
        };
        email: {
            total: number;
            today: number;
            failed: number;
        };
        login: {
            total: number;
            today: number;
            failed: number;
        };
    };
    current_type: string;
    filters: {
        search: string | null;
        date_from: string | null;
        date_to: string | null;
        page: number;
        per_page: number;
    };
}

/**
 * 日誌管理頁面
 */
export default function Logs({ logs, statistics, current_type, filters }: LogsProps) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [dateFrom, setDateFrom] = useState(filters.date_from || '');
    const [dateTo, setDateTo] = useState(filters.date_to || '');
    const [selectedLogType, setSelectedLogType] = useState(current_type);

    const cleanupForm = useForm({
        log_type: current_type,
        days: 30,
    });

    const exportForm = useForm({
        log_type: current_type,
        format: 'csv',
        date_from: dateFrom,
        date_to: dateTo,
    });

    const handleSearch = () => {
        router.get(route('admin.logs.index'), {
            type: selectedLogType,
            search: searchTerm,
            date_from: dateFrom,
            date_to: dateTo,
            page: 1,
        });
    };

    const handleLogTypeChange = (type: string) => {
        setSelectedLogType(type);
        router.get(route('admin.logs.index'), {
            type: type,
            search: searchTerm,
            date_from: dateFrom,
            date_to: dateTo,
            page: 1,
        });
    };

    const handleCleanup = (e: React.FormEvent) => {
        e.preventDefault();
        if (confirm(`確定要清理 ${cleanupForm.data.days} 天前的 ${cleanupForm.data.log_type} 日誌嗎？`)) {
            cleanupForm.post(route('admin.logs.cleanup'));
        }
    };

    const handleExport = (e: React.FormEvent) => {
        e.preventDefault();
        exportForm.post(route('admin.logs.export'));
    };

    const getLogTypeIcon = (type: string) => {
        switch (type) {
            case 'system':
                return <Database className="h-4 w-4" />;
            case 'email':
                return <Mail className="h-4 w-4" />;
            case 'login':
                return <ScanFace className="h-4 w-4" />;
            case 'operation':
                return <Activity className="h-4 w-4" />;
            default:
                return <Activity className="h-4 w-4" />;
        }
    };

    const getLogTypeName = (type: string) => {
        switch (type) {
            case 'system':
                return '系統日誌';
            case 'email':
                return '郵件日誌';
            case 'login':
                return '登入日誌';
            case 'operation':
                return '操作日誌';
            default:
                return '未知日誌';
        }
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'success':
                return (
                    <Badge variant="default" className="bg-green-500">
                        成功
                    </Badge>
                );
            case 'failed':
            case 'error':
                return <Badge variant="destructive">失敗</Badge>;
            case 'pending':
                return <Badge variant="secondary">處理中</Badge>;
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    const formatDateTime = (dateString: string) => {
        return new Date(dateString).toLocaleString('zh-TW');
    };

    return (
        <AdminLayout>
            <Head title="日誌管理" />

            <div className="space-y-6 p-6">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">日誌管理</h1>
                    <p className="text-muted-foreground">統一管理系統的各類日誌，包括系統日誌、郵件日誌、登入日誌等</p>
                </div>

                {/* 統計卡片 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">系統日誌</CardTitle>
                            <Database className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{statistics.system.total}</div>
                            <p className="text-xs text-muted-foreground">
                                今日: {statistics.system.today} | 錯誤: {statistics.system.errors}
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">郵件日誌</CardTitle>
                            <Mail className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{statistics.email.total}</div>
                            <p className="text-xs text-muted-foreground">
                                今日: {statistics.email.today} | 失敗: {statistics.email.failed}
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">登入日誌</CardTitle>
                            <ScanFace className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{statistics.login.total}</div>
                            <p className="text-xs text-muted-foreground">
                                今日: {statistics.login.today} | 失敗: {statistics.login.failed}
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* 日誌管理 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            {getLogTypeIcon(selectedLogType)}
                            {getLogTypeName(selectedLogType)}
                        </CardTitle>
                        <CardDescription>查看和管理 {getLogTypeName(selectedLogType)}</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-6">
                            {/* 篩選器 */}
                            <div className="flex flex-wrap gap-4">
                                <div className="min-w-[200px] flex-1">
                                    <Label htmlFor="log_type">日誌類型</Label>
                                    <Select value={selectedLogType} onValueChange={handleLogTypeChange}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="system">系統日誌</SelectItem>
                                            <SelectItem value="email">郵件日誌</SelectItem>
                                            <SelectItem value="login">登入日誌</SelectItem>
                                            <SelectItem value="operation">操作日誌</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="min-w-[200px] flex-1">
                                    <Label htmlFor="search">搜尋</Label>
                                    <Input
                                        id="search"
                                        placeholder="搜尋日誌內容..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>

                                <div className="min-w-[150px] flex-1">
                                    <Label htmlFor="date_from">開始日期</Label>
                                    <Input id="date_from" type="date" value={dateFrom} onChange={(e) => setDateFrom(e.target.value)} />
                                </div>

                                <div className="min-w-[150px] flex-1">
                                    <Label htmlFor="date_to">結束日期</Label>
                                    <Input id="date_to" type="date" value={dateTo} onChange={(e) => setDateTo(e.target.value)} />
                                </div>

                                <div className="flex items-end">
                                    <Button onClick={handleSearch} className="flex items-center gap-2">
                                        <Search className="h-4 w-4" />
                                        搜尋
                                    </Button>
                                </div>
                            </div>

                            {/* 操作按鈕 */}
                            <div className="flex gap-2">
                                <form onSubmit={handleExport} className="flex gap-2">
                                    <Select value={exportForm.data.format} onValueChange={(value) => exportForm.setData('format', value)}>
                                        <SelectTrigger className="w-[120px]">
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="csv">CSV</SelectItem>
                                            <SelectItem value="excel">Excel</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <Button type="submit" variant="outline" className="flex items-center gap-2">
                                        <Download className="h-4 w-4" />
                                        匯出
                                    </Button>
                                </form>

                                <form onSubmit={handleCleanup} className="flex gap-2">
                                    <Input
                                        type="number"
                                        min="1"
                                        max="365"
                                        value={cleanupForm.data.days}
                                        onChange={(e) => cleanupForm.setData('days', parseInt(e.target.value))}
                                        className="w-[100px]"
                                        placeholder="天數"
                                    />
                                    <Button type="submit" variant="destructive" className="flex items-center gap-2">
                                        <Trash2 className="h-4 w-4" />
                                        清理
                                    </Button>
                                </form>
                            </div>

                            {/* 日誌列表 */}
                            <div className="space-y-2">
                                {logs.data.map((log, index) => (
                                    <div key={index} className="rounded-lg border p-4 hover:bg-muted/50">
                                        <div className="flex items-center justify-between">
                                            <div className="flex-1">
                                                <div className="mb-2 flex items-center gap-2">
                                                    <span className="font-medium">{log.message || log.subject || log.action}</span>
                                                    {log.status && getStatusBadge(log.status)}
                                                    {log.level && (
                                                        <Badge variant={log.level === 'error' ? 'destructive' : 'outline'}>{log.level}</Badge>
                                                    )}
                                                </div>
                                                <div className="text-sm text-muted-foreground">
                                                    {formatDateTime(log.created_at)}
                                                    {log.email && ` | ${log.email}`}
                                                    {log.ip_address && ` | ${log.ip_address}`}
                                                </div>
                                            </div>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => router.get(route('admin.logs.show', log.id), { type: selectedLogType })}
                                                className="flex items-center gap-2"
                                            >
                                                <Eye className="h-4 w-4" />
                                                查看
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>

                            {/* 分頁 */}
                            {logs.last_page > 1 && (
                                <div className="flex justify-center gap-2">
                                    {Array.from({ length: logs.last_page }, (_, i) => i + 1).map((page) => (
                                        <Button
                                            key={page}
                                            variant={page === logs.current_page ? 'default' : 'outline'}
                                            size="sm"
                                            onClick={() =>
                                                router.get(route('admin.logs.index'), {
                                                    ...filters,
                                                    type: selectedLogType,
                                                    page: page,
                                                })
                                            }
                                        >
                                            {page}
                                        </Button>
                                    ))}
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
