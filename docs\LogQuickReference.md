# Log 系統快速參考

## 🚀 快速開始

```php
use App\Services\LogService;
use App\Enums\LogConstants;

// 操作成功
LogService::operationSuccess('推薦函創建成功', ['id' => 123]);

// 操作失敗
LogService::operationFailure('推薦函創建失敗', ['error' => '驗證失敗']);

// 系統日誌
LogService::system(['request' => $data], ['response' => $result], 200);

// 登入成功/失敗
LogService::loginSuccess();
LogService::loginFailure('密碼錯誤');

// 郵件日誌
LogService::emailSent('<EMAIL>', '邀請信', LogConstants::EMAIL_TYPE_INVITATION);
LogService::emailFailed('<EMAIL>', '邀請信', 'SMTP錯誤', LogConstants::EMAIL_TYPE_INVITATION);
```

## 📊 常用查詢

```php
use App\Models\{OperationLog, SystemLog, LoginLog, EmailLog};

// 最近錯誤
$errors = OperationLog::errors()->recent(7)->get();

// 失敗登入
$failedLogins = LoginLog::failed()->recent(1)->get();

// 伺服器錯誤
$serverErrors = SystemLog::serverErrors()->recent(24)->get();

// 失敗郵件
$failedEmails = EmailLog::failed()->retryable()->get();
```

## 📈 統計資料

```php
// 基本統計
$stats = LogService::getLogStatistics(OperationLog::class, 30);

// 綜合統計
$allStats = LogService::getComprehensiveStats(30);

// 模型統計
$typeStats = OperationLog::getTypeStatistics(30);
$loginStats = LoginLog::getSuccessRateStats(30);
$emailStats = EmailLog::getStats(30);
```

## 🔧 批量處理

```php
// 啟用批量模式
LogService::enableAsync();

// 記錄大量日誌...
for ($i = 0; $i < 1000; $i++) {
    LogService::operationSuccess("批量操作 {$i}");
}

// 刷新緩存
LogService::flushBatch();

// 停用批量模式
LogService::disableAsync();
```

## 🧹 清理舊日誌

```php
// 清理指定天數前的日誌
LogService::cleanupOldLogs(OperationLog::class, 730);
LogService::cleanupOldLogs(SystemLog::class, 365);
LogService::cleanupOldLogs(LoginLog::class, 365);
LogService::cleanupOldLogs(EmailLog::class, 180);
```

## 🎯 常用常數

```php
// 操作類型
LogConstants::OPERATION_TYPE_OPERATION
LogConstants::OPERATION_TYPE_ERROR
LogConstants::OPERATION_TYPE_SECURITY

// 日誌級別
LogConstants::LEVEL_INFO
LogConstants::LEVEL_WARNING
LogConstants::LEVEL_ERROR
LogConstants::LEVEL_CRITICAL

// 操作動作
LogConstants::ACTION_CREATE
LogConstants::ACTION_UPDATE
LogConstants::ACTION_DELETE
LogConstants::ACTION_VIEW

// 郵件類型
LogConstants::EMAIL_TYPE_INVITATION
LogConstants::EMAIL_TYPE_REMINDER
LogConstants::EMAIL_TYPE_NOTIFICATION
LogConstants::EMAIL_TYPE_ALERT

// 郵件狀態
LogConstants::EMAIL_STATUS_PENDING
LogConstants::EMAIL_STATUS_SENT
LogConstants::EMAIL_STATUS_FAILED
```

## 🔍 進階查詢範例

```php
// 查詢特定用戶的操作
$userOps = OperationLog::forUser($userId)
    ->ofType(LogConstants::OPERATION_TYPE_OPERATION)
    ->recent(30)
    ->get();

// 查詢可疑登入
$suspicious = LoginLog::failed()
    ->where('created_at', '>=', now()->subHours(1))
    ->selectRaw('ip_address, COUNT(*) as attempts')
    ->groupBy('ip_address')
    ->having('attempts', '>=', 5)
    ->get();

// 查詢高優先級失敗郵件
$criticalEmails = EmailLog::failed()
    ->whereIn('email_type', [
        LogConstants::EMAIL_TYPE_ALERT,
        LogConstants::EMAIL_TYPE_RESET_PASSWORD
    ])
    ->get();
```

## ⚙️ 配置要點

```bash
# .env 重要配置
LOG_RETENTION_OPERATION_DAYS=730
LOG_RETENTION_SYSTEM_DAYS=365
```

## 💡 最佳實踐

1. **選擇正確的日誌類型**
    - 業務操作 → OperationLog
    - 技術層面 → SystemLog
    - 登入活動 → LoginLog
    - 郵件發送 → EmailLog

2. **提供有意義的描述**

    ```php
    // ✅ 好的描述
    LogService::operationSuccess('推薦函 #123 已由 <EMAIL> 提交');

    // ❌ 不好的描述
    LogService::operationSuccess('操作成功');
    ```

3. **合理使用 metadata**

    ```php
    LogService::operationFailure('推薦函創建失敗', [
        'user_id' => $user->id,
        'department' => $request->department,
        'error_code' => 'VALIDATION_FAILED'
    ]);
    ```

4. **避免記錄敏感資訊**

    ```php
    // ❌ 危險
    LogService::operationSuccess('密碼更新', ['password' => $newPassword]);

    // ✅ 安全
    LogService::operationSuccess('密碼更新', ['user_id' => $userId]);
    ```

5. **高頻操作使用批量模式**
    ```php
    if ($isHighFrequency) {
        LogService::enableAsync();
    }
    ```

## 🚨 故障排除

### 常見問題

1. **日誌記錄失敗**
    - 檢查資料庫連接
    - 確認表結構正確
    - 查看 Laravel 日誌檔案

2. **效能問題**
    - 啟用批量模式
    - 定期清理舊日誌
    - 考慮使用佇列

3. **查詢緩慢**
    - 檢查資料庫索引
    - 限制查詢範圍
    - 使用分頁

### 除錯技巧

```php
// 檢查日誌記錄是否成功
$log = LogService::operationSuccess('測試');
if (!$log) {
    Log::error('日誌記錄失敗');
}

// 查看批量緩存狀態
LogService::enableAsync();
// ... 記錄日誌
$inserted = LogService::flushBatch();
Log::info("批量插入了 {$inserted} 條記錄");
```

---

**完整文檔**: [LogSystem.md](./LogSystem.md)  
**最後更新**: 2024-08-26
