<?php

namespace App\Models;

use App\Enums\LogConstants;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 登入日誌模型
 *
 * 記錄用戶登入活動
 */
class LoginLog extends Model
{
    use HasFactory;

    /**
     * 可批量賦值的屬性
     */
    protected $fillable = [
        'user_id',
        'ip_address',
        'user_agent',
        'success',
        'failure_reason',
        'login_at',
    ];

    /**
     * 屬性類型轉換
     */
    protected $casts = [
        'success' => 'boolean',
        'login_at' => 'datetime',
    ];

    public const LOGIN_SUCCESS = LogConstants::LOGIN_SUCCESS;
    public const LOGIN_FAILURE = LogConstants::LOGIN_FAILURE;

    /**
     * 關聯用戶
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 範圍查詢：成功的登入
     */
    public function scopeSuccessful($query)
    {
        return $query->where('success', true);
    }

    /**
     * 範圍查詢：失敗的登入
     */
    public function scopeFailed($query)
    {
        return $query->where('success', false);
    }

    /**
     * 範圍查詢：最近的登入
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('login_at', '>=', now()->subDays($days));
    }

    /**
     * 範圍查詢：按用戶
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 範圍查詢：按IP地址
     */
    public function scopeFromIp($query, string $ip)
    {
        return $query->where('ip_address', $ip);
    }



    /**
     * 範圍查詢：今日登入
     */
    public function scopeToday($query)
    {
        return $query->whereDate('login_at', today());
    }

    /**
     * 範圍查詢：本週登入
     */
    public function scopeThisWeek($query)
    {
        return $query->whereBetween('login_at', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    #region: 統計方法

    /**
     * 取得登入成功率統計
     */
    public static function getSuccessRateStats(int $days = 30): array
    {
        $total = self::where('login_at', '>=', now()->subDays($days))->count();
        $successful = self::successful()
            ->where('login_at', '>=', now()->subDays($days))
            ->count();

        return [
            'total_attempts' => $total,
            'successful_logins' => $successful,
            'failed_logins' => $total - $successful,
            'success_rate' => $total > 0 ? round(($successful / $total) * 100, 2) : 0,
        ];
    }

    /**
     * 取得每日登入統計
     */
    public static function getDailyLoginStats(int $days = 30): array
    {
        return self::where('login_at', '>=', now()->subDays($days))
            ->selectRaw('DATE(login_at) as date,
                        COUNT(*) as total,
                        SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful,
                        SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date')
            ->toArray();
    }

    /**
     * 取得IP地址統計
     */
    public static function getIpStats(int $days = 30, int $limit = 20): array
    {
        return self::where('login_at', '>=', now()->subDays($days))
            ->selectRaw('ip_address,
                        COUNT(*) as total,
                        SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful,
                        SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed')
            ->groupBy('ip_address')
            ->orderByDesc('total')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * 取得失敗原因統計
     */
    public static function getFailureReasonStats(int $days = 30): array
    {
        return self::failed()
            ->where('login_at', '>=', now()->subDays($days))
            ->whereNotNull('failure_reason')
            ->selectRaw('failure_reason, COUNT(*) as count')
            ->groupBy('failure_reason')
            ->orderByDesc('count')
            ->pluck('count', 'failure_reason')
            ->toArray();
    }

    /**
     * 取得用戶登入頻率統計
     */
    public static function getUserLoginFrequency(int $days = 30): array
    {
        return self::successful()
            ->where('login_at', '>=', now()->subDays($days))
            ->whereNotNull('user_id')
            ->with('user:id,name,email')
            ->selectRaw('user_id, COUNT(*) as login_count')
            ->groupBy('user_id')
            ->orderByDesc('login_count')
            ->limit(20)
            ->get()
            ->toArray();
    }

    /**
     * 取得每小時登入分佈
     */
    public static function getHourlyDistribution(int $days = 30): array
    {
        return self::where('login_at', '>=', now()->subDays($days))
            ->selectRaw('HOUR(login_at) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour')
            ->toArray();
    }

    #endregion

    #region: 輔助方法

    /**
     * 檢查是否為可疑登入
     */
    public function isSuspicious(): bool
    {
        // 檢查是否在短時間內有多次失敗嘗試
        $recentFailures = self::failed()
            ->where('ip_address', $this->ip_address)
            ->where('login_at', '>=', now()->subMinutes(15))
            ->count();

        return $recentFailures >= 5;
    }

    /**
     * 取得登入狀態文字
     */
    public function getStatusTextAttribute(): string
    {
        return $this->success ? '成功' : '失敗';
    }



    #endregion
}
