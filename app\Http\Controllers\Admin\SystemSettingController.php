<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SystemSetting;
use App\Services\LogService;
use App\Enums\LogConstants;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

/**
 * 系統設定控制器
 *
 * 管理系統的各種設定參數
 */
class SystemSettingController extends Controller
{
    /**
     * 顯示系統設定頁面
     *
     * @return \Inertia\Response
     */
    public function index()
    {
        try {
            $settings = [
                'timing' => SystemSetting::getByCategory(SystemSetting::CATEGORY_TIMING),
                'general' => SystemSetting::getByCategory(SystemSetting::CATEGORY_GENERAL),
                'security' => SystemSetting::getByCategory(SystemSetting::CATEGORY_SECURITY),
                'email' => SystemSetting::getByCategory(SystemSetting::CATEGORY_EMAIL),
            ];

            // 系統狀態資訊
            $systemStatus = [
                'is_maintenance_mode' => SystemSetting::isMaintenanceMode(),
                'reminder_cooldown_hours' => SystemSetting::getReminderCooldownHours(),
                'auto_timeout_days' => SystemSetting::getAutoTimeoutDays(),
                'allow_pdf_upload' => SystemSetting::isAllowPdfUpload(),
                'allow_questionnaire_submission' => SystemSetting::isAllowQuestionnaireSubmission(),
                'support_tel' => SystemSetting::getSupportTel(),
                'support_email' => SystemSetting::getSupportEmail(),
                'admin_email' => SystemSetting::getAdminEmail(),
            ];

            LogService::operationSuccess(
                '查看系統設定頁面',
                ['page' => 'system_settings'],
                LogConstants::ACTION_VIEW
            );

            return Inertia::render('admin/SystemSettings', [
                'settings' => $settings,
                'system_status' => $systemStatus,
            ]);
        } catch (\Exception $e) {
            LogService::operationFailure(
                '查看系統設定頁面失敗',
                ['error' => $e->getMessage()],
                LogConstants::LEVEL_ERROR,
                LogConstants::ACTION_VIEW
            );

            return back()->withErrors([
                'system' => '載入系統設定失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 更新系統設定
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        try {
            // 驗證前端表單資料
            $validator = Validator::make($request->all(), [
                'reminder_cooldown' => 'required|integer|min:1|max:168',
                'auto_timeout' => 'required|integer|min:1|max:30',
                'support_tel' => 'nullable|string|max:50',
                'support_email' => 'nullable|email|max:255',
                'admin_email' => 'nullable|email|max:255',
                'allow_pdf_upload' => 'boolean',
                'allow_questionnaire_submission' => 'boolean',
            ]);

            if ($validator->fails()) {
                return back()->withErrors($validator->errors());
            }

            $updatedSettings = [];
            $user = Auth::user();

            // 定義設定映射
            $settingsMap = [
                'reminder_cooldown' => [
                    'key' => SystemSetting::REMINDER_COOLDOWN_HOURS,
                    'type' => SystemSetting::TYPE_INTEGER,
                    'category' => SystemSetting::CATEGORY_TIMING,
                    'description' => '提醒郵件冷卻時間（小時）',
                ],
                'auto_timeout' => [
                    'key' => SystemSetting::AUTO_TIMEOUT_DAYS,
                    'type' => SystemSetting::TYPE_INTEGER,
                    'category' => SystemSetting::CATEGORY_TIMING,
                    'description' => '自動超時天數',
                ],
                'support_tel' => [
                    'key' => SystemSetting::SYSTEM_SUPPORT_TEL,
                    'type' => SystemSetting::TYPE_STRING,
                    'category' => SystemSetting::CATEGORY_GENERAL,
                    'description' => '技術支援電話',
                ],
                'support_email' => [
                    'key' => SystemSetting::SYSTEM_SUPPORT_EMAIL,
                    'type' => SystemSetting::TYPE_STRING,
                    'category' => SystemSetting::CATEGORY_GENERAL,
                    'description' => '技術支援信箱',
                ],
                'admin_email' => [
                    'key' => SystemSetting::SYSTEM_ADMIN_EMAIL,
                    'type' => SystemSetting::TYPE_STRING,
                    'category' => SystemSetting::CATEGORY_GENERAL,
                    'description' => '系統管理員信箱',
                ],
                'allow_pdf_upload' => [
                    'key' => SystemSetting::ALLOW_PDF_UPLOAD,
                    'type' => SystemSetting::TYPE_BOOLEAN,
                    'category' => SystemSetting::CATEGORY_GENERAL,
                    'description' => '允許PDF上傳',
                ],
                'allow_questionnaire_submission' => [
                    'key' => SystemSetting::ALLOW_QUESTIONNAIRE_SUBMISSION,
                    'type' => SystemSetting::TYPE_BOOLEAN,
                    'category' => SystemSetting::CATEGORY_GENERAL,
                    'description' => '允許線上問卷提交',
                ],
            ];

            // 更新每個設定
            foreach ($settingsMap as $formField => $settingConfig) {
                if ($request->has($formField)) {
                    $value = $request->input($formField);

                    // 處理空值
                    if ($value === null || $value === '') {
                        continue;
                    }

                    $success = SystemSetting::set(
                        $settingConfig['key'],
                        $value,
                        $settingConfig['type'],
                        $settingConfig['category'],
                        $settingConfig['description']
                    );

                    if ($success) {
                        $updatedSettings[] = $settingConfig['key'];
                    }
                }
            }

            // 記錄操作日誌
            LogService::securityOperation(
                "系統設定更新成功",
                [
                    'admin_id' => $user->id,
                    'admin_name' => $user->name,
                    'updated_settings' => $updatedSettings,
                ]
            );

            return back()->with('success', '系統設定已成功更新');
        } catch (\Exception $e) {
            LogService::securityOperation(
                "更新系統設定失敗: {$e->getMessage()}",
                ['request_data' => $request->all()]
            );

            return back()->withErrors([
                'system' => '更新系統設定失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 更新單一設定
     *
     * @param Request $request
     * @param string $key
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateSingle(Request $request, string $key)
    {
        try {
            $validator = Validator::make($request->all(), [
                'value' => 'required',
                'type' => 'required|string|in:string,json,boolean,datetime,integer',
                'category' => 'required|string',
                'description' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '驗證失敗',
                    'errors' => $validator->errors()
                ], 422);
            }

            $success = SystemSetting::set(
                $key,
                $request->input('value'),
                $request->input('type'),
                $request->input('category'),
                $request->input('description')
            );

            if (!$success) {
                return response()->json([
                    'success' => false,
                    'message' => '設定更新失敗'
                ], 500);
            }

            $user = Auth::user();
            LogService::securityOperation(
                "管理員 {$user->name} 更新了設定: {$key}",
                [
                    'setting_key' => $key,
                    'new_value' => $request->input('value'),
                    'type' => $request->input('type'),
                    'admin_id' => $user->id,
                ]
            );

            return response()->json([
                'success' => true,
                'message' => '設定已成功更新'
            ]);
        } catch (\Exception $e) {
            LogService::operationFailure(
                "更新單一設定失敗: {$key}",
                [
                    'error' => $e->getMessage(),
                    'setting_key' => $key,
                    'request_data' => $request->all()
                ],
                LogConstants::LEVEL_ERROR,
                LogConstants::ACTION_SYSTEM_SETTING
            );

            return response()->json([
                'success' => false,
                'message' => '設定更新失敗，請稍後再試'
            ], 500);
        }
    }

    /**
     * 重置為預設設定
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function resetDefaults()
    {
        try {
            SystemSetting::initializeDefaults();

            $user = Auth::user();
            LogService::securityOperation(
                "管理員 {$user->name} 重置了系統設定為預設值",
                [
                    'action' => 'reset_defaults',
                    'admin_id' => $user->id,
                ]
            );

            return back()->with('success', '系統設定已重置為預設值');
        } catch (\Exception $e) {
            LogService::operationFailure(
                '重置系統設定失敗',
                ['error' => $e->getMessage()],
                LogConstants::LEVEL_ERROR,
                LogConstants::ACTION_SYSTEM_SETTING
            );

            return back()->withErrors([
                'system' => '重置系統設定失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 取得系統狀態
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSystemStatus()
    {
        try {
            $recruitmentService = app(\App\Services\RecruitmentPeriodService::class);

            $status = [
                'reminder_cooldown_hours' => SystemSetting::getReminderCooldownHours(),
                'auto_timeout_days' => SystemSetting::getAutoTimeoutDays(),
                'recruitment_periods' => [
                    'total_count' => count($recruitmentService->getAllPeriods()),
                    'active_count' => count($recruitmentService->getActiveRecruitmentPeriods()),
                    'last_sync' => $recruitmentService->getLastSyncTime()?->toISOString(),
                    'needs_resync' => $recruitmentService->needsResync()
                ],
                'current_time' => now()->toDateTimeString(),
            ];

            return response()->json([
                'success' => true,
                'data' => $status
            ]);
        } catch (\Exception $e) {
            LogService::operationFailure(
                '取得系統狀態失敗',
                ['error' => $e->getMessage()],
                LogConstants::LEVEL_ERROR,
                LogConstants::ACTION_VIEW
            );

            return response()->json([
                'success' => false,
                'message' => '取得系統狀態失敗'
            ], 500);
        }
    }

    /**
     * 切換維護模式
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleMaintenanceMode(Request $request)
    {
        try {
            // 如果請求中有 enabled 參數，使用該值；否則切換當前狀態
            if ($request->has('enabled')) {
                $newStatus = $request->boolean('enabled');
            } else {
                $currentStatus = SystemSetting::isMaintenanceMode();
                $newStatus = !$currentStatus;
            }

            $success = SystemSetting::setMaintenanceMode($newStatus);

            if (!$success) {
                return response()->json([
                    'success' => false,
                    'message' => '切換維護模式失敗'
                ], 500);
            }

            $user = Auth::user();
            $action = $newStatus ? '啟用' : '停用';
            LogService::securityOperation(
                "管理員 {$user->name} {$action}了系統維護模式",
                [
                    'previous_status' => !$newStatus,
                    'new_status' => $newStatus,
                    'admin_id' => $user->id,
                ]
            );

            return back()->with('success', '維護模式以切換成功')->with([
                'maintenance_mode' => $newStatus,
            ]);
        } catch (\Exception $e) {
            LogService::operationFailure(
                '切換維護模式失敗',
                ['error' => $e->getMessage()],
                LogConstants::LEVEL_ERROR,
                LogConstants::ACTION_SYSTEM_SETTING
            );

            return back()->withErrors([
                'system' => '切換維護模式失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 手動同步外部系統資料
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function syncExternalData()
    {
        try {
            $syncService = new \App\Services\ExternalApiSyncService();
            $result = $syncService->syncSystemSettings();

            $user = Auth::user();
            LogService::securityOperation(
                "管理員 {$user->name} 手動同步外部系統資料",
                [
                    'sync_result' => $result,
                    'admin_id' => $user->id,
                ]
            );

            return response()->json([
                'success' => $result['success'],
                'message' => $result['message'],
                'data' => $result['results'] ?? []
            ]);
        } catch (\Exception $e) {
            LogService::operationFailure(
                '手動同步外部系統資料失敗',
                ['error' => $e->getMessage()],
                LogConstants::LEVEL_ERROR,
                LogConstants::ACTION_SYSTEM_SETTING
            );

            return response()->json([
                'success' => false,
                'message' => '同步失敗，請稍後再試：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 檢查外部API連線狀態
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function checkApiConnection()
    {
        try {
            $syncService = new \App\Services\ExternalApiSyncService();
            $result = $syncService->checkApiConnection();

            return back()->with('api_status', $result);
        } catch (\Exception $e) {
            return back()->with('api_status', [
                'success' => false,
                'message' => '檢查API連線失敗：' . $e->getMessage()
            ]);
        }
    }
}
