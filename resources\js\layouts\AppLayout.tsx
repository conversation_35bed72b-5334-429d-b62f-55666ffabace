import AppHeaderFooterLayout from '@/layouts/app/AppHeaderFooterLayout';
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout';
import { type BreadcrumbItem, type SharedData } from '@/types';
import { usePage } from '@inertiajs/react';
import { type ReactNode } from 'react';

interface AppLayoutProps {
    children: ReactNode;
    breadcrumbs?: BreadcrumbItem[];
}

/**
 * 應用程式主要佈局元件
 * 根據使用者角色決定使用哪種佈局
 * - 管理員使用側邊欄佈局 (AppSidebarLayout)
 * - 考生與推薦人使用頁首頁尾佈局 (AppHeaderFooterLayout)
 */
export default ({ children, breadcrumbs, ...props }: AppLayoutProps) => {
    const { auth } = usePage<SharedData>().props;
    const userRole = auth?.user?.role;

    if (userRole === 'admin') {
        return (
            <AppSidebarLayout breadcrumbs={breadcrumbs} {...props}>
                {children}
            </AppSidebarLayout>
        );
    }

    return <AppHeaderFooterLayout {...props}>{children}</AppHeaderFooterLayout>;
};
