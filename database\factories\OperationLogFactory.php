<?php

namespace Database\Factories;

use App\Models\OperationLog;
use App\Models\User;
use App\Enums\LogConstants;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OperationLog>
 */
class OperationLogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = OperationLog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'route' => $this->faker->randomElement([
                'dashboard.index',
                'recommendations.index',
                'recommendations.create',
                'recommendations.show',
                'admin.users.index',
                'admin.settings.index'
            ]),
            'ip_address' => $this->faker->ipv4(),
            'user_agent' => $this->faker->userAgent(),
            'type' => $this->faker->randomElement([
                LogConstants::OPERATION_TYPE_OPERATION,
                LogConstants::OPERATION_TYPE_SYSTEM,
                LogConstants::OPERATION_TYPE_ERROR,
                LogConstants::OPERATION_TYPE_SECURITY
            ]),
            'description' => $this->faker->sentence(),
            'metadata' => [
                'action' => $this->faker->randomElement([
                    LogConstants::ACTION_VIEW,
                    LogConstants::ACTION_CREATE,
                    LogConstants::ACTION_UPDATE,
                    LogConstants::ACTION_DELETE
                ]),
                'timestamp' => now()->toISOString(),
                'additional_data' => $this->faker->words(3, true)
            ],
            'level' => $this->faker->randomElement([
                LogConstants::LEVEL_DEBUG,
                LogConstants::LEVEL_INFO,
                LogConstants::LEVEL_WARNING,
                LogConstants::LEVEL_ERROR,
                LogConstants::LEVEL_CRITICAL
            ]),
            'created_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the operation log is an error.
     */
    public function error(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => LogConstants::OPERATION_TYPE_ERROR,
            'level' => LogConstants::LEVEL_ERROR,
            'description' => $this->faker->sentence() . ' 錯誤',
        ]);
    }

    /**
     * Indicate that the operation log is a security operation.
     */
    public function security(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => LogConstants::OPERATION_TYPE_SECURITY,
            'level' => LogConstants::LEVEL_WARNING,
            'description' => '安全操作: ' . $this->faker->sentence(),
        ]);
    }

    /**
     * Indicate that the operation log is a normal operation.
     */
    public function operation(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => LogConstants::OPERATION_TYPE_OPERATION,
            'level' => LogConstants::LEVEL_INFO,
            'description' => '正常操作: ' . $this->faker->sentence(),
        ]);
    }

    /**
     * Indicate that the operation log is recent.
     */
    public function recent(int $hours = 24): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween("-{$hours} hours", 'now'),
        ]);
    }

    /**
     * Indicate that the operation log is old.
     */
    public function old(int $days = 30): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween("-{$days} days", "-1 day"),
        ]);
    }
}
