<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\RecommendationLetter;
use App\Models\Applicant;
use App\Services\LogService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * 推薦函API控制器
 *
 * 提供外部系統查詢推薦函資料的API端點
 */
class RecommendationApiController extends Controller
{
    /**
     * 根據考生資訊查詢推薦函狀態
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getRecommendationsByApplicant(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'exam_id' => 'required|string',
                'stu_year' => 'required|string',
                'stu_idno' => 'required|string', // 加密的學號
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '參數驗證失敗',
                    'errors' => $validator->errors()
                ], 422);
            }

            // 查找考生
            $applicant = Applicant::where('exam_id', $request->exam_id)
                ->where('stu_year', $request->stu_year)
                ->where('external_uid', $request->stu_idno)
                ->first();

            if (!$applicant) {
                return response()->json([
                    'success' => false,
                    'message' => '找不到對應的考生資料'
                ], 404);
            }

            // 查詢推薦函
            $recommendations = RecommendationLetter::where('applicant_id', $applicant->id)
                ->with(['recommender'])
                ->get()
                ->map(function ($recommendation) {
                    return [
                        'id' => $recommendation->id,
                        'external_autono' => $recommendation->external_autono,
                        'department_name' => $recommendation->department_name,
                        'program_type' => $recommendation->program_type,
                        'status' => $recommendation->status,
                        'recommender_name' => $recommendation->recommender_name,
                        'recommender_email' => $recommendation->recommender_email,
                        'submitted_at' => $recommendation->submitted_at?->format('Y-m-d H:i:s'),
                        'created_at' => $recommendation->created_at->format('Y-m-d H:i:s'),
                        'updated_at' => $recommendation->updated_at->format('Y-m-d H:i:s'),
                    ];
                });

            LogService::system(
                [
                    'endpoint' => 'api.recommendations.index',
                    'exam_id' => $request->exam_id,
                    'stu_year' => $request->stu_year,
                ],
                [
                    'applicant_id' => $applicant->id,
                    'recommendations_count' => $recommendations->count()
                ],
                200
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'applicant' => [
                        'id' => $applicant->id,
                        'exam_id' => $applicant->exam_id,
                        'stu_year' => $applicant->stu_year,
                        'name' => $applicant->user->name ?? null,
                        'email' => $applicant->user->email ?? null,
                    ],
                    'recommendations' => $recommendations
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('API查詢推薦函失敗', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            LogService::system(
                [
                    'endpoint' => 'api.recommendations.index',
                    'error' => $e->getMessage(),
                ],
                [],
                500
            );

            return response()->json([
                'success' => false,
                'message' => '查詢失敗，請稍後再試'
            ], 500);
        }
    }

    /**
     * 查詢考生(所有)的推薦函進度
     *
     * @param Request $request
     */
    public function getApplicantRecommendationStats(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'exam_id' => 'nullable|string',
                'exam_year' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '參數驗證失敗',
                    'errors' => $validator->errors()
                ], 422);
            }

            $examId   = $request->exam_id ?? '2';
            $examYear = $request->exam_year ?? '114';

            /**
             * 查詢邏輯:
             * - 根據 exam_id 和 exam_year 過濾推薦函，確保能正確查詢對應年度與類別下的所有考生推薦函狀態
             * - 使用 external_autono 作為分組依據，統計每個考生的推薦函申請數量和已完成數量
             * - 返回的資料格式為 CSV，包含考生的 external_autono(外部流水號)、申請數量、完成數量
             */
            $stats = RecommendationLetter::where('exam_id', $examId)
                ->where('exam_year', $examYear)
                ->selectRaw('external_autono as autono')
                ->selectRaw('COUNT(*) as inv_count')
                ->selectRaw("SUM(CASE WHEN status='submitted' THEN 1 ELSE 0 END) as cmp_count")
                ->groupBy('external_autono')
                ->get();

            LogService::system(
                [
                    'endpoint' => 'api.recommendations.statistics',
                    'filters' => $request->only(['exam_id', 'exam_year']),
                ],
                [
                    'total_count' => $stats->count()
                ],
                200
            );

            // 1. 定義檔名和 Headers
            $filename = "recommendation_stats_" . date('YmdHis') . ".csv";
            $headers = [
                'Content-Type'        => 'text/csv; charset=utf-8',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ];

            // 2. 使用 streamDownload 建立回應
            // Laravel 會處理 Headers，我們只需專注於如何寫入資料
            return response()->streamDownload(function () use ($stats) {

                // 開啟 PHP 的輸出流，直接寫入到 HTTP 回應中
                $handle = fopen('php://output', 'w');

                // (可選，但強烈建議) 寫入 BOM，確保 Excel 能正確開啟 UTF-8 CSV 檔案
                fwrite($handle, "\xEF\xBB\xBF");

                // 寫入 CSV 的標頭 (欄位名稱)
                fputcsv($handle, ['autono', 'inv_count', 'cmp_count']);

                // 遍歷從資料庫取出的資料
                foreach ($stats as $row) {
                    // 使用 fputcsv 寫入每一行，它會自動處理特殊字元
                    fputcsv($handle, [
                        $row->autono,
                        $row->inv_count,
                        $row->cmp_count,
                    ]);
                }

                // 關閉檔案流
                fclose($handle);
            }, $filename, $headers);
        } catch (\Exception $e) {
            Log::error('API查詢推薦函統計失敗', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            // 發生錯誤時，回傳一個標準的 HTTP 500 錯誤和 JSON 訊息
            return response()->json(['error' => '查詢統計失敗，請查看伺服器日誌。'], 500);
        }
    }

    /**
     * 健康檢查端點
     *
     * @return JsonResponse
     */
    public function health(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => '推薦函系統API運行正常',
            'timestamp' => now()->toISOString(),
            'version' => '1.0.0'
        ]);
    }

    /**
     * API資訊端點
     *
     * @return JsonResponse
     */
    public function info(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'system_name' => '推薦函管理系統',
                'api_version' => '1.0.0',
                'endpoints' => [
                    'GET /api/recommendations/by-applicant' => '根據考生資訊查詢推薦函',
                    'GET /api/recommendations/stats' => '查詢推薦函統計資料',
                    'GET /api/health' => '健康檢查',
                    'GET /api/info' => 'API資訊'
                ],
                'timestamp' => now()->toISOString()
            ]
        ]);
    }
}
