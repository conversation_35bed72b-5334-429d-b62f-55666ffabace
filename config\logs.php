<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Log 系統配置
    |--------------------------------------------------------------------------
    |
    | 此配置檔案包含了所有 Log 系統相關的配置選項
    |
    */

    /*
    |--------------------------------------------------------------------------
    | 日誌保留設定
    |--------------------------------------------------------------------------
    */
    'retention' => [
        'operation_logs' => env('LOG_RETENTION_OPERATION_DAYS', 730),  // 操作日誌保留天數
        'system_logs' => env('LOG_RETENTION_SYSTEM_DAYS', 365),       // 系統日誌保留天數
        'login_logs' => env('LOG_RETENTION_LOGIN_DAYS', 365),         // 登入日誌保留天數
        'email_logs' => env('LOG_RETENTION_EMAIL_DAYS', 180),         // 郵件日誌保留天數
    ],

    /*
    |--------------------------------------------------------------------------
    | 郵件日誌設定
    |--------------------------------------------------------------------------
    */
    'email' => [
        'retry_limit' => env('LOG_EMAIL_RETRY_LIMIT', 3),             // 郵件重試次數限制
        'retry_delay' => env('LOG_EMAIL_RETRY_DELAY', 300),           // 重試延遲時間(秒)
        'high_priority_types' => [                                    // 高優先級郵件類型
            'alert',
            'reset_password',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 安全設定
    |--------------------------------------------------------------------------
    */
    'security' => [
        'mask_sensitive_fields' => [                                               // 需要遮罩的敏感欄位
            'password',
            'token',
            'api_key',
            'secret',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 統計設定
    |--------------------------------------------------------------------------
    */
    'statistics' => [
        'default_period_days' => env('LOG_STATS_DEFAULT_PERIOD', 30),         // 預設統計期間(天)
        'cache_ttl' => env('LOG_STATS_CACHE_TTL', 3600),                      // 統計快取時間(秒)
        'enable_real_time' => env('LOG_STATS_REAL_TIME', false),              // 是否啟用即時統計
    ],

    /*
    |--------------------------------------------------------------------------
    | 清理設定
    |--------------------------------------------------------------------------
    */
    'cleanup' => [
        'enabled' => env('LOG_CLEANUP_ENABLED', true),                        // 是否啟用自動清理
        'schedule' => env('LOG_CLEANUP_SCHEDULE', 'daily'),                   // 清理排程
        'chunk_size' => env('LOG_CLEANUP_CHUNK_SIZE', 1000),                  // 清理批次大小
        'max_execution_time' => env('LOG_CLEANUP_MAX_TIME', 300),             // 最大執行時間(秒)
    ],

    /*
    |--------------------------------------------------------------------------
    | 通知設定
    |--------------------------------------------------------------------------
    */
    'notifications' => [
        'enabled' => env('LOG_NOTIFICATIONS_ENABLED', false),                 // 是否啟用通知
        'channels' => [                                                        // 通知管道
            'email' => env('LOG_NOTIFICATION_EMAIL', null),
            'slack' => env('LOG_NOTIFICATION_SLACK', null),
        ],
        'error_threshold' => env('LOG_ERROR_THRESHOLD', 10),                   // 錯誤通知閾值
        'critical_immediate' => env('LOG_CRITICAL_IMMEDIATE', true),           // 嚴重錯誤立即通知
    ],

    /*
    |--------------------------------------------------------------------------
    | 除錯設定
    |--------------------------------------------------------------------------
    */
    'debug' => [
        'enabled' => env('LOG_DEBUG_ENABLED', false),                         // 是否啟用除錯模式
        'log_queries' => env('LOG_DEBUG_QUERIES', false),                     // 是否記錄資料庫查詢
        'log_requests' => env('LOG_DEBUG_REQUESTS', false),                   // 是否記錄所有請求
        'verbose_errors' => env('LOG_DEBUG_VERBOSE_ERRORS', false),           // 是否詳細記錄錯誤
    ],

    /*
    |--------------------------------------------------------------------------
    | 壓縮設定
    |--------------------------------------------------------------------------
    */
    'compression' => [
        'enabled' => env('LOG_COMPRESSION_ENABLED', false),                   // 是否啟用壓縮
        'algorithm' => env('LOG_COMPRESSION_ALGORITHM', 'gzip'),              // 壓縮演算法
        'level' => env('LOG_COMPRESSION_LEVEL', 6),                           // 壓縮級別
        'older_than_days' => env('LOG_COMPRESSION_OLDER_THAN', 30),           // 壓縮多少天前的日誌
    ],

    /*
    |--------------------------------------------------------------------------
    | 匯出設定
    |--------------------------------------------------------------------------
    */
    'export' => [
        'formats' => ['csv', 'json', 'xlsx'],                                 // 支援的匯出格式
        'max_records' => env('LOG_EXPORT_MAX_RECORDS', 10000),                // 最大匯出記錄數
        'chunk_size' => env('LOG_EXPORT_CHUNK_SIZE', 1000),                   // 匯出批次大小
        'temp_path' => env('LOG_EXPORT_TEMP_PATH', storage_path('app/temp')), // 暫存路徑
    ],

    /*
    |--------------------------------------------------------------------------
    | 索引設定
    |--------------------------------------------------------------------------
    */
    'indexing' => [
        'auto_create' => env('LOG_AUTO_CREATE_INDEXES', true),                // 是否自動建立索引
        'optimize_frequency' => env('LOG_OPTIMIZE_FREQUENCY', 'weekly'),      // 索引優化頻率
    ],

];
