<?php

namespace Database\Factories;

use App\Models\SystemLog;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SystemLog>
 */
class SystemLogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SystemLog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => $this->faker->optional()->randomElement([
                null,
                User::factory()
            ]),
            'method' => $this->faker->randomElement(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']),
            'route' => $this->faker->randomElement([
                'api.recommendations.index',
                'api.recommendations.store',
                'api.users.show',
                'api.admin.settings',
                'web.dashboard',
                'web.login'
            ]),
            'request_payload' => [
                'endpoint' => $this->faker->url(),
                'parameters' => $this->faker->words(3, true),
                'user_agent' => $this->faker->userAgent()
            ],
            'response_payload' => [
                'status' => 'success',
                'data' => $this->faker->words(5, true),
                'timestamp' => now()->toISOString()
            ],
            'status_code' => $this->faker->randomElement([200, 201, 400, 401, 403, 404, 422, 500, 502, 503]),
            'created_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the system log is successful.
     */
    public function successful(): static
    {
        return $this->state(fn (array $attributes) => [
            'status_code' => $this->faker->randomElement([200, 201, 202, 204]),
            'response_payload' => [
                'status' => 'success',
                'message' => 'Request completed successfully',
                'timestamp' => now()->toISOString()
            ]
        ]);
    }

    /**
     * Indicate that the system log is a client error.
     */
    public function clientError(): static
    {
        return $this->state(fn (array $attributes) => [
            'status_code' => $this->faker->randomElement([400, 401, 403, 404, 422]),
            'response_payload' => [
                'status' => 'error',
                'message' => 'Client error occurred',
                'error' => $this->faker->sentence()
            ]
        ]);
    }

    /**
     * Indicate that the system log is a server error.
     */
    public function serverError(): static
    {
        return $this->state(fn (array $attributes) => [
            'status_code' => $this->faker->randomElement([500, 502, 503, 504]),
            'response_payload' => [
                'status' => 'error',
                'message' => 'Server error occurred',
                'error' => $this->faker->sentence()
            ]
        ]);
    }

    /**
     * Indicate that the system log is for GET method.
     */
    public function get(): static
    {
        return $this->state(fn (array $attributes) => [
            'method' => 'GET',
            'request_payload' => [
                'query_params' => $this->faker->words(3, true)
            ]
        ]);
    }

    /**
     * Indicate that the system log is for POST method.
     */
    public function post(): static
    {
        return $this->state(fn (array $attributes) => [
            'method' => 'POST',
            'request_payload' => [
                'body' => $this->faker->words(5, true),
                'content_type' => 'application/json'
            ]
        ]);
    }

    /**
     * Indicate that the system log is recent.
     */
    public function recent(int $hours = 24): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween("-{$hours} hours", 'now'),
        ]);
    }
}
