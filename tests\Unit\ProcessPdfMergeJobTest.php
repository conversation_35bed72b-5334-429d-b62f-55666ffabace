<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Jobs\ProcessPdfMergeJob;
use App\Models\PdfMergeTask;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ProcessPdfMergeJobTest extends TestCase
{
    use RefreshDatabase;

    /**
     * 測試對象格式數據的處理
     */
    public function test_handles_object_format_recommendations()
    {
        // 創建測試任務
        $task = PdfMergeTask::create([
            'task_id' => 'test_task_123',
            'status' => PdfMergeTask::STATUS_PROCESSING,
            'progress' => 0,
            'parameters' => [
                'test_mode' => 'large_zip',
                'test_file_count' => 1,
                'exam_id' => 'TEST2024',
                'exam_year' => 113
            ],
            'expires_at' => now()->addHours(24),
        ]);

        // 創建Job實例
        $job = new ProcessPdfMergeJob($task->task_id, $task->parameters);

        // 使用反射來測試私有方法
        $reflection = new \ReflectionClass($job);

        // 測試 generateTestRecommendations 方法
        $generateMethod = $reflection->getMethod('generateTestRecommendations');
        $generateMethod->setAccessible(true);

        $testRecommendations = $generateMethod->invoke($job);

        // 驗證生成的數據格式
        $this->assertNotEmpty($testRecommendations);
        $this->assertEquals(1, $testRecommendations->count());

        // 測試 groupRecommendationsByExternalAutono 方法
        $groupMethod = $reflection->getMethod('groupRecommendationsByExternalAutono');
        $groupMethod->setAccessible(true);

        $grouped = $groupMethod->invoke($job, $testRecommendations);

        // 驗證分組結果
        $this->assertIsArray($grouped);
        $this->assertNotEmpty($grouped);

        // 測試 prepareApplicantFiles 方法
        $prepareMethod = $reflection->getMethod('prepareApplicantFiles');
        $prepareMethod->setAccessible(true);

        // 取得第一組數據進行測試
        $firstGroup = array_values($grouped)[0];
        $files = $prepareMethod->invoke($job, $firstGroup);

        // 驗證檔案準備結果
        $this->assertIsArray($files);

        // 清理測試任務
        $task->delete();
    }

    /**
     * 測試數組格式數據的處理（向後兼容）
     */
    public function test_handles_array_format_recommendations()
    {
        // 創建測試任務
        $task = PdfMergeTask::create([
            'task_id' => 'test_task_456',
            'status' => PdfMergeTask::STATUS_PROCESSING,
            'progress' => 0,
            'parameters' => [
                'exam_id' => 'TEST2024',
                'exam_year' => 113
            ],
            'expires_at' => now()->addHours(24),
        ]);

        // 創建Job實例
        $job = new ProcessPdfMergeJob($task->task_id, $task->parameters);

        // 創建數組格式的測試數據
        $arrayRecommendations = collect([
            [
                'id' => 'rec_1',
                'applicant_id' => 'app_1',
                'external_autono' => 'autono_1',
                'pdf_path' => 'test_pdfs/test1.pdf',
                'status' => 'submitted',
                'applicant' => [
                    'id' => 'app_1',
                    'name' => '測試考生1'
                ],
                'recommender' => [
                    'id' => 'recommender_1',
                    'name' => '測試推薦人1'
                ]
            ]
        ]);

        // 使用反射來測試私有方法
        $reflection = new \ReflectionClass($job);

        // 測試 groupRecommendationsByExternalAutono 方法
        $groupMethod = $reflection->getMethod('groupRecommendationsByExternalAutono');
        $groupMethod->setAccessible(true);

        $grouped = $groupMethod->invoke($job, $arrayRecommendations);

        // 驗證分組結果
        $this->assertIsArray($grouped);
        $this->assertArrayHasKey('autono_1', $grouped);

        // 清理測試任務
        $task->delete();
    }
}
