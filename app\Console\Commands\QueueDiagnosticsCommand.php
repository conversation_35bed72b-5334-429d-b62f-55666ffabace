<?php

namespace App\Console\Commands;

use App\Models\PdfMergeTask;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;

/**
 * Queue 診斷指令
 *
 * 用於檢查 Queue 系統狀態和診斷問題
 */
class QueueDiagnosticsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'queue:diagnostics 
                            {--monitor : 持續監控模式}
                            {--clear-failed : 清理失敗的任務}
                            {--test : 執行測試任務}';

    /**
     * The console command description.
     */
    protected $description = 'Queue 系統診斷和監控工具';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Queue 系統診斷 ===');

        if ($this->option('clear-failed')) {
            return $this->clearFailedJobs();
        }

        if ($this->option('test')) {
            return $this->runTestJob();
        }

        if ($this->option('monitor')) {
            return $this->monitorQueue();
        }

        // 預設執行完整診斷
        $this->runDiagnostics();
    }

    /**
     * 執行完整診斷
     */
    protected function runDiagnostics(): void
    {
        $this->checkQueueConfiguration();
        $this->checkDatabaseTables();
        $this->checkQueueStatus();
        $this->checkPdfMergeTasks();
        $this->checkSystemResources();
        $this->showRecommendations();
    }

    /**
     * 檢查 Queue 配置
     */
    protected function checkQueueConfiguration(): void
    {
        $this->info('');
        $this->info('📋 檢查 Queue 配置');

        $connection = config('queue.default');
        $this->line("預設連接: {$connection}");

        $driver = config("queue.connections.{$connection}.driver");
        $this->line("驅動程式: {$driver}");

        if ($driver === 'database') {
            $table = config("queue.connections.{$connection}.table", 'jobs');
            $this->line("資料表: {$table}");

            $retryAfter = config("queue.connections.{$connection}.retry_after", 90);
            $this->line("重試間隔: {$retryAfter} 秒");
        }

        // 檢查環境變數
        $envConnection = env('QUEUE_CONNECTION');
        if ($envConnection !== $connection) {
            $this->warn("⚠️  環境變數 QUEUE_CONNECTION ({$envConnection}) 與配置不符 ({$connection})");
        } else {
            $this->info("✅ Queue 配置正常");
        }
    }

    /**
     * 檢查資料庫表
     */
    protected function checkDatabaseTables(): void
    {
        $this->info('');
        $this->info('🗄️ 檢查資料庫表');

        try {
            // 檢查 jobs 表
            $jobsCount = DB::table('jobs')->count();
            $this->line("待處理任務: {$jobsCount}");

            // 檢查 failed_jobs 表
            $failedCount = DB::table('failed_jobs')->count();
            $this->line("失敗任務: {$failedCount}");

            // 檢查 pdf_merge_tasks 表
            $mergeTasksCount = PdfMergeTask::count();
            $this->line("PDF合併任務總數: {$mergeTasksCount}");

            $processingCount = PdfMergeTask::where('status', 'processing')->count();
            $this->line("處理中任務: {$processingCount}");

            $readyCount = PdfMergeTask::where('status', 'ready')->count();
            $this->line("已完成任務: {$readyCount}");

            $failedMergeCount = PdfMergeTask::where('status', 'failed')->count();
            $this->line("失敗的合併任務: {$failedMergeCount}");

            $this->info("✅ 資料庫表檢查完成");
        } catch (\Exception $e) {
            $this->error("❌ 資料庫表檢查失敗: " . $e->getMessage());
        }
    }

    /**
     * 檢查 Queue 狀態
     */
    protected function checkQueueStatus(): void
    {
        $this->info('');
        $this->info('⚡ 檢查 Queue 狀態');

        try {
            // 檢查最近的任務
            $recentJobs = DB::table('jobs')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();

            if ($recentJobs->isEmpty()) {
                $this->line("沒有待處理的任務");
            } else {
                $this->line("最近的任務:");
                foreach ($recentJobs as $job) {
                    $payload = json_decode($job->payload, true);
                    $jobClass = $payload['displayName'] ?? 'Unknown';
                    $attempts = $job->attempts;
                    $createdAt = date('Y-m-d H:i:s', $job->created_at);
                    $this->line("  - {$jobClass} (嘗試: {$attempts}, 建立: {$createdAt})");
                }
            }

            // 檢查失敗的任務
            $recentFailed = DB::table('failed_jobs')
                ->orderBy('failed_at', 'desc')
                ->limit(3)
                ->get();

            if (!$recentFailed->isEmpty()) {
                $this->warn("最近失敗的任務:");
                foreach ($recentFailed as $failed) {
                    $payload = json_decode($failed->payload, true);
                    $jobClass = $payload['displayName'] ?? 'Unknown';
                    $failedAt = $failed->failed_at;
                    $this->warn("  - {$jobClass} (失敗時間: {$failedAt})");
                }
            }
        } catch (\Exception $e) {
            $this->error("❌ Queue 狀態檢查失敗: " . $e->getMessage());
        }
    }

    /**
     * 檢查 PDF 合併任務
     */
    protected function checkPdfMergeTasks(): void
    {
        $this->info('');
        $this->info('📄 檢查 PDF 合併任務');

        try {
            // 檢查長時間處理中的任務
            $stuckTasks = PdfMergeTask::where('status', 'processing')
                ->where('created_at', '<', now()->subMinutes(30))
                ->get();

            if ($stuckTasks->isNotEmpty()) {
                $this->warn("⚠️  發現可能卡住的任務:");
                foreach ($stuckTasks as $task) {
                    $duration = $task->created_at->diffInMinutes(now());
                    $this->warn("  - 任務 {$task->task_id} (處理中 {$duration} 分鐘)");
                }
            } else {
                $this->info("✅ 沒有發現卡住的任務");
            }

            // 檢查最近完成的任務
            $recentCompleted = PdfMergeTask::where('status', 'ready')
                ->orderBy('updated_at', 'desc')
                ->limit(3)
                ->get();

            if ($recentCompleted->isNotEmpty()) {
                $this->info("最近完成的任務:");
                foreach ($recentCompleted as $task) {
                    $completedAt = $task->updated_at->format('Y-m-d H:i:s');
                    $this->line("  - 任務 {$task->task_id} (完成: {$completedAt})");
                }
            }
        } catch (\Exception $e) {
            $this->error("❌ PDF 合併任務檢查失敗: " . $e->getMessage());
        }
    }

    /**
     * 檢查系統資源
     */
    protected function checkSystemResources(): void
    {
        $this->info('');
        $this->info('💻 檢查系統資源');

        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        $memoryLimit = ini_get('memory_limit');

        $this->line("記憶體使用: " . $this->formatBytes($memoryUsage));
        $this->line("記憶體峰值: " . $this->formatBytes($memoryPeak));
        $this->line("記憶體限制: {$memoryLimit}");

        $maxExecutionTime = ini_get('max_execution_time');
        $this->line("最大執行時間: {$maxExecutionTime} 秒");

        // 檢查磁碟空間
        $storageSpace = disk_free_space(storage_path());
        $this->line("可用磁碟空間: " . $this->formatBytes($storageSpace));
    }

    /**
     * 顯示建議
     */
    protected function showRecommendations(): void
    {
        $this->info('');
        $this->info('💡 建議');

        $this->line("1. 確保 Queue Worker 正在運行:");
        $this->line("   php artisan queue:work --tries=3 --timeout=1800");
        $this->line("");
        $this->line("2. 監控 Queue 狀態:");
        $this->line("   php artisan queue:diagnostics --monitor");
        $this->line("");
        $this->line("3. 清理失敗的任務:");
        $this->line("   php artisan queue:diagnostics --clear-failed");
        $this->line("");
        $this->line("4. 測試 Queue 功能:");
        $this->line("   php artisan queue:diagnostics --test");
    }

    /**
     * 持續監控模式
     */
    protected function monitorQueue(): int
    {
        $this->info('🔍 開始監控 Queue (按 Ctrl+C 停止)');

        while (true) {
            $this->line('');
            $this->line('=== ' . now()->format('Y-m-d H:i:s') . ' ===');

            $jobsCount = DB::table('jobs')->count();
            $failedCount = DB::table('failed_jobs')->count();
            $processingCount = PdfMergeTask::where('status', 'processing')->count();

            $this->line("待處理任務: {$jobsCount}");
            $this->line("失敗任務: {$failedCount}");
            $this->line("處理中的PDF任務: {$processingCount}");

            sleep(10);
        }

        return Command::SUCCESS;
    }

    /**
     * 清理失敗的任務
     */
    protected function clearFailedJobs(): int
    {
        $this->info('🧹 清理失敗的任務');

        $count = DB::table('failed_jobs')->count();

        if ($count === 0) {
            $this->info('沒有失敗的任務需要清理');
            return Command::SUCCESS;
        }

        if ($this->confirm("確定要清理 {$count} 個失敗的任務嗎?")) {
            DB::table('failed_jobs')->delete();
            $this->info("✅ 已清理 {$count} 個失敗的任務");
        }

        return Command::SUCCESS;
    }

    /**
     * 執行測試任務
     */
    protected function runTestJob(): int
    {
        $this->info('🧪 執行測試任務');

        // 這裡可以添加一個簡單的測試 Job
        $this->line('測試功能尚未實現');

        return Command::SUCCESS;
    }

    /**
     * 格式化位元組
     */
    protected function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
