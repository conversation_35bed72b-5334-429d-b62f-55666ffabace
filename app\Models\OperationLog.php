<?php

namespace App\Models;

use App\Enums\LogConstants;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

/**
 * 操作日誌模型
 *
 * 記錄使用者(考生、推薦人、管理員)的操作行為相關日誌
 */
class OperationLog extends Model
{
    use HasFactory;

    /**
     * 資料表名稱
     */
    protected $table = 'operation_logs';

    /**
     * 可批量賦值的屬性
     */
    protected $fillable = [
      'type',
      'route',
      'description',
      'user_id',
      'ip_address',
      'user_agent',
      'metadata',
      'level',
    ];

    /**
     * 屬性類型轉換
     */
    protected $casts = [
      'metadata' => 'array',
    ];

    /**
     * 日誌類型常數 (使用統一常數管理)
     */
    public const TYPE_OPERATION = LogConstants::OPERATION_TYPE_OPERATION;
    public const TYPE_SYSTEM = LogConstants::OPERATION_TYPE_SYSTEM;
    public const TYPE_ERROR = LogConstants::OPERATION_TYPE_ERROR;
    public const TYPE_SECURITY = LogConstants::OPERATION_TYPE_SECURITY;

    /**
     * 日誌級別常數 (使用統一常數管理)
     */
    public const LEVEL_DEBUG = LogConstants::LEVEL_DEBUG;
    public const LEVEL_INFO = LogConstants::LEVEL_INFO;
    public const LEVEL_WARNING = LogConstants::LEVEL_WARNING;
    public const LEVEL_ERROR = LogConstants::LEVEL_ERROR;
    public const LEVEL_CRITICAL = LogConstants::LEVEL_CRITICAL;

    /**
     * 操作動作常數 (使用統一常數管理)
     */
    public const ACTION_VIEW = LogConstants::ACTION_VIEW;
    public const ACTION_CREATE = LogConstants::ACTION_CREATE;
    public const ACTION_UPDATE = LogConstants::ACTION_UPDATE;
    public const ACTION_DELETE = LogConstants::ACTION_DELETE;
    public const ACTION_UPLOAD = LogConstants::ACTION_UPLOAD;
    public const ACTION_DOWNLOAD = LogConstants::ACTION_DOWNLOAD;

    /**
     * 取得關聯的用戶
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    #region: 查詢範圍 (Scopes)

    /**
     * 範圍查詢：按類型
     */
    public function scopeOfType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * 範圍查詢：按級別
     */
    public function scopeOfLevel(Builder $query, string $level): Builder
    {
        return $query->where('level', $level);
    }

    /**
     * 範圍查詢：錯誤級別
     */
    public function scopeErrors(Builder $query): Builder
    {
        return $query->whereIn('level', [self::LEVEL_ERROR, self::LEVEL_CRITICAL]);
    }

    /**
     * 範圍查詢：警告級別
     */
    public function scopeWarnings(Builder $query): Builder
    {
        return $query->where('level', self::LEVEL_WARNING);
    }

    /**
     * 範圍查詢：按用戶
     */
    public function scopeForUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 範圍查詢：最近的記錄
     */
    public function scopeRecent(Builder $query, int $days = 7): Builder
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * 範圍查詢：按路由
     */
    public function scopeForRoute(Builder $query, string $route): Builder
    {
        return $query->where('route', $route);
    }

    /**
     * 範圍查詢：按IP地址
     */
    public function scopeFromIp(Builder $query, string $ip): Builder
    {
        return $query->where('ip_address', $ip);
    }

    /**
     * 範圍查詢：今日記錄
     */
    public function scopeToday(Builder $query): Builder
    {
        return $query->whereDate('created_at', today());
    }

    #endregion

    #region: 統計方法

    /**
     * 取得類型統計
     */
    public static function getTypeStatistics(int $days = 30): array
    {
        return self::where('created_at', '>=', now()->subDays($days))
          ->selectRaw('type, COUNT(*) as count')
          ->groupBy('type')
          ->pluck('count', 'type')
          ->toArray();
    }

    /**
     * 取得級別統計
     */
    public static function getLevelStatistics(int $days = 30): array
    {
        return self::where('created_at', '>=', now()->subDays($days))
          ->selectRaw('level, COUNT(*) as count')
          ->groupBy('level')
          ->pluck('count', 'level')
          ->toArray();
    }

    /**
     * 取得用戶活動統計
     */
    public static function getUserActivityStats(int $days = 30): array
    {
        return self::where('created_at', '>=', now()->subDays($days))
          ->whereNotNull('user_id')
          ->with('user:id,name,email')
          ->selectRaw('user_id, COUNT(*) as count')
          ->groupBy('user_id')
          ->orderByDesc('count')
          ->limit(10)
          ->get()
          ->toArray();
    }

    /**
     * 取得每日統計
     */
    public static function getDailyStats(int $days = 30): array
    {
        return self::where('created_at', '>=', now()->subDays($days))
          ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
          ->groupBy('date')
          ->orderBy('date')
          ->pluck('count', 'date')
          ->toArray();
    }

    /**
     * 取得路由統計
     */
    public static function getRouteStats(int $days = 30, int $limit = 10): array
    {
        return self::where('created_at', '>=', now()->subDays($days))
          ->selectRaw('route, COUNT(*) as count')
          ->groupBy('route')
          ->orderByDesc('count')
          ->limit($limit)
          ->pluck('count', 'route')
          ->toArray();
    }

    #endregion

    #region: 輔助方法

    /**
     * 檢查是否為錯誤級別
     */
    public function isError(): bool
    {
        return in_array($this->level, [self::LEVEL_ERROR, self::LEVEL_CRITICAL]);
    }

    /**
     * 檢查是否為警告級別
     */
    public function isWarning(): bool
    {
        return $this->level === self::LEVEL_WARNING;
    }

    /**
     * 取得級別的中文名稱
     */
    public function getLevelNameAttribute(): string
    {
        return LogConstants::getLogLevels()[$this->level] ?? $this->level;
    }

    /**
     * 取得類型的中文名稱
     */
    public function getTypeNameAttribute(): string
    {
        return LogConstants::getOperationTypes()[$this->type] ?? $this->type;
    }

    #endregion
}
