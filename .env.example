APP_NAME="推薦函系統（測試）"
APP_ENV=local # 環境類型: local(開發), testing(測試), staging(預發布), production(正式)
APP_KEY= # 應用程式加密金鑰，使用 php artisan key:generate 生成
APP_DEBUG=true # 除錯模式，正式環境請設為 false
APP_URL=http://localhost # 應用程式網址，正式環境請設為實際網域

APP_LOCALE=zh-TW 
APP_FALLBACK_LOCALE=en 
APP_FAKER_LOCALE=en_US
APP_TIMEZONE=Asia/Taipei

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database
PHP_CLI_SERVER_WORKERS=4
BCRYPT_ROUNDS=12

# ==============================================================================
# Log 設定
# ==============================================================================
LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug # 開發階段使用 debug 等級，正式環境可改為 info 或 warning，避免過多日誌輸出

# ==============================================================================
# 資料庫設定
# ==============================================================================
DB_CONNECTION=mysql # 資料庫類型，支援 mysql, pgsql, sqlite, sqlsrv
DB_HOST=127.0.0.1 # 資料庫主機位址
DB_PORT=3306 # 資料庫連接埠，MySQL 預設 3306
DB_DATABASE=recommendation_system # 資料庫名稱
DB_USERNAME=root # 資料庫使用者名稱
DB_PASSWORD= # 資料庫密碼
DB_CHARSET=utf8mb4 # 資料庫字元集，建議使用 utf8mb4 支援完整 Unicode
DB_COLLATION=utf8mb4_unicode_ci # 資料庫排序規則

# ==============================================================================
# Session 設定
# ==============================================================================
SESSION_DRIVER=database # Session 儲存方式: file, cookie, database, redis
SESSION_LIFETIME=120 # Session 有效時間(分鐘)，120分鐘 = 2小時
SESSION_ENCRYPT=false # 是否加密 Session 資料
SESSION_PATH=/ # Session Cookie 路徑
SESSION_DOMAIN=null # Session Cookie 網域，null 表示使用當前網域
SESSION_SECURE=false # 是否只在 HTTPS 下傳送 Session Cookie，正式環境建議設為 true
SESSION_HTTP_ONLY=true # 是否只允許 HTTP 存取 Session Cookie，防止 XSS 攻擊
SESSION_SAME_SITE=lax # SameSite 設定: lax, strict, none

# ==============================================================================
# 快取與佇列設定
# ==============================================================================
BROADCAST_CONNECTION=log # 廣播驅動: log, redis, pusher
FILESYSTEM_DISK=local # 檔案系統磁碟: local, public, s3
QUEUE_CONNECTION=database # 佇列驅動: sync, database, redis, sqs
# 使用 database 連接，方便管理和監控(需透過 php artisan queue:work 啟動工作隊列)

CACHE_STORE=database # 快取驅動: file, database, redis, memcached
CACHE_PREFIX= # 快取前綴，避免與其他應用程式衝突

# ==============================================================================
# 前端建置設定 (Vite)
# ==============================================================================
VITE_APP_NAME="${APP_NAME}" # 前端應用程式名稱
VITE_APP_ENV="${APP_ENV}" # 前端環境變數
VITE_APP_URL="${APP_URL}" # 前端應用程式網址

# ==============================================================================
# 推薦函系統設定(開發中)
# ==============================================================================
# PDF設定(用於問卷轉換成PDF的相關設定)
PDF_ENGINE=dompdf # 使用 dompdf 作為 PDF 引擎
PDF_PAPER_SIZE=A4  # 預設紙張大小為 A4，可選擇 A3, Letter 等
PDF_ORIENTATION=portrait # 預設為直式，橫式可改為 landscape
PDF_DPI=96 # 預設解析度為 96 DPI
PDF_DEFAULT_FONT="DejaVu Sans" # 預設字體為 DejaVu Sans，支援多語系
PDF_CHINESE_FONT="Microsoft JhengHei" # 中文字體設定，使用微軟正黑體
PDF_STORAGE_DISK=local # PDF 檔案儲存磁碟，使用 local 磁碟
PDF_STORAGE_PATH=recommendations # PDF 檔案儲存路徑
PDF_MAX_SIZE=10240 # PDF 檔案最大大小，單位為 KB

# ==============================================================================
# 檔案上傳設定
# ==============================================================================
UPLOAD_STORAGE_DISK=local # 上傳檔案儲存磁碟
UPLOAD_STORAGE_PATH=uploads # 上傳檔案儲存路徑
UPLOAD_IMAGE_OPTIMIZATION=true # 是否啟用圖片優化
UPLOAD_ALLOWED_TYPES=pdf
UPLOAD_MAX_SIZE=5096 # 單位: KB，預設 5MB
UPLOAD_STORAGE_PATH=recommendations

# ==============================================================================
# 外部郵件 API 設定(使用此設定發送郵件)
# ==============================================================================
MAIL_EXTERNAL_API_ENABLED=true # 是否啟用外部郵件 API
MAIL_EXTERNAL_API_URL= # 外部郵件 API 網址，例如: http://example.com/sendmail.php
MAIL_EXTERNAL_API_ACCOUNT= # 外部郵件 API 帳號
MAIL_EXTERNAL_API_PASSWORD= # 外部郵件 API 密碼
MAIL_EXTERNAL_API_REPLY_TO="${MAIL_FROM_ADDRESS}" # 回覆信箱
MAIL_EXTERNAL_API_TIMEOUT=30 # API 請求超時時間 (秒)
MAIL_EXTERNAL_API_RETRY_TIMES=3 # API 請求重試次數
MAIL_EXTERNAL_API_SSL_VERIFY=true # 是否驗證 SSL 憑證

# ==============================================================================
# 外部系統 API 設定
# - BASE_URL: 外部系統 API 端點
# - API_IP: 伺服器 IP (用於 IP 白名單驗證)
# - API_KEY: 外部系統 API 金鑰
# ==============================================================================

# EAPAPI 系統設定(用於向 eapapi 系統請求資料)
EAPAPI_BASE_URL=https://EAPAPI網域/index.php/api/v1/recommendation_system
EAPAPI_API_KEY=

# EXAM 系統設定(用於向 exam 系統請求資料)
EXAM_BASE_URL=https://EXAM網域/index.php
EXAM_API_KEY= 

# API 認證設定，分別對兩個外部系統的IP作白名單設置，並以API_SECRET作為額外的安全驗證
EAPAPI_API_IP= 
EXAM_API_IP=
API_SECRET=

# ==============================================================================
# 部署檢查清單 (上線前必須完成)
# ==============================================================================

# 🔐 安全性檢查
# 1. 重新產生應用程式金鑰: php artisan key:generate
# 2. 設定強密碼的資料庫密碼 (DB_PASSWORD)
# 3. 設定安全的 API 密鑰 (API_SECRET, EAPAPI_API_KEY, EXAM_API_KEY)
# 4. 設定正確的 IP 白名單 (EAPAPI_API_IP, EXAM_API_IP)
# 5. 確認 APP_DEBUG=false (正式環境)
# 6. 確認 APP_ENV=production (正式環境)
