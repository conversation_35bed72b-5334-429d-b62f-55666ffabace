<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\QuestionnaireTemplate;
use App\Models\RecommendationLetter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class QuestionnaireController extends Controller
{
    /**
     * 顯示問卷模板管理頁面
     */
    public function index()
    {
        $templates = QuestionnaireTemplate::orderBy('created_at', 'desc')->get();

        // Ensure questions is parsed as array for each template
        $templates->transform(function ($template) {
            if (is_string($template->questions)) {
                $template->questions = json_decode($template->questions, true);
            }
            return $template;
        });

        return Inertia::render('admin/QuestionnaireTemplates', [
            'templates' => $templates,
        ]);
    }

    /**
     * 取得推薦函的問卷模板
     */
    public function getTemplate($recommendationId)
    {
        $user = Auth::user();

        $recommendation = RecommendationLetter::where('id', $recommendationId)
            ->where('recommender_email', $user->email)
            ->first();

        if (!$recommendation) {
            abort(404, '推薦函不存在或您無權限查看');
        }

        // 獲取推薦函相關的問卷模板
        $template = QuestionnaireTemplate::getTemplate(
            $recommendation->department_name,
            $recommendation->program_type
        );

        // 如果沒有找到模板，則使用預設模板
        if (!$template) {
            $template = QuestionnaireTemplate::getDefaultTemplate();
        }

        // 如果模板不存在，則返回空模板
        if (!$template) {
            return response()->json([
                'template' => null,
                'recommendation' => $recommendation,
            ]);
        }

        // 確保問題是陣列格式
        if ($template && is_string($template->questions)) {
            $template->questions = json_decode($template->questions, true);
        }

        return response()->json([
            'template' => $template, // 返回問卷模板
            'recommendation' => $recommendation, // 返回推薦函資料
        ]);
    }

    /**
     * 上傳 CSV 檔案並創建問卷模板
     */
    public function uploadCsvTemplate(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt',
            'department_name' => 'required|string',
            'program_type' => 'required|string',
        ]);

        $file = $request->file('csv_file');
        $csvData = $this->parseCsvFile($file);

        if (empty($csvData)) {
            return back()->withErrors(['csv_file' => 'CSV 檔案格式錯誤或為空']);
        }

        $template = QuestionnaireTemplate::createFromCsv(
            $request->department_name,
            $request->program_type,
            $csvData
        );

        return back()->with([
            'success' => '問卷模板已成功創建',
            'template' => $template,
        ]);
    }

    /**
     * 儲存問卷模板
     */
    public function saveTemplate(Request $request)
    {
        $request->validate([
            'department_name' => 'required|string',
            'program_type' => 'required|string',
            'template_name' => 'required|string',
            'questions' => 'required|array',
            'questions.*.question' => 'required|string',
            'questions.*.type' => 'required|string|in:text,textarea,select,radio,checkbox',
        ]);

        // Deactivate existing templates for this department/program
        QuestionnaireTemplate::where('department_name', $request->department_name)
            ->where('program_type', $request->program_type)
            ->update(['is_active' => false]);

        QuestionnaireTemplate::create([
            'department_name' => $request->department_name,
            'program_type' => $request->program_type,
            'template_name' => $request->template_name,
            'questions' => $request->questions,
            'is_active' => true,
        ]);

        return redirect()->route('questionnaire.index')->with('success', '問卷模板已保存');
    }

    /**
     * 刪除問卷模板
     */
    public function destroy($id)
    {
        try {
            $template = QuestionnaireTemplate::findOrFail($id);
            $template->update(['is_active' => false]);

            return back()->with('success', '問卷模板已刪除');
        } catch (\Exception $e) {
            return back()->withErrors([
                'delete' => '刪除問卷模板失敗：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 解析 CSV 檔案並轉換為陣列格式
     */
    private function parseCsvFile($file): array
    {
        $csvData = [];
        $handle = fopen($file->getPathname(), 'r');

        if ($handle === false) {
            return [];
        }

        // Read header row
        $headers = fgetcsv($handle);
        if (!$headers) {
            fclose($handle);
            return [];
        }

        // Read data rows
        while (($row = fgetcsv($handle)) !== false) {
            if (count($row) === count($headers)) {
                $csvData[] = array_combine($headers, $row);
            }
        }

        fclose($handle);
        return $csvData;
    }
}
